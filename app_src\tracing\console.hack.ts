// import { format, inspect, formatWithOptions } from 'util'
// import * as asyncHooks from 'async_hooks'
// const { executionAsyncId } = asyncHooks
// import { contexts } from './async_hook'

export default function() {}
// function console_hack() {
//   //@ts-ignore
//   const log: typeof console = {}

//   log.debug = console.debug
//   log.log = console.log
//   log.info = console.info
//   log.dir = console.dir
//   log.warn = console.warn
//   log.error = console.error

//   const logger = function(level: string, message?: any, ...optionalParams: any[]) {
//     var context = contexts[executionAsyncId()] || {}
//     var str = inspect(
//       {
//         level: level,
//         context: JSON.stringify(context),
//         message: [message, ...optionalParams]
//       },
//       { depth: Infinity }
//     )
//     log.log(str.replace(/\n/g, ''))
//   }

//   console.debug = function(message?: any, ...optionalParams: any[]) {
//     var context = contexts[executionAsyncId()]
//     if (!context) {
//       return log.debug(message, ...optionalParams)
//     }
//     return logger('debug', message, ...optionalParams)
//   }

//   console.log = function(message?: any, ...optionalParams: any[]) {
//     var context = contexts[executionAsyncId()]
//     if (!context) {
//       return log.log(message, ...optionalParams)
//     }
//     return logger('log', message, ...optionalParams)
//   }

//   console.info = function(message?: any, ...optionalParams: any[]) {
//     var context = contexts[executionAsyncId()]
//     if (!context) {
//       return log.info(message, ...optionalParams)
//     }
//     return logger('info', message, ...optionalParams)
//   }

//   console.warn = function(message?: any, ...optionalParams: any[]) {
//     var context = contexts[executionAsyncId()]
//     if (!context) {
//       return log.warn(message, ...optionalParams)
//     }
//     return logger('warn', message, ...optionalParams)
//   }

//   console.error = function(message?: any, ...optionalParams: any[]) {
//     var context = contexts[executionAsyncId()]
//     if (!context) {
//       return log.error(message, ...optionalParams)
//     }
//     return logger('error', message, ...optionalParams)
//   }
//   //@ts-ignore
//   console.dir = (obj: any, options?: NodeJS.InspectOptions): void => {
//     var context = contexts[executionAsyncId()]
//     if (!context) {
//       return log.dir(obj, options)
//     }

//     return logger(
//       'info',
//       `${inspect(obj, {
//         customInspect: false,
//         ...options
//       })}`
//     )
//   }
// }
