/*this is generated code */
import  { AxiosResponse } from 'axios'
declare global{
  namespace API {
    /** real_time_dw
      * 文档链接：http://op.pagoda.com.cn/?#/technical-doc?docId=1721
      * {"version":"1.0.0","title":"1.0.0","contact":{}}
      * goods-uat.ks1.pagoda.com.cn
    */
    namespace sink_member {

        type AccessSecretReqDTO = {

            accessKey?:   string

            signature?:   string

            systemCode?:   string

        }

        type ActivityImg = {

            imgInfo?:    Array < ImgInfo >

            proportion?:   string

        }

        type AdjustRedisKeyDTO = {

            parentCode?:   string

            val?:   number

        }

        type AdminBasicInfoRes = {

            distributionName?:   string

            goodsLevel?:   string

        }

        type AdminBatchSaveReq = {

            configValue?:   string

            floatUpNumber?:   string

            groupNumber?:   number

            headPic?:   string

            id?:   number

            input?:   string

            isDefaultPutAway?:   number

            memberPrice?:   string

            memberPriceChoice?:   number

            name?:   string

            specDesc?:   string

            vipPrice?:   string

            weight?:   string

        }

        type AdminCategorySortReq = {

            categoryList?:    Array < CategoryReq >

            distributionId?:   number

        }

        type AdminChannelRes = {

            channelName?:   string

            id?:   number

        }

        type AdminGoodsCategoryShowBackReq = {

            distributionId?:   number

            skuId?:   number

        }

        type AdminPublishCityStoreRes = {

            cityID?:   number

            cityName?:   string

            storeCount?:   number

        }

        type AdminPublishReq = {

            channelIDs?:    Array < number >

            cityIDs?:    Array < number >

            goodsIDs?:    Array < number >

        }

        type AdminPublishRes = {

            adminStoreStockResList?:    Array < AdminStoreStockRes >

            msgText?:   string

        }

        type AdminSaveGoodsCategoryRelReq = {

            distributionId?:   number

            goodsCategoryIdList?:    Array < number >

            skuIdList?:    Array < number >

        }

        type AdminSkuSpecListReq = {

            channelID?:   number

            distributionCode?:   string

            distributionId?:   number

            goodsId?:   number

            groupNumber?:   number

            spuNumber?:   string

            storeGroupNumber?:   number

        }

        type AdminSkuSpecListRes = {

            channels?:    Array < AdminChannelRes >

            createTime?:   string

            distributionId?:   number

            floatUpNumber?:   string

            groupNumber?:   number

            headPic?:   string

            id?:   number

            input?:   string

            isDefaultPutAway?:   number

            lastUpdate?:   string

            lastUpdateTime?:   string

            memberPrice?:   string

            memberPriceChoice?:   number

            name?:   string

            number?:   string

            publishCityStore?:    Array < AdminPublishCityStoreRes >

            publishStatus?:   string

            putAwayStatus?:   number

            quantity?:   string

            retailUnit?:   string

            sellPrice?:   string

            sort?:   number

            specDesc?:   string

            specQuantity?:   string

            specUnit?:   string

            storeGroupDataList?:    Array < AdminSkuSpecListRes >

            storeGroupNumber?:   number

            sysGenerateVipPrice?:   number

            vipPriceType?:   number

            weight?:   string

        }

        type AdminSpecBatchSortBaseReq = {

            distributionId?:   number

            sortList?:    Array < AdminSpecBatchSortReq >

            spuNumber?:   string

        }

        type AdminSpecBatchSortReq = {

            goodsId?:   number

            groupNumber?:   number

            priority?:   number

            storeGroupNumber?:   number

        }

        type AdminSpecCopySkuReq = {

            channelID?:   number

            copyGoodsID?:   number

            distributionCode?:   string

            distributionId?:   number

            goodsId?:   number

            groupNumber?:   number

            spuNumber?:   string

            storeGroupNumber?:   number

        }

        type AdminSpecDefaultPutAwayReq = {

            goodIds?:    Array < number >

            isDefaultPutAway?:   number

        }

        type AdminSpecDistributionBackShowReq = {

            distributionId?:   number

            resourceType?:   number

            skuId?:   number

        }

        type AdminSpecDistributionReq = {

            distributionId?:   number

        }

        type AdminStoreGroupSkuListReq = {

            groupNumberList?:    Array < number >

            storeGroupNumberList?:    Array < number >

        }

        type AdminStoreGroupSkuListRes = {

            goodsIdList?:    Array < number >

            storeGroupNumber?:   number

        }

        type AdminStoreStockRes = {

            goodsId?:   number

            storeId?:   number

        }

        type AggregateCommandDTO = {

            collect?:   string

            pipeline?:    Array < JSONObject >

        }

        type ApiForwardReqDTO = {

            body:   any

            head:   any

            key?:   string

            uri?:   string

        }

        type ApiRequestAccessSecretReqDTO = {

            body:  AccessSecretReqDTO

            head:   any

        }

        type ApiRequestAdjustRedisKeyDTO = {

            body:  AdjustRedisKeyDTO

            head:   any

        }

        type ApiRequestAdminCategorySortReq = {

            body:  AdminCategorySortReq

            head:   any

        }

        type ApiRequestAdminGoodsCategoryShowBackReq = {

            body:  AdminGoodsCategoryShowBackReq

            head:   any

        }

        type ApiRequestAdminPublishReq = {

            body:  AdminPublishReq

            head:   any

        }

        type ApiRequestAdminSaveGoodsCategoryRelReq = {

            body:  AdminSaveGoodsCategoryRelReq

            head:   any

        }

        type ApiRequestAdminSkuSpecListReq = {

            body:  AdminSkuSpecListReq

            head:   any

        }

        type ApiRequestAdminSpecBatchSortBaseReq = {

            body:  AdminSpecBatchSortBaseReq

            head:   any

        }

        type ApiRequestAdminSpecCopySkuReq = {

            body:  AdminSpecCopySkuReq

            head:   any

        }

        type ApiRequestAdminSpecDefaultPutAwayReq = {

            body:  AdminSpecDefaultPutAwayReq

            head:   any

        }

        type ApiRequestAdminSpecDistributionBackShowReq = {

            body:  AdminSpecDistributionBackShowReq

            head:   any

        }

        type ApiRequestAdminSpecDistributionReq = {

            body:  AdminSpecDistributionReq

            head:   any

        }

        type ApiRequestAdminStoreGroupSkuListReq = {

            body:  AdminStoreGroupSkuListReq

            head:   any

        }

        type ApiRequestBaseAdminPutAwayReq = {

            body:  BaseAdminPutAwayReq

            head:   any

        }

        type ApiRequestBatchSetSkuGoodsLabelRequest = {

            body:  BatchSetSkuGoodsLabelRequest

            head:   any

        }

        type ApiRequestBatchUpdateSkuGoodsRequest = {

            body:  BatchUpdateSkuGoodsRequest

            head:   any

        }

        type ApiRequestBlackWhiteListQueryDTO = {

            body:  BlackWhiteListQueryDTO

            head:   any

        }

        type ApiRequestBlackWhiteListReqDTO = {

            body:  BlackWhiteListReqDTO

            head:   any

        }

        type ApiRequestCategoryDelReqDTO = {

            body:  CategoryDelReqDTO

            head:   any

        }

        type ApiRequestCategoryQueryReqDTO = {

            body:  CategoryQueryReqDTO

            head:   any

        }

        type ApiRequestCategoryReq = {

            body:  CategoryReq

            head:   any

        }

        type ApiRequestCategorySelfAssociationQueryReqDTO = {

            body:  CategorySelfAssociationQueryReqDTO

            head:   any

        }

        type ApiRequestChannelAddReqDTO = {

            body:  ChannelAddReqDTO

            head:   any

        }

        type ApiRequestChannelDeleteReqDTO = {

            body:  ChannelDeleteReqDTO

            head:   any

        }

        type ApiRequestChannelGetReqDTO = {

            body:  ChannelGetReqDTO

            head:   any

        }

        type ApiRequestChannelGoodsReqDTO = {

            body:  ChannelGoodsReqDTO

            head:   any

        }

        type ApiRequestChannelListAllReqDTO = {

            body:  ChannelListAllReqDTO

            head:   any

        }

        type ApiRequestChannelListReqDTO = {

            body:  ChannelListReqDTO

            head:   any

        }

        type ApiRequestChannelResourceGroupDTO = {

            body:  ChannelResourceGroupDTO

            head:   any

        }

        type ApiRequestChannelUpdateReqDTO = {

            body:  ChannelUpdateReqDTO

            head:   any

        }

        type ApiRequestDeductionSaleStockReqDTO = {

            body:  DeductionSaleStockReqDTO

            head:   any

        }

        type ApiRequestDeleteUnPostReqDTO = {

            body:  DeleteUnPostReqDTO

            head:   any

        }

        type ApiRequestDictionaryAddReqDTO = {

            body:  DictionaryAddReqDTO

            head:   any

        }

        type ApiRequestDictionaryUpdateReqDTO = {

            body:  DictionaryUpdateReqDTO

            head:   any

        }

        type ApiRequestErpGoodsPriceRenewalDTO = {

            body:  ErpGoodsPriceRenewalDTO

            head:   any

        }

        type ApiRequestExampleInputDTO = {

            body:  ExampleInputDTO

            head:   any

        }

        type ApiRequestExampleSimpleInputDTO = {

            body:  ExampleSimpleInputDTO

            head:   any

        }

        type ApiRequestGoodsBasicReleaseAllDTO = {

            body:  GoodsBasicReleaseAllDTO

            head:   any

        }

        type ApiRequestGoodsDetailReqDTO = {

            body:  GoodsDetailReqDTO

            head:   any

        }

        type ApiRequestGoodsGenerateCodeReqDTO = {

            body:  GoodsGenerateCodeReqDTO

            head:   any

        }

        type ApiRequestGoodsInfoReqDTO = {

            body:  GoodsInfoReqDTO

            head:   any

        }

        type ApiRequestGoodsListReqDTO = {

            body:  GoodsListReqDTO

            head:   any

        }

        type ApiRequestGoodsPriceCountReqDTO = {

            body:  GoodsPriceCountReqDTO

            head:   any

        }

        type ApiRequestGoodsReq = {

            body:  GoodsReq

            head:   any

        }

        type ApiRequestGoodsSearchReqDTO = {

            body:  GoodsSearchReqDTO

            head:   any

        }

        type ApiRequestGoodsSnapshotDetailFilterQueryDTO = {

            body:  GoodsSnapshotDetailFilterQueryDTO

            head:   any

        }

        type ApiRequestGoodsTemplateDeleteReqDTO = {

            body:  GoodsTemplateDeleteReqDTO

            head:   any

        }

        type ApiRequestGoodsTemplateDetailReqDTO = {

            body:  GoodsTemplateDetailReqDTO

            head:   any

        }

        type ApiRequestGoodsTemplateDuplicateReqDTO = {

            body:  GoodsTemplateDuplicateReqDTO

            head:   any

        }

        type ApiRequestGoodsTemplateListReqDTO = {

            body:  GoodsTemplateListReqDTO

            head:   any

        }

        type ApiRequestListAdminBatchSaveReq = {

            body:    Array < AdminBatchSaveReq >

            head:   any

        }

        type ApiRequestListBlackWhiteListReqDTO = {

            body:    Array < BlackWhiteListReqDTO >

            head:   any

        }

        type ApiRequestListCancelGoodsCustomInventoryDTO = {

            body:    Array < CancelGoodsCustomInventoryDTO >

            head:   any

        }

        type ApiRequestListCategoryCreateWebDTO = {

            body:    Array < CategoryCreateWebDTO >

            head:   any

        }

        type ApiRequestListCategorySelfAssociationSaveDTO = {

            body:    Array < CategorySelfAssociationSaveDTO >

            head:   any

        }

        type ApiRequestListCategorySelfAssociationUpdateDTO = {

            body:    Array < CategorySelfAssociationUpdateDTO >

            head:   any

        }

        type ApiRequestListCategoryUpdateWebDTO = {

            body:    Array < CategoryUpdateWebDTO >

            head:   any

        }

        type ApiRequestListChannelOpenReqDTO = {

            body:    Array < ChannelOpenReqDTO >

            head:   any

        }

        type ApiRequestListDeleteChannelGoodsDTO = {

            body:    Array < DeleteChannelGoodsDTO >

            head:   any

        }

        type ApiRequestListDictionaryListReqDTO = {

            body:    Array < DictionaryListReqDTO >

            head:   any

        }

        type ApiRequestListEshopSaleStockGoodsAuthorizationDTO = {

            body:    Array < EshopSaleStockGoodsAuthorizationDTO >

            head:   any

        }

        type ApiRequestListFusionGoodsPriceDTO = {

            body:    Array < FusionGoodsPriceDTO >

            head:   any

        }

        type ApiRequestListGoodsCustomInventoryDTO = {

            body:    Array < GoodsCustomInventoryDTO >

            head:   any

        }

        type ApiRequestListGoodsCustomRetailPriceDTO = {

            body:    Array < GoodsCustomRetailPriceDTO >

            head:   any

        }

        type ApiRequestListGoodsDeleteReqDTO = {

            body:    Array < GoodsDeleteReqDTO >

            head:   any

        }

        type ApiRequestListGoodsMemberPriceUpdateDTO = {

            body:    Array < GoodsMemberPriceUpdateDTO >

            head:   any

        }

        type ApiRequestListGoodsPriceSyncReqDTO = {

            body:    Array < GoodsPriceSyncReqDTO >

            head:   any

        }

        type ApiRequestListGoodsPriceUpdateDTO = {

            body:    Array < GoodsPriceUpdateDTO >

            head:   any

        }

        type ApiRequestListGoodsPullAuthorizationReqDTO = {

            body:    Array < GoodsPullAuthorizationReqDTO >

            head:   any

        }

        type ApiRequestListGoodsRecommendationReqDTO = {

            body:    Array < GoodsRecommendationReqDTO >

            head:   any

        }

        type ApiRequestListGoodsReleaseReqDTO = {

            body:    Array < GoodsReleaseReqDTO >

            head:   any

        }

        type ApiRequestListGoodsSnapshotIdQueryDTO = {

            body:    Array < GoodsSnapshotIdQueryDTO >

            head:   any

        }

        type ApiRequestListGoodsSynchronizeInfoReqDTO = {

            body:    Array < GoodsSynchronizeInfoReqDTO >

            head:   any

        }

        type ApiRequestListGoodsSynchronizePriceReqDTO = {

            body:    Array < GoodsSynchronizePriceReqDTO >

            head:   any

        }

        type ApiRequestListGoodsTemplateReqDTO = {

            body:    Array < GoodsTemplateReqDTO >

            head:   any

        }

        type ApiRequestListGoodsUnionKeyReqDTO = {

            body:    Array < GoodsUnionKeyReqDTO >

            head:   any

        }

        type ApiRequestListGoodsUniqueKeyReqDTO = {

            body:    Array < GoodsUniqueKeyReqDTO >

            head:   any

        }

        type ApiRequestListGoodsUniqueReqDTO = {

            body:    Array < GoodsUniqueReqDTO >

            head:   any

        }

        type ApiRequestListGoodsUpdateBasicReqDTO = {

            body:    Array < GoodsUpdateBasicReqDTO >

            head:   any

        }

        type ApiRequestListGoodsUpdateChannelReqDTO = {

            body:    Array < GoodsUpdateChannelReqDTO >

            head:   any

        }

        type ApiRequestListGoodsUpdatePriceReqDTO = {

            body:    Array < GoodsUpdatePriceReqDTO >

            head:   any

        }

        type ApiRequestListGoodsUpdateSaleStatusReqDTO = {

            body:    Array < GoodsUpdateSaleStatusReqDTO >

            head:   any

        }

        type ApiRequestListMtGoodsUniqueReqDTO = {

            body:    Array < MtGoodsUniqueReqDTO >

            head:   any

        }

        type ApiRequestListOrgCategoryCreateWebDTO = {

            body:    Array < OrgCategoryCreateWebDTO >

            head:   any

        }

        type ApiRequestListOrgCategoryDelReqDTO = {

            body:    Array < OrgCategoryDelReqDTO >

            head:   any

        }

        type ApiRequestListOrgCategoryGoodsDelReqDTO = {

            body:    Array < OrgCategoryGoodsDelReqDTO >

            head:   any

        }

        type ApiRequestListOrgCategoryGoodsRelationDTO = {

            body:    Array < OrgCategoryGoodsRelationDTO >

            head:   any

        }

        type ApiRequestListOrgCategoryUpdateWebDTO = {

            body:    Array < OrgCategoryUpdateWebDTO >

            head:   any

        }

        type ApiRequestListOrgChannelCategoryGoodsWebReqDTO = {

            body:    Array < OrgChannelCategoryGoodsWebReqDTO >

            head:   any

        }

        type ApiRequestListOrganizationSaveReqDTO = {

            body:    Array < OrganizationSaveReqDTO >

            head:   any

        }

        type ApiRequestListPageableReqDTO = {

            body:    Array < PageableReqDTO >

            head:   any

        }

        type ApiRequestListPushGoodsReqDTO = {

            body:    Array < PushGoodsReqDTO >

            head:   any

        }

        type ApiRequestListReSyncOrgGoodsReqDTO = {

            body:    Array < ReSyncOrgGoodsReqDTO >

            head:   any

        }

        type ApiRequestListResourceCancelCornerReqDTO = {

            body:    Array < ResourceCancelCornerReqDTO >

            head:   any

        }

        type ApiRequestListResourceSetCornerReqDTO = {

            body:    Array < ResourceSetCornerReqDTO >

            head:   any

        }

        type ApiRequestListSpuErpGoodsReqDTO = {

            body:    Array < SpuErpGoodsReqDTO >

            head:   any

        }

        type ApiRequestListStockUpdateReqDTO = {

            body:    Array < StockUpdateReqDTO >

            head:   any

        }

        type ApiRequestListstring = {

            body:    Array < string >

            head:   any

        }

        type ApiRequestMarketingBatchListReqDto = {

            body:  MarketingBatchListReqDto

            head:   any

        }

        type ApiRequestMarketingPageQueryReqDto = {

            body:  MarketingPageQueryReqDto

            head:   any

        }

        type ApiRequestMarketingSaveActivityReqDto = {

            body:  MarketingSaveActivityReqDto

            head:   any

        }

        type ApiRequestMaterialGroupListDTO = {

            body:  MaterialGroupListDTO

            head:   any

        }

        type ApiRequestMeituanCategoryPropertyReqDTO = {

            body:  MeituanCategoryPropertyReqDTO

            head:   any

        }

        type ApiRequestMeituanCategoryPropertyValueReqDTO = {

            body:  MeituanCategoryPropertyValueReqDTO

            head:   any

        }

        type ApiRequestMtGoodsInfoReqDTO = {

            body:  MtGoodsInfoReqDTO

            head:   any

        }

        type ApiRequestOrgCategoryQueryReqDTO = {

            body:  OrgCategoryQueryReqDTO

            head:   any

        }

        type ApiRequestOrgCategoryReqDTO = {

            body:  OrgCategoryReqDTO

            head:   any

        }

        type ApiRequestOrgCategoryTreeQueryReqDTO = {

            body:  OrgCategoryTreeQueryReqDTO

            head:   any

        }

        type ApiRequestOrgLeafCategoryQueryDTO = {

            body:  OrgLeafCategoryQueryDTO

            head:   any

        }

        type ApiRequestOrganizationRemoveReqDTO = {

            body:  OrganizationRemoveReqDTO

            head:   any

        }

        type ApiRequestOrganizationReqDTO = {

            body:  OrganizationReqDTO

            head:   any

        }

        type ApiRequestOrganizationTreeReqDTO = {

            body:  OrganizationTreeReqDTO

            head:   any

        }

        type ApiRequestOrganizationUpdateReqDTO = {

            body:  OrganizationUpdateReqDTO

            head:   any

        }

        type ApiRequestPageableReqDTO = {

            body:  PageableReqDTO

            head:   any

        }

        type ApiRequestQueryConfigValueRequest = {

            body:  QueryConfigValueRequest

            head:   any

        }

        type ApiRequestQueryDistributionListReq = {

            body:  QueryDistributionListReq

            head:   any

        }

        type ApiRequestQueryGoodsPriceReqDTO = {

            body:  QueryGoodsPriceReqDTO

            head:   any

        }

        type ApiRequestQuerySaleGoodsReqDTO = {

            body:  QuerySaleGoodsReqDTO

            head:   any

        }

        type ApiRequestQuerySkuGoodsPriceReqDTO = {

            body:  QuerySkuGoodsPriceReqDTO

            head:   any

        }

        type ApiRequestQueryWarehouseStockReqDTO = {

            body:  QueryWarehouseStockReqDTO

            head:   any

        }

        type ApiRequestQueryWarehouseStoreDTO = {

            body:  QueryWarehouseStoreDTO

            head:   any

        }

        type ApiRequestRedisSetValueReqDTO = {

            body:  RedisSetValueReqDTO

            head:   any

        }

        type ApiRequestRemoteServiceRequest = {

            body:  RemoteServiceRequest

            head:   any

        }

        type ApiRequestResourceAddGroupReqDTO = {

            body:  ResourceAddGroupReqDTO

            head:   any

        }

        type ApiRequestResourceGroupDeleteReqDTO = {

            body:  ResourceGroupDeleteReqDTO

            head:   any

        }

        type ApiRequestResourceGroupGetReqDTO = {

            body:  ResourceGroupGetReqDTO

            head:   any

        }

        type ApiRequestResourceGroupListReqDTO = {

            body:  ResourceGroupListReqDTO

            head:   any

        }

        type ApiRequestResourceReqDTO = {

            body:  ResourceReqDTO

            head:   any

        }

        type ApiRequestResourceUpdateGoodsGroupReqDTO = {

            body:  ResourceUpdateGoodsGroupReqDTO

            head:   any

        }

        type ApiRequestResourceUpdateGroupReqDTO = {

            body:  ResourceUpdateGroupReqDTO

            head:   any

        }

        type ApiRequestResourceUpdateWebReqDTO = {

            body:  ResourceUpdateWebReqDTO

            head:   any

        }

        type ApiRequestSaleOrderReqDTO = {

            body:  SaleOrderReqDTO

            head:   any

        }

        type ApiRequestSaleStockListReqDTO = {

            body:  SaleStockListReqDTO

            head:   any

        }

        type ApiRequestSaveSkuGoodsRequest = {

            body:  SaveSkuGoodsRequest

            head:   any

        }

        type ApiRequestSetGoodsSpecialPriceRequest = {

            body:  SetGoodsSpecialPriceRequest

            head:   any

        }

        type ApiRequestSetGoodsVipPriceRequest = {

            body:  SetGoodsVipPriceRequest

            head:   any

        }

        type ApiRequestSetWarehouseStockCustomInventoryReqDTO = {

            body:  SetWarehouseStockCustomInventoryReqDTO

            head:   any

        }

        type ApiRequestSetWarehouseStockDTO = {

            body:  SetWarehouseStockDTO

            head:   any

        }

        type ApiRequestSetGoodsUnionKeyReqDTO = {

            body:    Array < GoodsUnionKeyReqDTO >

            head:   any

        }

        type ApiRequestSetWarehouseStockCoverReqDTO = {

            body:    Array < WarehouseStockCoverReqDTO >

            head:   any

        }

        type ApiRequestSetWarehouseStockUpdateReqDTO = {

            body:    Array < WarehouseStockUpdateReqDTO >

            head:   any

        }

        type ApiRequestSkuGoods = {

            body:  SkuGoods

            head:   any

        }

        type ApiRequestSpuBrandPicReq = {

            body:  SpuBrandPicReq

            head:   any

        }

        type ApiRequestSpuGoodsListReq = {

            body:  SpuGoodsListReq

            head:   any

        }

        type ApiRequestSpuReq = {

            body:  SpuReq

            head:   any

        }

        type ApiRequestSpuRes = {

            body:  SpuRes

            head:   any

        }

        type ApiRequestStockTccConfirmDTO = {

            body:  StockTccConfirmDTO

            head:   any

        }

        type ApiRequestStockTccDTO = {

            body:  StockTccDTO

            head:   any

        }

        type ApiRequestUpdateGoodsPriceReqDTO = {

            body:  UpdateGoodsPriceReqDTO

            head:   any

        }

        type ApiRequestWarehouseStockListReqDTO = {

            body:  WarehouseStockListReqDTO

            head:   any

        }

        type ApiRequestWarehouseStockRateReqDTO = {

            body:  WarehouseStockRateReqDTO

            head:   any

        }

        type ApiRequestsku_goods = {

            body:  sku_goods

            head:   any

        }

        type ApiRequestspu_goods = {

            body:  spu_goods

            head:   any

        }

        type ApiResult = {

            data?:   any

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultAccessSecretReqDTO = {

            data?:  AccessSecretReqDTO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultAdminBasicInfoRes = {

            data?:  AdminBasicInfoRes

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultAdminPublishRes = {

            data?:  AdminPublishRes

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultBaseRes = {

            data?:  BaseRes

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultCategoryNodeDTO = {

            data?:  CategoryNodeDTO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultCategoryRespDTO = {

            data?:  CategoryRespDTO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultChannelGetRespDTO = {

            data?:  ChannelGetRespDTO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultCustomDynamicPageOutputDTOCityDO = {

            data?:  CustomDynamicPageOutputDTOCityDO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultCustomDynamicPageOutputDTOGoodsCategoryCityRelDTO = {

            data?:  CustomDynamicPageOutputDTOGoodsCategoryCityRelDTO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultCustomDynamicPageOutputDTOGoodsRelDTO = {

            data?:  CustomDynamicPageOutputDTOGoodsRelDTO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultCustomDynamicPageOutputDTOLabel = {

            data?:  CustomDynamicPageOutputDTOLabel

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultCustomDynamicPageOutputDTOResource = {

            data?:  CustomDynamicPageOutputDTOResource

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultCustomDynamicPageOutputDTOSVipGoodsGoodsRel2DTO = {

            data?:  CustomDynamicPageOutputDTOSVipGoodsGoodsRel2DTO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultCustomDynamicPageOutputDTOSkuGoodsDTO = {

            data?:  CustomDynamicPageOutputDTOSkuGoodsDTO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultCustomDynamicPageOutputDTOStoreDO = {

            data?:  CustomDynamicPageOutputDTOStoreDO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultCustomDynamicPageOutputDTOSvipGoodsStockDO = {

            data?:  CustomDynamicPageOutputDTOSvipGoodsStockDO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultCustomDynamicPageOutputDTObrand = {

            data?:  CustomDynamicPageOutputDTObrand

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultCustomDynamicPageOutputDTOgoods_city_rel = {

            data?:  CustomDynamicPageOutputDTOgoods_city_rel

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultCustomDynamicPageOutputDTOgoods_distribution = {

            data?:  CustomDynamicPageOutputDTOgoods_distribution

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultCustomDynamicPageOutputDTOgoods = {

            data?:  CustomDynamicPageOutputDTOgoods

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultCustomDynamicPageOutputDTOspu_goods = {

            data?:  CustomDynamicPageOutputDTOspu_goods

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultCustomDynamicPageOutputDTOstore_goods_rel = {

            data?:  CustomDynamicPageOutputDTOstore_goods_rel

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultCustomDynamicPageOutputDTOsvip_goods_extend = {

            data?:  CustomDynamicPageOutputDTOsvip_goods_extend

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultCustomDynamicPageOutputDTOsvip_goods_goods_rel = {

            data?:  CustomDynamicPageOutputDTOsvip_goods_goods_rel

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultErpGoodsPriceDTO = {

            data?:  ErpGoodsPriceDTO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultEshopDaoRespDTO = {

            data?:  EshopDaoRespDTO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultGoodsCategoryRelDis = {

            data?:  GoodsCategoryRelDis

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultGoodsDetailRespDTO = {

            data?:  GoodsDetailRespDTO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultGoodsTemplateRespDTO = {

            data?:  GoodsTemplateRespDTO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultJSONObject = {

            data?:   any

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListAdminChannelRes = {

            data?:    Array < AdminChannelRes >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListAdminSkuSpecListRes = {

            data?:    Array < AdminSkuSpecListRes >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListAdminStoreGroupSkuListRes = {

            data?:    Array < AdminStoreGroupSkuListRes >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListBaseGoodsSnapshotRespDTO = {

            data?:    Array < BaseGoodsSnapshotRespDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListBlackWhiteListDTO = {

            data?:    Array < BlackWhiteListDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListBrand = {

            data?:    Array < Brand >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListCategoryRespDTO = {

            data?:    Array < CategoryRespDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListCategoryRes = {

            data?:    Array < CategoryRes >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListCategorySelfAssociationRespDTO = {

            data?:    Array < CategorySelfAssociationRespDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListChannelGoodsRespDTO = {

            data?:    Array < ChannelGoodsRespDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListChannelListBeanRespDTO = {

            data?:    Array < ChannelListBeanRespDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListDictionaryListRespDTO = {

            data?:    Array < DictionaryListRespDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListDistribution = {

            data?:    Array < Distribution >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListESGoodsChannelDTO = {

            data?:    Array < ESGoodsChannelDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListErpStoreGoodsStockInfoDTO = {

            data?:    Array < ErpStoreGoodsStockInfoDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListGoodsChannelDTO = {

            data?:    Array < GoodsChannelDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListGoodsLabelRelInfo = {

            data?:    Array < GoodsLabelRelInfo >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListGoodsPriceCountDTO = {

            data?:    Array < GoodsPriceCountDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListGoodsSnapshotRespDTO = {

            data?:    Array < GoodsSnapshotRespDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListImages = {

            data?:    Array < Images >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListJSONObject = {

            data?:    Array < JSONObject >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListLightGoodsDetailDTO = {

            data?:    Array < LightGoodsDetailDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListMarketingBatchListRespDto = {

            data?:    Array < MarketingBatchListRespDto >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListMaterialGroupRespDTO = {

            data?:    Array < MaterialGroupRespDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListMtGoodsChannelDTO = {

            data?:    Array < MtGoodsChannelDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListOrgCategoryRespDTO = {

            data?:    Array < OrgCategoryRespDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListOrgChannelCategoryGoodsWebRespDTO = {

            data?:    Array < OrgChannelCategoryGoodsWebRespDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListOrganizationRespDTO = {

            data?:    Array < OrganizationRespDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListOrganizationTreeRespDTO = {

            data?:    Array < OrganizationTreeRespDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListQueryGoodsPriceRespDTO = {

            data?:    Array < QueryGoodsPriceRespDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListQuerySaleableGoodsRespDTO = {

            data?:    Array < QuerySaleableGoodsRespDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListQuerySkuGoodsPriceRespDTO = {

            data?:    Array < QuerySkuGoodsPriceRespDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListQueryWarehouseStockRespDTO = {

            data?:    Array < QueryWarehouseStockRespDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListReSyncOrgGoodsRespDTO = {

            data?:    Array < ReSyncOrgGoodsRespDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListSaleStockListRespDTO = {

            data?:    Array < SaleStockListRespDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListSectionOfCombinationGoodsDTO = {

            data?:    Array < SectionOfCombinationGoodsDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListSpuGoodsResponseDTO = {

            data?:    Array < SpuGoodsResponseDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListWarehouseStockListRespDTO = {

            data?:    Array < WarehouseStockListRespDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListWarehouseStockRateRespDTO = {

            data?:    Array < WarehouseStockRateRespDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListWriteOperateAffectedCategoryDTO = {

            data?:    Array < WriteOperateAffectedCategoryDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListlong = {

            data?:    Array < number >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListstring = {

            data?:    Array < string >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultMapstringListGoodsLabel = {

            data?:   any

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultMarketingQueryRespDto = {

            data?:  MarketingQueryRespDto

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultOrgCategoryNodeDTO = {

            data?:  OrgCategoryNodeDTO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPageWebRespDTOChannelListBeanRespDTO = {

            data?:  PageWebRespDTOChannelListBeanRespDTO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPageWebRespDTOResourceGroupListBean = {

            data?:  PageWebRespDTOResourceGroupListBean

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPageableRespDTOFrontOrgChannelGoodsWebRespDTO = {

            data?:  PageableRespDTOFrontOrgChannelGoodsWebRespDTO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPageableRespDTOGoodsChannelDTO = {

            data?:  PageableRespDTOGoodsChannelDTO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPageableRespDTOGoodsTemplateListRespDTO = {

            data?:  PageableRespDTOGoodsTemplateListRespDTO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPageableRespDTOGoodsWarehouseStockRespDTO = {

            data?:  PageableRespDTOGoodsWarehouseStockRespDTO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPageableRespDTOMarketingPageQueryRespDto = {

            data?:  PageableRespDTOMarketingPageQueryRespDto

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPageableRespDTOOrgChannelGoodsRespDTO = {

            data?:  PageableRespDTOOrgChannelGoodsRespDTO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPageableRespDTOQueryWarehouseStockRespDTO = {

            data?:  PageableRespDTOQueryWarehouseStockRespDTO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPageableRespDTOResourceWebRespDTO = {

            data?:  PageableRespDTOResourceWebRespDTO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPageableRespDTOSaleStockRespDTO = {

            data?:  PageableRespDTOSaleStockRespDTO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPageableRespDTOSkuGoodsResponseDTO = {

            data?:  PageableRespDTOSkuGoodsResponseDTO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPageableRespDTOSpuRes = {

            data?:  PageableRespDTOSpuRes

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPageableRespDTOStoreGoodsRespDTO = {

            data?:  PageableRespDTOStoreGoodsRespDTO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultQueryWarehouseStockRespDTO = {

            data?:  QueryWarehouseStockRespDTO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultResourceGroupGetRespDTO = {

            data?:  ResourceGroupGetRespDTO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultSkuGoodsQueryDetailResponse = {

            data?:  SkuGoodsQueryDetailResponse

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultSkuGoods = {

            data?:  SkuGoods

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultSpuGoods = {

            data?:  SpuGoods

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultSpuRes = {

            data?:  SpuRes

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultThirdChannelGoodsSyncStateDTO = {

            data?:  ThirdChannelGoodsSyncStateDTO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultboolean = {

            data?:   boolean

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultfloat = {

            data?:   number

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultint = {

            data?:   number

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultlong = {

            data?:   number

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultobject = {

            data?:   any

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultsku_goods = {

            data?:  sku_goods

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultspu_goods = {

            data?:  spu_goods

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultstring = {

            data?:   string

            errorMsg?:   string

            resultCode:   number

        }

        type BaseAdminPutAwayReq = {

            distributionId?:   number

            putAwayStatus?:   number

            skuNumberList?:    Array < string >

        }

        type BaseGoodsDTO = {

            goodsSn?:   string

        }

        type BaseGoodsDetailReqDTO = {

            basicGoodsId?:   string

            basicGoodsName?:   string

            basicGoodsSn?:   string

            dosage?:   number

            saleType?:   number

        }

        type BaseGoodsSnapshotRespDTO = {

            goodsId?:   number

            id?:   number

            organizationCode?:   string

            organizationType?:   number

            skuId?:   number

            updateTimestamp?:   string

        }

        type BaseRes = {

            goodsList?:    Array < GoodsInf >

            totalElements?:   number

            totalPages?:   number

        }

        type BatchSetSkuGoodsLabelRequest = {

            idsList?:    Array < number >

            labelList?:    Array < GoodsLabel >

        }

        type BatchUpdateSkuGoodsRequest = {

            idsList?:    Array < number >

            operateType?:   number

        }

        type BlackWhiteListDTO = {

            channelId?:   number

            id?:   number

            isEnable?:   boolean

            listType?:   number

            organizationCode?:   string

        }

        type BlackWhiteListQueryDTO = {

            channelId?:   number

            isEnable?:   boolean

            listType?:   number

            organizationCode?:   string

        }

        type BlackWhiteListReqDTO = {

            channelId?:   number

            isEnable?:   boolean

            listType?:   number

            organizationCode?:   string

        }

        type Brand = {

            contacter?:   string

            createTime?:   string

            id?:   number

            lastUpdate?:   string

            logo?:   string

            name?:   string

            status?:   string

        }

        type CancelGoodsCustomInventoryDTO = {

            channelId?:   number

            goodsShortId?:   number

            goodsSn?:   string

            operator?:   string

            organizationCode?:   string

        }

        type CarouselImg = {

            imgInfo?:    Array < ImgInfo >

            proportion?:   string

        }

        type CategoryCreateWebDTO = {

            name?:   string

            operator?:   string

            parentCategoryCode?:   string

            sortVal?:   number

            thirdPartyCategoryCode?:   string

            thirdPartyChannelCode?:   string

        }

        type CategoryDelReqDTO = {

            categoryCodeList?:    Array < string >

            operator?:   string

        }

        type CategoryNodeDTO = {

            associateFrontCategory?:    Array < string >

            associateSystemCategory?:    Array < string >

            associateThirdCategory?:    Array < string >

            categoryCode?:   string

            children?:    Array < CategoryNodeDTO >

            createdAt?:   string

            goodsNum?:   number

            name?:   string

            sortVal?:   number

            thirdPartyCategoryCode?:   string

            thirdPartyChannelCode?:   string

            updatedAt?:   string

        }

        type CategoryQueryReqDTO = {

            categoryCode?:   string

            nameKeyword?:   string

            needReturnAssociateFrontCategory?:   boolean

            needReturnAssociateSystemCategory?:   boolean

            needReturnAssociateThirdCategory?:   boolean

            onlyChild?:   boolean

            thirdChannelCode?:   string

        }

        type CategoryReq = {

            cityId?:   number

            distributionId?:   number

            id?:   number

            name?:   string

            priority?:   number

            type?:   number

        }

        type CategoryRes = {

            count?:   number

            id?:   number

            name?:   string

            priority?:   number

        }

        type CategoryRespDTO = {

            categoryCode?:   string

            extendsCode?:   string

            id?:   number

            isDeleted?:   boolean

            isSupportVip?:   number

            leafNum?:   number

            logicId?:   number

            name?:   string

            organizationCode?:   string

            parentCategoryCode?:   string

            refId?:   number

            sortVal?:   number

            thirdPartyCategoryCode?:   string

            thirdPartyChannelCode?:   string

            updatedAt?:   string

            version?:   number

        }

        type CategorySelfAssociationQueryReqDTO = {

            associationType?:   number

            categoryCodes?:    Array < string >

        }

        type CategorySelfAssociationRespDTO = {

            associationCategoryCode?:   string

            associationCategoryName?:   string

            associationCode?:   string

            associationType?:   number

            categoryCode?:   string

        }

        type CategorySelfAssociationSaveDTO = {

            associationCodeList?:    Array < string >

            associationType?:   number

            sysCategoryCode?:   string

        }

        type CategorySelfAssociationUpdateDTO = {

            associationCategoryCode?:   string

            associationCode?:   string

            associationType?:   number

            deleted?:   boolean

            sysCategoryCode?:   string

        }

        type CategoryUpdateWebDTO = {

            categoryCode?:   string

            name?:   string

            operator?:   string

            sortVal?:   number

            thirdPartyCategoryCode?:   string

            thirdPartyChannelCode?:   string

        }

        type ChannelAddReqDTO = {

            activityImgProportion?:   string

            activityImgSize?:   string

            brand?:   number

            carouselImgProportion?:   string

            carouselImgSize?:   string

            description?:   string

            isEnabled?:   boolean

            isManagedInDistributionCenter?:   boolean

            logoUrl?:   string

            mainImgProportion?:   string

            mainImgSize?:   string

            name?:   string

            openedAt?:   string

            operator?:   string

            type?:   number

        }

        type ChannelDeleteReqDTO = {

            id?:    Array < number >

            operator?:   string

        }

        type ChannelGetReqDTO = {

            id?:   number

        }

        type ChannelGetRespDTO = {

            activityImgProportion?:   string

            activityImgSize?:   string

            carouselImgProportion?:   string

            carouselImgSize?:   string

            description?:   string

            id?:   number

            isEnabled?:   number

            isManagedInDistributionCenter?:   number

            logoUrl?:   string

            mainImgProportion?:   string

            mainImgSize?:   string

            name?:   string

            openedAt?:   string

            type?:   number

        }

        type ChannelGoodsReqDTO = {

            channelId?:   number

            goodsName?:   string

            goodsSn?:   string

            organizationCode?:   string

            pageNumber?:   number

            pageSize?:   number

        }

        type ChannelGoodsRespDTO = {

            channelId?:   number

            goodsChannelId?:   number

            goodsId?:   number

            goodsName?:   string

            goodsPriceId?:   number

            goodsSn?:   string

            mnemonicCode?:   string

            organizationCode?:   string

            retailPrice?:   number

            retailUnitCode?:   string

            retailUnitName?:   string

        }

        type ChannelListAllReqDTO = {

            name?:   string

        }

        type ChannelListBeanRespDTO = {

            activityImgProportion?:   string

            activityImgSize?:   string

            carouselImgProportion?:   string

            carouselImgSize?:   string

            id?:   number

            isEnabled?:   number

            logoUrl?:   string

            mainImgProportion?:   string

            mainImgSize?:   string

            name?:   string

            type?:   number

        }

        type ChannelListReqDTO = {

            name?:   string

            pageNumber?:   number

            pageSize?:   number

        }

        type ChannelOpenReqDTO = {

            channelID?:   number

            operator?:   string

            organizationCode?:   string

        }

        type ChannelResourceGroupDTO = {

            activityImg?:  ActivityImg

            carouselImg?:  CarouselImg

            categoryCode?:   string

            channelId?:   number

            detailImg?:    Array < DetailImg >

            keywords?:    Array < string >

            mainImg?:  MainImg

            operator?:   string

            saleType?:   number

            video?:   string

            videoHeadImg?:   string

        }

        type ChannelSet = {

            carouselImgProportion?:   string

            carouselImgSize?:   string

            channel?:   string

            detailImgProportion?:   string

            detailImgSize?:   string

            dynamicImgProportion?:   string

            dynamicImgSize?:   string

            isSelected?:   number

            mainImgProportion?:   string

            mainImgSize?:   string

            setsList?:    Array < ImageSet >

        }

        type ChannelSetDTO = {

            carouselImgProportion?:   string

            carouselImgSize?:   string

            channel?:   string

            detailImgProportion?:   string

            detailImgSize?:   string

            dynamicImgProportion?:   string

            dynamicImgSize?:   string

            isSelected?:   number

            mainImgProportion?:   string

            mainImgSize?:   string

            setsList?:    Array < SetDTO >

        }

        type ChannelUpdateReqDTO = {

            activityImgProportion?:   string

            activityImgSize?:   string

            carouselImgProportion?:   string

            carouselImgSize?:   string

            description?:   string

            id?:   number

            isEnabled?:   boolean

            isManagedInDistributionCenter?:   boolean

            logoUrl?:   string

            mainImgProportion?:   string

            mainImgSize?:   string

            name?:   string

            operator?:   string

            type?:   number

        }

        type City = {

            cityGroupID?:   number

            createTime?:   string

            id?:   number

            isDefault?:   string

            isSupportVip?:   string

            lastUpdate?:   string

            lat?:   string

            lon?:   string

            mapCityID?:   string

            name?:   string

            province?:   string

            status?:   string

        }

        type CityDO = {

            cityGroupId?:   number

            createdAt?:   string

            createdOperator?:   string

            deliverySwitch?:   string

            id?:   number

            isDefault?:   boolean

            isSupportVip?:   boolean

            lat?:   string

            lon?:   string

            mapCityId?:   string

            name?:   string

            province?:   string

            status?:   string

            updatedAt?:   string

            updatedOperator?:   string

        }

        type CityDelayJudgeTime = {

            cityID?:   number

            delayJudgeTime?:  Time

        }

        type CountCommandDTO = {

            collect?:   string

            query?:   any

        }

        type CriteriaInputDTO = {

            andOr?:   string

            criteria?:    Array < CriterionInputDTO >

        }

        type CriterionInputDTO = {

            andOr?:   string

            condition?:   string

            property?:   string

            secondValue?:   any

            value?:   any

        }

        type CustomDynamicPageOutputDTOCityDO = {

            items?:    Array < CityDO >

            totalElements?:   number

        }

        type CustomDynamicPageOutputDTOGoodsCategoryCityRelDTO = {

            items?:    Array < GoodsCategoryCityRelDTO >

            totalElements?:   number

        }

        type CustomDynamicPageOutputDTOGoodsRelDTO = {

            items?:    Array < GoodsRelDTO >

            totalElements?:   number

        }

        type CustomDynamicPageOutputDTOLabel = {

            items?:    Array < Label >

            totalElements?:   number

        }

        type CustomDynamicPageOutputDTOResource = {

            items?:    Array < Resource >

            totalElements?:   number

        }

        type CustomDynamicPageOutputDTOSVipGoodsGoodsRel2DTO = {

            items?:    Array < SVipGoodsGoodsRel2DTO >

            totalElements?:   number

        }

        type CustomDynamicPageOutputDTOSkuGoodsDTO = {

            items?:    Array < SkuGoodsDTO >

            totalElements?:   number

        }

        type CustomDynamicPageOutputDTOStoreDO = {

            items?:    Array < StoreDO >

            totalElements?:   number

        }

        type CustomDynamicPageOutputDTOSvipGoodsStockDO = {

            items?:    Array < SvipGoodsStockDO >

            totalElements?:   number

        }

        type CustomDynamicPageOutputDTObrand = {

            items?:    Array < brand >

            totalElements?:   number

        }

        type CustomDynamicPageOutputDTOgoods_city_rel = {

            items?:    Array < goods_city_rel >

            totalElements?:   number

        }

        type CustomDynamicPageOutputDTOgoods_distribution = {

            items?:    Array < goods_distribution >

            totalElements?:   number

        }

        type CustomDynamicPageOutputDTOgoods = {

            items?:    Array < goods >

            totalElements?:   number

        }

        type CustomDynamicPageOutputDTOspu_goods = {

            items?:    Array < spu_goods >

            totalElements?:   number

        }

        type CustomDynamicPageOutputDTOstore_goods_rel = {

            items?:    Array < store_goods_rel >

            totalElements?:   number

        }

        type CustomDynamicPageOutputDTOsvip_goods_extend = {

            items?:    Array < svip_goods_extend >

            totalElements?:   number

        }

        type CustomDynamicPageOutputDTOsvip_goods_goods_rel = {

            items?:    Array < svip_goods_goods_rel >

            totalElements?:   number

        }

        type DeductionGoods = {

            goodsSn?:   string

            stockNum?:   number

        }

        type DeductionSaleStockReqDTO = {

            channelId?:   number

            goodsList?:    Array < DeductionGoods >

            organizationCode?:   string

        }

        type DeleteChannelGoodsDTO = {

            channelId?:   number

            organizationCode?:   string

        }

        type DeleteCommandDTO = {

            collect?:   string

            deleteMany?:   boolean

            query?:   any

        }

        type DeleteUnPostReqDTO = {

            goodsIds?:    Array < number >

        }

        type DetailImg = {

            sort?:   number

            thirdPartyID?:   number

            url?:   string

        }

        type DictionaryAddReqDTO = {

            id?:   string

            name?:   string

            operator?:   string

            valueInfo?:    Array < ValueInfo >

            valueType?:   number

        }

        type DictionaryListReqDTO = {

            id?:   string

        }

        type DictionaryListRespDTO = {

            id?:   string

            name?:   string

            valueInfo?:    Array < ValueInfo >

            valueType?:   number

        }

        type DictionaryUpdateReqDTO = {

            id?:   string

            isDeleted?:   boolean

            name?:   string

            operator?:   string

            valueInfo?:    Array < ValueInfo >

        }

        type Distribution = {

            area?:   string

            cityDelayJudgeTimeList?:    Array < CityDelayJudgeTime >

            cityList?:    Array < City >

            createTime?:   string

            deleted?:   boolean

            deliveryCenterIdCode?:   string

            depositoryCode?:   string

            deptCode?:   string

            id?:   number

            isSupportImport?:   string

            isSupportVip?:   string

            lastUpdate?:   string

            superVipDepositoryCode?:   string

        }

        type DocumentGoodsDetailReqDTO = {

            associationList?:    Array < BaseGoodsDetailReqDTO >

            goodsId?:   number

            goodsName?:   string

            goodsSn?:   string

            goodsType?:   number

            quantity?:   number

            specDesc?:   string

        }

        type ESGoodsChannelDTO = {

            channelId?:   number

            goodsDescription?:   string

            goodsId?:   string

            goodsName?:   string

            goodsSn?:   string

            linePrice?:   number

            mainImgUrl?:   string

            onSaleStatusAt?:   string

            organizationCode?:   string

            purchaseLimit?:   number

            retailPrice?:   number

            saleStatus?:   number

            specDesc?:   string

            vendibleQuantity?:   number

        }

        type ErpGoods = {

            spuCode?:   string

            spuName?:   string

        }

        type ErpGoodsPriceDTO = {

            deliveryPrice?:   number

            goodsSn?:   string

            heartDiffBalance?:   number

            maximumPrice?:   number

            memberDiffBalance?:   number

            minimumPrice?:   number

            operator?:   string

            organizationCode?:   string

            retailDiffBalance?:   number

            retailPrice?:   number

            retailUnitCode?:   string

            retailUnitConvertRate?:   number

            returnPrice?:   number

            vipDiscAmt?:   number

        }

        type ErpGoodsPriceRenewalDTO = {

            centralCode?:   string

            goodsPrice?:   number

            goodsSn?:   string

        }

        type ErpStoreGoodsStockInfoDTO = {

            allocationQty?:   number

            availableQty?:   number

            depotCode?:   string

            depotName?:   string

            goodsCode?:   string

            goodsName?:   string

            onHandQty?:   number

            realPrice?:   number

            storeCode?:   string

        }

        type EshopDaoRespDTO = {

            data?:   any

            insert?:   boolean

            insertIdList?:    Array < any >

            keyProperty?:   string

            pageNumber?:   number

            totalElements?:   number

        }

        type EshopSaleStockGoodsAuthorizationDTO = {

            goodsId?:   number

            storeId?:   number

        }

        type ExampleInputDTO = {

            filter?:    Array < CriteriaInputDTO >

            limit?:   number

            needFieldList?:    Array < string >

            needTotalCount?:   boolean

            offset?:   number

            orderBy?:    Array < SqlOrderInoutDTO >

        }

        type ExampleSimpleInputDTO = {

            filter?:   any

            limit?:   number

            needFieldList?:    Array < string >

            needTotalCount?:   boolean

            offset?:   number

            orderBy?:    Array < SqlOrderInoutDTO >

        }

        type FindCommandDTO = {

            collect?:   string

            findMany?:   boolean

            pageNumber?:   number

            pageSize?:   number

            query?:   any

            sort?:   any

            sorts?:    Array < JSONObject >

        }

        type FrontCategoryGoodsOrderDTO = {

            frontCategory?:   string

            orderLevel?:   number

        }

        type FrontOrgChannelGoodsWebRespDTO = {

            channelId?:   number

            frontCategoryRelation?:    Array < OrgCategoryGoodsDTO >

            goodsId?:   string

            goodsLevel?:   string

            goodsSn?:   string

            heartPrice?:   number

            isRecommended?:   boolean

            mainImg?:   string

            memberPrice?:   number

            name?:   string

            namePrefix?:   string

            organizationId?:   string

            postStatus?:   number

            price?:   number

            resourceName?:   string

            retailPrice?:   number

            retailUnitName?:   string

            saleStatus?:   number

            saleType?:   number

            sourceUrl?:   string

            specDesc?:   string

            specWeight?:   number

        }

        type FusionGoodsPriceDTO = {

            channelId?:   number

            goodsSn?:   string

            heartPrice?:   number

            memberPrice?:   number

            mnemonicCode?:   string

            operator?:   string

            organizationCode?:   string

            retailPrice?:   number

        }

        type GoodsAssociationDTO = {

            associationGroupId?:   number

            basicGoodsSn?:   string

            dosage?:   number

            erpCoefficient?:   string

        }

        type GoodsBasicReleaseAllDTO = {

            goodsSnList?:    Array < string >

            operator?:   string

            organizationCode?:   string

        }

        type GoodsCategoryCityRelDTO = {

            cityId?:   number

            createdAt?:   string

            goodsCategoryId?:   number

            id?:   number

            priority?:   number

            status?:   string

            updatedAt?:   string

        }

        type GoodsCategoryRelDis = {

            distributionId?:   number

            goodsCategoryId?:   number

            goodsCategoryIdList?:    Array < number >

            goodsId?:   number

            id?:   number

        }

        type GoodsChannelDTO = {

            abbreviation?:   string

            activityCommissionRatio?:   number

            associationList?:    Array < GoodsAssociationDTO >

            attrDesc?:   string

            barcode?:   string

            brand?:   string

            buyerRecommend?:   string

            channelId?:   number

            createdAt?:   string

            customRetailPrice?:   number

            defaultCommissionRatio?:   number

            effectiveDateEnd?:   string

            effectiveDateStart?:   string

            eshopGoodsId?:   number

            frontCategoryList?:    Array < GoodsFrontCategoryDTO >

            fruitType?:   number

            goodsCombinationAssociationGroupId?:   number

            goodsDescription?:   string

            goodsId?:   string

            goodsLevel?:   string

            goodsName?:   string

            goodsRestrictions?:   number

            goodsSn?:   string

            groupNumber?:   number

            heartPrice?:   number

            incomePrice?:   number

            incomeRatio?:   number

            isMention?:   number

            isRecommended?:   number

            linePrice?:   number

            memberPrice?:   number

            namePrefix?:   string

            nutritionalValue?:   string

            onSaleStatusAt?:   string

            organizationCode?:   string

            organizationType?:   string

            outerPackageSn?:   string

            outputTaxRate?:   number

            placeOrigin?:   string

            purchaseLimit?:   number

            purchaseRestrictions?:   number

            resourceList?:    Array < GoodsResourceDTO >

            retailPrice?:   number

            saleStatus?:   number

            saleType?:   number

            shareTitle?:   string

            shelfLife?:   number

            shortId?:   number

            sortVal?:   number

            specDesc?:   string

            specWeight?:   number

            storageMethod?:   string

            subtitle?:   string

            supplierAbbreviation?:   string

            supplierName?:   string

            takeawayAttr?:   string

            tips?:   string

            updatedAt?:   string

        }

        type GoodsCombinationAssociationDto = {

            basicGoodsName?:   string

            basicGoodsSn?:   string

            dosage?:   string

            groupId?:   number

            saleType?:   number

            shelfLife?:   number

        }

        type GoodsCombinationAssociationReqDTO = {

            basicGoodsSn?:   string

            dosage?:   number

            id?:   number

            saleType?:   number

            shelfLife?:   number

        }

        type GoodsCombinationAssociationRespDTO = {

            basicGoodsName?:   string

            basicGoodsSn?:   string

            dosage?:   number

            id?:   number

            retailUnitName?:   string

            saleType?:   number

            shelfLife?:   number

        }

        type GoodsCriteria = {

            distinctBySku?:   boolean

            goodsIdList?:    Array < string >

            goodsLevelList?:    Array < string >

            goodsSnList?:    Array < string >

            goodsSnapIdList?:    Array < string >

            goodsSpuIdList?:    Array < string >

            goodsSpusnList?:    Array < string >

            goodsStatusList?:    Array < string >

            goodsTypeList?:    Array < string >

            isGroupSupport?:   string

            isNeedReturnSpuInfo?:   string

            isNewCustomerAvailable?:   number

            isSpecGroups?:   string

            isVipGoods?:   string

            onlineOfflineStatusList?:    Array < string >

            price?:   string

            publishStatusList?:    Array < string >

            saleTypeList?:    Array < string >

            takeawayAttrList?:    Array < string >

        }

        type GoodsCustomInventoryDTO = {

            channelId?:   number

            channelType?:   number

            endAt?:   string

            goodsShortId?:   number

            goodsSn?:   string

            inventory?:   number

            operator?:   string

            organizationCode?:   string

            startAt?:   string

        }

        type GoodsCustomRetailPriceDTO = {

            channelId?:   number

            customRetailPrice?:   number

            endAt?:   string

            goodsSn?:   string

            operator?:   string

            organizationCode?:   string

            startAt?:   string

        }

        type GoodsDeleteReqDTO = {

            channelIdList?:    Array < number >

            goodsSnList?:    Array < string >

            operationType?:   number

            operator?:   string

            organizationCode?:   string

        }

        type GoodsDetailReqDTO = {

            channelId?:   number

            goodsId?:   string

            goodsSn?:   string

            organizationCode?:   string

        }

        type GoodsDetailRespDTO = {

            abbreviation?:   string

            activityCommissionRatio?:   number

            allowedWriteOffDay?:   number

            associationList?:    Array < GoodsCombinationAssociationRespDTO >

            attrDesc?:   string

            barcode?:   string

            brand?:   string

            buyerRecommend?:   string

            channelFactorPrice?:   number

            channelId?:   number

            consumablesQuantity?:   number

            customInventory?:   number

            customInventoryEndAt?:   string

            customInventoryStartAt?:   string

            defaultCommissionRatio?:   number

            deliveryType?:   number

            effectiveDateEnd?:   string

            effectiveDateStart?:   string

            frontCategoryGoodsOrderList?:    Array < FrontCategoryGoodsOrderDTO >

            fruitType?:   number

            goodsDescription?:   string

            goodsId?:   string

            goodsLevel?:   string

            goodsPriceDTO?:  GoodsPriceDTO

            goodsRestrictions?:   number

            goodsSn?:   string

            goodsType?:   string

            importMark?:   string

            incomeRatio?:   number

            isDetachable?:   number

            isEliminate?:   number

            isMention?:   number

            isNew?:   number

            isPeel?:   number

            isRecommended?:   number

            isUploadAlipay?:   number

            isUploadWechat?:   number

            isUsable?:   number

            isWeigh?:   number

            itemTypeNew?:   number

            labelList?:    Array < LabelRespDTO >

            lifeStatus?:   number

            mainCategory?:   string

            materialGroupDosage?:   number

            materialGroupId?:   string

            materialGroupName?:   string

            materialGroupPrice?:   number

            meatRate?:   number

            meituanCategoryProperty?:   string

            minimumPurchaseQty?:   number

            mnemonicCode?:   string

            modulePrice?:   number

            name?:   string

            nameEn?:   string

            namePrefix?:   string

            nameTw?:   string

            nutritionalValue?:   string

            organizationCode?:   string

            outerPackageSn?:   string

            outputTaxRate?:   number

            placeOrigin?:   string

            purchaseLimit?:   number

            purchaseRestrictions?:   number

            resourceGroupId?:   string

            resourceList?:    Array < GoodsResourceRespDTO >

            saleStatus?:   number

            saleType?:   number

            shareTitle?:   string

            shelfLife?:   number

            shelfLifeUnit?:   string

            singleFruitWeight?:   number

            soldQuantity?:   number

            specDesc?:   string

            specWeight?:   number

            storageMethod?:   string

            storeSortingTips?:   string

            subtitle?:   string

            supplierAbbreviation?:   string

            supplierCode?:   string

            supplierName?:   string

            takeawayAttr?:   string

            thirdPartyCategoryCode?:   string

            tips?:   string

            unitDTO?:  GoodsUnitRespDTO

            vendibleQuantity?:   number

            volume?:   number

            warehouseCode?:   string

            weight?:   number

        }

        type GoodsFrontCategoryDTO = {

            categoryCode?:   string

            categoryName?:   string

            sortVal?:   number

        }

        type GoodsGenerateCodeReqDTO = {

            inputGoodsSn?:   string

            saleType?:   number

        }

        type GoodsInf = {

            abbreviation?:   string

            activityCommissionRatio?:   number

            allowedWriteOffDay?:   number

            associationList?:    Array < GoodsCombinationAssociationDto >

            attrDesc?:   string

            autoRecordCoefficient?:   string

            barcode?:   string

            brand?:   string

            brandID?:   number

            buyerRecommend?:   string

            channel?:   string

            createAt?:   string

            defaultCommissionRatio?:   number

            deliveryCycle?:   number

            deliveryDate?:   string

            deliveryNumber?:   number

            deliveryType?:   number

            effectiveDateEnd?:   string

            effectiveDateStart?:   string

            erpCoefficient?:   string

            eshopDeliveryType?:   string

            floatUpNumber?:   string

            fruitType?:   number

            goodsDescription?:   string

            goodsId?:   string

            goodsLevel?:   string

            goodsPriceDTO?:  GoodsPriceDto

            goodsRestrictions?:   number

            goodsSn?:   string

            goodsType?:   string

            groupPrice?:   number

            groupSupport?:   string

            importMark?:   string

            incomeRatio?:   number

            isCombined?:   string

            isCycle?:   string

            isDefaultPutAway?:   number

            isDeliveryOnly?:   number

            isDetachable?:   number

            isEliminate?:   number

            isGift?:   number

            isIce?:   number

            isMention?:   number

            isNew?:   number

            isNewCustomerAvailable?:   number

            isPeel?:   number

            isRecommended?:   number

            isShowAppWeight?:   number

            isSupportCoupon?:   string

            isSupportDisparityRefund?:   number

            isUploadAlipay?:   number

            isUploadWechat?:   number

            isUsable?:   number

            isVipGoods?:   string

            isWeigh?:   number

            itemTypeNew?:   number

            labelList?:    Array < LabelRespDTO >

            lifeStatus?:   number

            linePrice?:   number

            lowestSpecialPrice?:   string

            mainCategory?:   string

            meatRate?:   number

            minimumPurchaseQty?:   number

            mnemonicCode?:   string

            name?:   string

            nameEn?:   string

            namePrefix?:   string

            nameTw?:   string

            nutritionalValue?:   string

            operateRecommend?:   string

            operateRecommendTime?:   string

            organizationCode?:   string

            organizationName?:   string

            outerPackageSn?:   string

            outputTaxRate?:   number

            ownedStoreType?:   string

            packageType?:   number

            pickingTip?:   string

            placeOrigin?:   string

            preAdvice?:   string

            priceShowFormat?:   number

            purchaseLimit?:   number

            purchaseRestrictions?:   number

            quantity?:   string

            resourceList?:    Array < GoodsResourceDto >

            saleStatus?:   number

            saleType?:   number

            saleTypeName?:   string

            shareSpec?:   string

            shareTitle?:   string

            shareWeChatPic?:   string

            shelfLife?:   number

            shelfLifeUnit?:   string

            singleFruitWeight?:   number

            specDesc?:   string

            specWeight?:   number

            specialDefineMemberPrice?:   string

            specialPriceGenerateBySys?:   string

            storageMethod?:   string

            storeSortingTips?:   string

            subtitle?:   string

            supplierAbbreviation?:   string

            supplierName?:   string

            takeawayAttr?:   string

            tips?:   string

            unit?:   string

            unitDTO?:  GoodsUnitRespDTO

            updatedAt?:   string

            vipPriceType?:   number

            volume?:   number

            warehouseCode?:   string

            weight?:   number

        }

        type GoodsInfoReqDTO = {

            goodsIdList?:    Array < string >

        }

        type GoodsLabel = {

            createTime?:   string

            id?:   number

            lastUpdate?:   string

            name?:   string

            priority?:   number

            type?:   string

        }

        type GoodsLabelRelInfo = {

            createTime?:   string

            goodsID?:   number

            goodsLabel?:  GoodsLabel

            id?:   number

            labelID?:   number

            lastUpdate?:   string

        }

        type GoodsListReqDTO = {

            channelId?:   number

            organizationCode?:   string

        }

        type GoodsMemberPriceUpdateDTO = {

            channelId?:   number

            goodsSn?:   string

            memberPrice?:   number

            operator?:   string

            organizationCode?:   string

        }

        type GoodsPrice = {

            channelFactorPrice?:   number

            consumablesPrice?:   number

            createdAt?:   string

            createdOperator?:   string

            customRetailPrice?:   number

            customRetailPriceEndAt?:   string

            customRetailPriceStartAt?:   string

            deliveryPrice?:   number

            heartDiffBalance?:   number

            heartGrossProfitMargin?:   number

            heartPrice?:   number

            id?:   number

            isDeleted?:   boolean

            jingdongMemberPrice?:   number

            linePrice?:   number

            maximumGrossProfitMargin?:   number

            maximumPrice?:   number

            memberDiffBalance?:   number

            memberGrossProfitMargin?:   number

            memberPrice?:   number

            minimumPrice?:   number

            price?:   number

            retailDiffBalance?:   number

            retailGrossProfitMargin?:   number

            retailPrice?:   number

            retailReferencePrice?:   number

            returnPrice?:   number

            updatedAt?:   string

            updatedOperator?:   string

            version?:   number

        }

        type GoodsPriceCountDTO = {

            goodsSn?:   string

            heartPrices?:    Array < PriceCount >

            memberPrices?:    Array < PriceCount >

        }

        type GoodsPriceCountReqDTO = {

            channelIds?:    Array < number >

            goodsSns?:    Array < string >

            organizationTypes?:    Array < number >

        }

        type GoodsPriceDTO = {

            channelFactorPrice?:   number

            consumablesPrice?:   number

            customRetailPrice?:   number

            customRetailPriceEndAt?:   string

            customRetailPriceStartAt?:   string

            heartPrice?:   number

            jingdongMemberPrice?:   number

            linePrice?:   number

            maximumPrice?:   number

            memberPrice?:   number

            price?:   number

            realRetailPrice?:   number

            retailPrice?:   number

        }

        type GoodsPriceDto = {

            channelFactorPrice?:   number

            consumablesPrice?:   string

            customRetailPrice?:   string

            customRetailPriceEndAt?:   string

            customRetailPriceStartAt?:   string

            heartPrice?:   string

            memberPrice?:   string

            price?:   string

            retailPrice?:   string

        }

        type GoodsPriceReqDTO = {

            channelFactorPrice?:   number

            goodsSn?:   string

            heartDiffBalance?:   number

            memberDiffBalance?:   number

            retailDiffBalance?:   number

        }

        type GoodsPriceSyncReqDTO = {

            goodsSn?:   string

            organizationCode?:   string

        }

        type GoodsPriceUpdateDTO = {

            channelFactorPrice?:   number

            channelId?:   number

            goodsSn?:   string

            operator?:   string

            organizationCode?:   string

            organizationType?:   number

        }

        type GoodsPullAuthorizationReqDTO = {

            channelId?:   number

            goodsSn?:   string

            operator?:   string

            organizationCode?:   string

        }

        type GoodsRecommendationReqDTO = {

            channelId?:   number

            goodsSn?:   string

            isRecommended?:   number

            operator?:   string

            organizationCode?:   string

        }

        type GoodsRelDTO = {

            skuId?:   number

        }

        type GoodsReleaseReqDTO = {

            channelIdList?:    Array < number >

            goodsSnList?:    Array < string >

            operator?:   string

            organizationCode?:   string

        }

        type GoodsReq = {

            channelList?:    Array < string >

            criteria?:  GoodsCriteria

            isOnlyEshop?:   boolean

            organizationList?:    Array < Organization >

            pageNumber?:   number

            pageSize?:   number

            search?:  GoodsSearch

        }

        type GoodsResourceDTO = {

            proportion?:   string

            resourceGroupId?:   number

            sort?:   number

            type?:   number

            url?:   string

        }

        type GoodsResourceDto = {

            channelId?:   number

            groupId?:   number

            proportion?:   string

            sort?:   number

            type?:   number

            url?:   string

        }

        type GoodsResourceRespDTO = {

            keywords?:    Array < string >

            proportion?:   string

            sort?:   number

            type?:   number

            url?:   string

        }

        type GoodsSearch = {

            createTimeBegin?:   string

            createTimeEnd?:   string

            goodsName?:   string

            goodsSnapHistorieList?:    Array < GoodsSnapHistory >

        }

        type GoodsSearchReqDTO = {

            channelId?:   number

            keywords?:   string

            organizationCode?:   string

            resultNum?:   number

            saleStatus?:   number

        }

        type GoodsSnapHistory = {

        }

        type GoodsSnapshotDetailFilterQueryDTO = {

            channelId?:   number

            queryEshop?:   boolean

            snapshotIdList?:    Array < number >

        }

        type GoodsSnapshotIdQueryDTO = {

            channelId?:   number

            end?:   string

            goodsId?:   number

            isSkuId?:   boolean

            queryEshop?:   boolean

            start?:   string

        }

        type GoodsSnapshotRespDTO = {

            activityCommissionRatio?:   number

            activityPrice?:   number

            activityType?:   number

            allowedWriteOffDay?:   number

            autoRecordCoefficient?:   string

            barcode?:   string

            brand?:   string

            buyerRecommend?:   string

            channelFactorPrice?:   number

            channelId?:   number

            channelName?:   string

            cityDownStatus?:   string

            cityEffectNum?:   string

            combinationGoodsJson?:   string

            consumablesPrice?:   number

            createdAt?:   string

            customRetailPrice?:   number

            customRetailPriceEndAt?:   string

            customRetailPriceStartAt?:   string

            defaultCommissionRatio?:   number

            deliveryCycle?:   number

            deliveryNumber?:   number

            deliveryPrice?:   number

            deliveryType?:   number

            effectiveDateEnd?:   string

            effectiveDateStart?:   string

            englishName?:   string

            erpCoefficient?:   string

            fruitType?:   string

            goodsAbbreviation?:   string

            goodsAttrDesc?:   string

            goodsDescription?:   string

            goodsId?:   number

            goodsLevel?:   string

            goodsName?:   string

            goodsRestrictions?:   boolean

            goodsSn?:   string

            goodsSubtitle?:   string

            goodsType?:   string

            groupSupport?:   boolean

            headPic?:   string

            heartDiffBalance?:   number

            heartGrossProfitMargin?:   number

            heartPrice?:   number

            id?:   number

            importMark?:   string

            incomeRatio?:   number

            isActivity?:   boolean

            isCombination?:   boolean

            isCombined?:   boolean

            isCycle?:   boolean

            isDeliveryOnly?:   boolean

            isDetachable?:   boolean

            isEliminate?:   boolean

            isGift?:   boolean

            isLocked?:   boolean

            isMention?:   boolean

            isNew?:   boolean

            isNewCustomerAvailable?:   boolean

            isPeel?:   boolean

            isPre?:   boolean

            isRecommended?:   boolean

            isSupportCoupon?:   boolean

            isUploadAlipay?:   boolean

            isUploadWechat?:   boolean

            isUsable?:   boolean

            isVipGoods?:   boolean

            isWeigh?:   boolean

            lowestSpecialPrice?:   string

            mainCategory?:   string

            mainImgUrl?:   string

            maximumGrossProfitMargin?:   number

            maximumPrice?:   number

            meatRate?:   number

            memberDiffBalance?:   number

            memberGrossProfitMargin?:   number

            memberPrice?:   number

            minimumPrice?:   number

            minimumPurchaseQty?:   number

            mnemonicCode?:   string

            nutritionalValue?:   string

            onSaleStatusAt?:   string

            organizationCode?:   string

            organizationType?:   number

            outerPackageSn?:   string

            outputTaxRate?:   number

            placeOrigin?:   string

            postStatus?:   boolean

            preAdvice?:   string

            price?:   number

            purchaseLimit?:   number

            purchaseRestrictions?:   boolean

            retailDiffBalance?:   number

            retailGrossProfitMargin?:   number

            retailPrice?:   number

            retailReferencePrice?:   number

            retailUnitCode?:   string

            retailUnitConvertRate?:   number

            retailUnitName?:   string

            returnPrice?:   number

            saleStatus?:   number

            saleType?:   number

            shareTitle?:   string

            shareWechatPic?:   string

            shelfLife?:   number

            shelfLifeUnit?:   string

            singleFruitWeight?:   number

            skuId?:   number

            specDesc?:   string

            specWeight?:   number

            storageMethod?:   string

            storeSaleStatus?:   string

            storeSortingTips?:   string

            storeStockBorder?:   number

            supplierAbbreviation?:   string

            supplierCode?:   string

            supplierName?:   string

            takeawayAttr?:   string

            taxCode?:   string

            tips?:   string

            traditionalName?:   string

            updateTimestamp?:   string

            volume?:   number

            warehouseCode?:   string

            weight?:   number

        }

        type GoodsSynchronizeInfoReqDTO = {

            batchPolicy?:   string

            businessCircleMustDesc?:   string

            deliverableCycle?:   string

            difference?:   string

            fruitEvaluation?:   string

            goodsSn?:   string

            isNew?:   number

            minimumOrderQty?:   number

            mustFruit?:   string

            operator?:   string

            organizationCode?:   string

            qualityStatusDesc?:   string

            shoppingGuide?:   string

            singleFruitWeight?:   number

            sourceStatus?:   number

            tasteIndex?:   string

            transportStorageType?:   string

            trend?:   string

        }

        type GoodsSynchronizePriceReqDTO = {

            deliveryPrice?:   number

            goodsSn?:   string

            goodsSnList?:    Array < string >

            heartDiffBalance?:   number

            maximumPrice?:   number

            memberDiffBalance?:   number

            minimumPrice?:   number

            operator?:   string

            organizationCode?:   string

            organizationCodeList?:    Array < string >

            retailDiffBalance?:   number

            returnPrice?:   number

        }

        type GoodsTemplateDeleteReqDTO = {

            goodsSnList?:    Array < string >

            idList?:    Array < number >

            saleType?:   number

        }

        type GoodsTemplateDetailReqDTO = {

            goodsSn?:   string

        }

        type GoodsTemplateDuplicateReqDTO = {

            endDate?:   string

            startDate?:   string

        }

        type GoodsTemplateListReqDTO = {

            goodsSns?:    Array < string >

            isEshopGoods?:   boolean

            keyword?:   string

            mainCategory?:   string

            mnemonicCode?:   string

            organizationCodes?:    Array < string >

            pageNumber?:   number

            pageSize?:   number

            saleType?:   number

        }

        type GoodsTemplateListRespDTO = {

            categoryName?:   string

            eshopGoodsId?:   number

            fruitType?:   number

            goodsLevel?:   string

            goodsSn?:   string

            id?:   number

            mainCategory?:   string

            mainImgUrl?:   string

            meatRate?:   number

            mnemonicCode?:   string

            name?:   string

            organizationCodes?:    Array < string >

            retailUnitConvertRate?:   number

            retailUnitName?:   string

            saleType?:   number

            specDesc?:   string

            specWeight?:   number

        }

        type GoodsTemplateReqDTO = {

            abbreviation?:   string

            activityCommissionRatio?:   number

            associationList?:    Array < GoodsCombinationAssociationReqDTO >

            attrDesc?:   string

            autoRecordCoefficient?:   string

            barcode?:   string

            brand?:   string

            defaultCommissionRatio?:   number

            deliveryCycle?:   number

            deliveryDate?:   string

            deliveryNumber?:   number

            distributionCenter?:   string

            effectiveDateEnd?:   string

            effectiveDateStart?:   string

            erpCoefficient?:   string

            eshopDeliveryType?:   string

            eshopGoodsId?:   number

            floatUpNumber?:   string

            fruitType?:   number

            goodsLevel?:   string

            goodsSn?:   string

            goodsType?:   string

            groupPrice?:   number

            groupSupport?:   string

            id?:   number

            importMark?:   string

            isCombined?:   string

            isCycle?:   string

            isDefaultPutAway?:   number

            isDeliveryOnly?:   number

            isDetachable?:   number

            isEliminate?:   number

            isGift?:   number

            isIce?:   number

            isMention?:   number

            isNew?:   number

            isNewCustomerAvailable?:   number

            isPeel?:   number

            isRecommended?:   number

            isShowAppWeight?:   number

            isSupportCoupon?:   string

            isSupportDisparityRefund?:   number

            isUploadAlipay?:   number

            isUploadWechat?:   number

            isUsable?:   number

            isVipGoods?:   string

            isWeigh?:   number

            labelGroupId?:   number

            labelList?:    Array < LabelReqDTO >

            lowestSpecialPrice?:   string

            mainCategory?:   string

            materialGroupDosage?:   number

            materialGroupId?:   number

            materialGroupName?:   string

            materialGroupPrice?:   number

            meatRate?:   number

            meituanCategoryProperty?:   string

            mnemonicCode?:   string

            name?:   string

            nameEn?:   string

            nameTw?:   string

            nutritionalValue?:   string

            operateRecommend?:   string

            operateRecommendTime?:   string

            operator?:   string

            outerPackageSn?:   string

            outputTaxRate?:   number

            ownedStoreType?:   string

            packageType?:   number

            pickingTip?:   string

            placeOrigin?:   string

            preAdvice?:   string

            priceShowFormat?:   number

            quantity?:   string

            resourceGroupId?:   string

            saleType?:   number

            shareSpec?:   string

            shareTitle?:   string

            shareWeChatPic?:   string

            shelfLife?:   number

            shelfLifeUnit?:   string

            specDesc?:   string

            specWeight?:   number

            specialDefineMemberPrice?:   string

            specialPriceGenerateBySys?:   string

            storageMethod?:   string

            storeSortingTips?:   string

            subtitle?:   string

            supplierAbbreviation?:   string

            supplierCode?:   string

            supplierName?:   string

            takeawayAttr?:   string

            tips?:   string

            unit?:   string

            unitReqDTO?:  GoodsUnitReqDTO

            vipPriceType?:   number

            volume?:   number

            weight?:   number

        }

        type GoodsTemplateResourceRespDTO = {

            type?:   number

            url?:   string

        }

        type GoodsTemplateRespDTO = {

            abbreviation?:   string

            associationList?:    Array < GoodsCombinationAssociationRespDTO >

            attrDesc?:   string

            barcode?:   string

            brand?:   string

            fruitType?:   number

            goodsLevel?:   string

            goodsSn?:   string

            goodsType?:   string

            id?:   number

            importMark?:   string

            isDetachable?:   number

            isEliminate?:   number

            isMention?:   number

            isNew?:   number

            isPeel?:   number

            isRecommended?:   number

            isUploadAlipay?:   number

            isUploadWechat?:   number

            isUsable?:   number

            isWeigh?:   number

            labelGroupId?:   number

            labelList?:    Array < LabelRespDTO >

            mainCategory?:   string

            materialGroupDosage?:   number

            materialGroupId?:   string

            materialGroupName?:   string

            materialGroupPrice?:   number

            meatRate?:   number

            meituanCategoryProperty?:   string

            mnemonicCode?:   string

            name?:   string

            nameEn?:   string

            nameTw?:   string

            nutritionalValue?:   string

            outerPackageSn?:   string

            outputTaxRate?:   number

            placeOrigin?:   string

            resourceGroupId?:   string

            resourceList?:    Array < GoodsTemplateResourceRespDTO >

            saleType?:   number

            shareTitle?:   string

            shelfLife?:   number

            shelfLifeUnit?:   string

            specDesc?:   string

            specWeight?:   number

            storageMethod?:   string

            storeSortingTips?:   string

            subtitle?:   string

            takeawayAttr?:   string

            tips?:   string

            unitRespDTO?:  GoodsUnitRespDTO

            volume?:   number

            weight?:   number

        }

        type GoodsUnionKeyReqDTO = {

            channelId?:   number

            goodsSn?:   string

            organizationCode?:   string

        }

        type GoodsUniqueKeyReqDTO = {

            channelId?:   number

            goodsSn?:   string

            organizationCode?:   string

        }

        type GoodsUniqueReqDTO = {

            channelId?:   number

            goodsSn?:   string

            organizationCode?:   string

        }

        type GoodsUnitReqDTO = {

            basicUnitCode?:   string

            basicUnitConvertRate?:   number

            basicUnitName?:   string

            id?:   number

            purchaseUnitCode?:   string

            purchaseUnitConvertRate?:   number

            purchaseUnitName?:   string

            retailUnitCode?:   string

            retailUnitConvertRate?:   number

            retailUnitName?:   string

            saleUnitCode?:   string

            saleUnitConvertRate?:   number

            saleUnitName?:   string

            stockUnitCode?:   string

            stockUnitConvertRate?:   number

            stockUnitName?:   string

        }

        type GoodsUnitRespDTO = {

            basicUnitCode?:   string

            basicUnitConvertRate?:   number

            basicUnitName?:   string

            id?:   number

            purchaseUnitCode?:   string

            purchaseUnitConvertRate?:   number

            purchaseUnitName?:   string

            retailUnitCode?:   string

            retailUnitConvertRate?:   number

            retailUnitName?:   string

            saleUnitCode?:   string

            saleUnitConvertRate?:   number

            saleUnitName?:   string

            stockUnitCode?:   string

            stockUnitConvertRate?:   number

            stockUnitName?:   string

        }

        type GoodsUpdateBasicReqDTO = {

            associationList?:    Array < GoodsCombinationAssociationReqDTO >

            goodsSn?:   string

            meatRate?:   number

            modulePrice?:   number

            operator?:   string

            organizationCodeList?:    Array < string >

            singleFruitWeight?:   number

            specWeight?:   number

            warehouseCode?:   string

        }

        type GoodsUpdateChannelReqDTO = {

            activityCommissionRatio?:   number

            allowedWriteOffDay?:   number

            attrDesc?:   string

            brand?:   string

            buyerRecommend?:   string

            channelId?:   number

            defaultCommissionRatio?:   number

            deliveryType?:   number

            effectiveDateEnd?:   string

            effectiveDateStart?:   string

            goodsAbbreviation?:   string

            goodsDescription?:   string

            goodsName?:   string

            goodsRestrictions?:   number

            goodsSn?:   string

            incomeRatio?:   number

            isActivity?:   number

            isMention?:   number

            isRecommended?:   number

            materialGroupDosage?:   number

            materialGroupId?:   number

            materialGroupName?:   string

            materialGroupPrice?:   number

            minimumPurchaseQty?:   number

            namePrefix?:   string

            operator?:   string

            organizationCode?:   string

            placeOrigin?:   string

            purchaseLimit?:   number

            purchaseRestrictions?:   number

            resourceGroupId?:   number

            shareTitle?:   string

            shelfLife?:   number

            specDesc?:   string

            storageMethod?:   string

            subtitle?:   string

            supplierAbbreviation?:   string

            supplierCode?:   string

            supplierName?:   string

            takeawayAttr?:   string

            tips?:   string

        }

        type GoodsUpdatePriceReqDTO = {

            channelIdList?:    Array < number >

            goodsPriceList?:    Array < GoodsPriceReqDTO >

            operationType?:   number

            operator?:   string

            organizationCode?:   string

        }

        type GoodsUpdateSaleStatusReqDTO = {

            channelIdList?:    Array < number >

            channelType?:   number

            goodsShortId?:   number

            goodsSnList?:    Array < string >

            operator?:   string

            organizationCode?:   string

            saleStatus?:   number

        }

        type GoodsVipPriceRequest = {

            id?:   number

            operationType?:   number

            vipPrice?:   string

            vipPriceGenerateBySys?:   number

        }

        type GoodsWarehouseStockRespDTO = {

            availableQuantity?:   number

            goodsId?:   string

            goodsName?:   string

            goodsSn?:   string

            organizationCode?:   string

            organizationName?:   string

            totalStock?:   number

            usedQuantity?:   number

        }

        type ImageSet = {

            carouselImageList?:    Array < Images >

            detailHeadImageList?:    Array < Images >

            detailImageList?:    Array < Images >

            groupId?:   number

            isSelected?:   number

        }

        type Images = {

            createTime?:   string

            groupName?:   string

            httpUrl?:   string

            id?:   number

            lastUpdate?:   string

            level?:   string

            maxHeight?:   string

            maxSize?:   string

            maxWidth?:   string

            onwerID?:   number

            priority?:   number

            remoteFileName?:   string

            status?:   string

        }

        type ImgInfo = {

            sort?:   number

            thirdPartyID?:   number

            url?:   string

        }

        type InsertCommandDTO = {

            collect?:   string

            documents?:    Array < JSONObject >

        }

        type JSONObject = {

        }

        type Label = {

            createdAt?:   string

            createdOperator?:   string

            groupId?:   number

            id?:   number

            isDeleted?:   boolean

            jsonStr?:   string

            keyword?:   string

            labelCode?:   string

            priority?:   number

            resourceId?:   string

            skuId?:   number

            type?:   number

            updatedAt?:   string

            updatedOperator?:   string

            version?:   number

        }

        type LabelReqDTO = {

            id?:   number

            keyword?:   string

            labelCode?:   string

        }

        type LabelRespDTO = {

            id?:   number

            keyword?:   string

            labelCode?:   string

        }

        type LightBasicGoodsDetailDTO = {

            basicGoodsId?:   string

            basicGoodsName?:   string

            basicGoodsSaleType?:   number

            basicGoodsSn?:   string

            channelId?:   number

            defaultBasicGoodsName?:   string

            dosage?:   number

            erpCoefficient?:   string

            organizationCode?:   string

            retailUnitCode?:   string

            retailUnitName?:   string

            shelfLife?:   number

            specDesc?:   string

            specWeight?:   number

            weight?:   number

        }

        type LightGoodsDetailDTO = {

            association?:    Array < LightBasicGoodsDetailDTO >

            channelId?:   number

            defaultGoodsName?:   string

            goodsId?:   string

            goodsName?:   string

            goodsSn?:   string

            organizationCode?:   string

            retailUnitCode?:   string

            retailUnitName?:   string

            saleType?:   number

            specDesc?:   string

            specWeight?:   number

            weight?:   number

        }

        type MainImg = {

            proportion?:   string

            thirdPartyID?:   number

            url?:   string

        }

        type MapstringListGoodsLabel = {

        }

        type MarketingBatchListReqDto = {

            skuNumber?:    Array < string >

        }

        type MarketingBatchListRespDto = {

            goodsSpecialPrice?:   string

            id?:   string

            skuNumber?:   string

        }

        type MarketingGoodsRespDto = {

            couponStacking?:   number

            dayAmount?:   number

            goodsBidPrice?:   string

            goodsCode?:   string

            goodsId?:   number

            goodsName?:   string

            goodsSpecialPrice?:   string

            goodsVipPrice?:   string

            isSupportCoupon?:   number

            onceLimit?:   number

            skuNumber?:   string

            totalAmount?:   number

        }

        type MarketingPageQueryReqDto = {

            activityEndDate?:   string

            activityEndTime?:   string

            activityStartDate?:   string

            activityStartTime?:   string

            activityStatus?:   number

            distributionCode?:   string

            id?:   number

            operaterName?:   string

            pageNumber?:   number

            pageSize?:   number

            skuNumber?:   string

            specialShow?:   number

            status?:   string

            stopper?:   string

        }

        type MarketingPageQueryRespDto = {

            activityEndDate?:   string

            activityEndTime?:   string

            activityName?:   string

            activityStartDate?:   string

            activityStartTime?:   string

            activityStatus?:   number

            distributionName?:   string

            id?:   number

            lastUpdateTime?:   string

            operaterName?:   string

            specialShow?:   number

        }

        type MarketingQueryRespDto = {

            activityEndDate?:   string

            activityEndTime?:   string

            activityName?:   string

            activityStartDate?:   string

            activityStartTime?:   string

            channel?:   string

            code?:   string

            countDown?:   number

            countDownShow?:   number

            countDownUnit?:   string

            distributionCode?:   string

            distributionId?:   number

            goodsInfoList?:    Array < MarketingGoodsRespDto >

            id?:   number

            singleDayLimit?:   number

            specialShow?:   number

            totalLimit?:   number

        }

        type MarketingSaveActivityReqDto = {

            activityEndDate?:   string

            activityEndTime?:   string

            activityName?:   string

            activityStartDate?:   string

            activityStartTime?:   string

            channel?:   string

            countDown?:   number

            countDownShow?:   number

            countDownUnit?:   string

            distributionCode?:   string

            distributionName?:   string

            goodsInfoList?:    Array < MarketingGoodsRespDto >

            operaterDeptCode?:   string

            operaterName?:   string

            operator?:   string

            singleDayLimit?:   number

            specialShow?:   number

            totalLimit?:   number

        }

        type MaterialGroupListDTO = {

            name?:   string

        }

        type MaterialGroupRespDTO = {

            groupId?:   string

            groupName?:   string

        }

        type MeituanCategoryPropertyReqDTO = {

            categoryCode?:   string

        }

        type MeituanCategoryPropertyValueReqDTO = {

            keyword?:   string

            queryType?:   number

        }

        type MtGoodsAssociationDTO = {

            associationGroupId?:   number

            basicGoodsSn?:   string

            dosage?:   number

            erpCoefficient?:   string

        }

        type MtGoodsChannelDTO = {

            abbreviation?:   string

            activityCommissionRatio?:   number

            associationList?:    Array < MtGoodsAssociationDTO >

            attrDesc?:   string

            barcode?:   string

            brand?:   string

            buyerRecommend?:   string

            channelId?:   number

            createdAt?:   string

            customRetailPrice?:   number

            defaultCommissionRatio?:   number

            effectiveDateEnd?:   string

            effectiveDateStart?:   string

            frontCategoryList?:    Array < MtGoodsFrontCategoryDTO >

            fruitType?:   number

            goodsCombinationAssociationGroupId?:   number

            goodsDescription?:   string

            goodsId?:   string

            goodsLevel?:   string

            goodsName?:   string

            goodsRestrictions?:   number

            goodsSn?:   string

            heartPrice?:   number

            incomePrice?:   number

            incomeRatio?:   number

            isMention?:   number

            isRecommended?:   number

            linePrice?:   number

            memberPrice?:   number

            namePrefix?:   string

            nutritionalValue?:   string

            onSaleStatusAt?:   string

            organizationCode?:   string

            organizationType?:   string

            outerPackageSn?:   string

            outputTaxRate?:   number

            placeOrigin?:   string

            purchaseLimit?:   number

            purchaseRestrictions?:   number

            resourceList?:    Array < MtGoodsResourceDTO >

            retailPrice?:   number

            saleStatus?:   number

            saleType?:   number

            shareTitle?:   string

            shelfLife?:   number

            shortId?:   number

            sortVal?:   number

            specDesc?:   string

            specWeight?:   number

            storageMethod?:   string

            subtitle?:   string

            supplierAbbreviation?:   string

            supplierName?:   string

            takeawayAttr?:   string

            tips?:   string

            updatedAt?:   string

        }

        type MtGoodsFrontCategoryDTO = {

            categoryCode?:   string

            categoryName?:   string

            sortVal?:   number

        }

        type MtGoodsInfoReqDTO = {

            goodsIdList?:    Array < string >

        }

        type MtGoodsResourceDTO = {

            proportion?:   string

            resourceGroupId?:   number

            sort?:   number

            type?:   number

            url?:   string

        }

        type MtGoodsUniqueReqDTO = {

            channelId?:   number

            goodsSn?:   string

            organizationCode?:   string

        }

        type OrgCategoryCreateWebDTO = {

            categoryName?:   string

            categoryType?:   number

            channelId?:   number

            operator?:   string

            organizationCode?:   string

            parentCategoryCode?:   string

            sortVal?:   number

        }

        type OrgCategoryDelReqDTO = {

            categoryCodeList?:    Array < string >

            categoryType?:   number

            channelId?:   number

            operator?:   string

            organizationCode?:   string

        }

        type OrgCategoryGoodsDTO = {

            caVersion?:   number

            categoryCode?:   string

            categoryName?:   string

            goodsId?:   string

            orderLevel?:   number

            relationId?:   string

            sortVal?:   number

            updateAt?:   string

        }

        type OrgCategoryGoodsDelReqDTO = {

            categoryCode?:   string

            channelId?:   number

            goodsSnList?:    Array < string >

            operator?:   string

            organizationCode?:   string

            storeType?:   number

        }

        type OrgCategoryGoodsRelationDTO = {

            categoryCode?:   string

            channelId?:   number

            goodsList?:    Array < BaseGoodsDTO >

            operator?:   string

            organizationCode?:   string

            storeType?:   number

        }

        type OrgCategoryNodeDTO = {

            associateFrontCategory?:    Array < string >

            associateSystemCategory?:    Array < string >

            associateThirdCategory?:    Array < string >

            categoryCode?:   string

            channelId?:   number

            children?:    Array < CategoryNodeDTO >

            createdAt?:   string

            goodsNum?:   number

            name?:   string

            organizationCode?:   string

            sortVal?:   number

            thirdPartyCategoryCode?:   string

            thirdPartyChannelCode?:   string

            updatedAt?:   string

            version?:   number

        }

        type OrgCategoryQueryReqDTO = {

            categoryCode?:   string

            channelId?:   number

            organizationCode?:   string

            organizationType?:   number

        }

        type OrgCategoryReqDTO = {

            channelId?:   number

            organizationCode?:   string

        }

        type OrgCategoryRespDTO = {

            categoryCode?:   string

            categoryName?:   string

            children?:    Array < OrgCategoryRespDTO >

            isRoot?:   boolean

            parentCategoryCode?:   string

            sortVal?:   number

        }

        type OrgCategoryTreeQueryReqDTO = {

            category?:   string

            channelId?:   number

            organizationCode?:   string

            storeType?:   number

        }

        type OrgCategoryUpdateWebDTO = {

            categoryCode?:   string

            categoryName?:   string

            categoryType?:   number

            channelId?:   number

            operator?:   string

            organizationCode?:   string

            sortVal?:   number

        }

        type OrgChannelCategoryGoodsWebReqDTO = {

            categoryCode?:   number

            channelId?:   number

            organizationCode?:   string

        }

        type OrgChannelCategoryGoodsWebRespDTO = {

            categoryCode?:   number

            channelId?:   number

            goodsLevel?:   string

            goodsName?:   string

            goodsSn?:   string

            heartPrice?:   number

            isRecommended?:   number

            mainImgUrl?:   string

            memberPrice?:   number

            namePrefix?:   string

            organizationCode?:   string

            price?:   number

            retailPrice?:   number

            retailUnitName?:   string

            saleStatus?:   number

            specDesc?:   string

            specWeight?:   number

        }

        type OrgChannelGoodsRespDTO = {

            channelFactorPrice?:   number

            channelId?:   number

            customInventory?:   number

            customInventoryEndAt?:   string

            customInventoryStartAt?:   string

            deliveryPrice?:   number

            eshopGoodsId?:   number

            frontCategoryRelation?:    Array < OrgCategoryGoodsDTO >

            goodsCreateAt?:   string

            goodsId?:   string

            goodsLevel?:   string

            goodsName?:   string

            goodsShortId?:   number

            goodsSn?:   string

            groupNumber?:   number

            heartPrice?:   number

            incomeRatio?:   number

            isCustomInventory?:   boolean

            isRecommended?:   boolean

            labelList?:    Array < LabelRespDTO >

            linePrice?:   number

            mainCategory?:   string

            mainImg?:   string

            mainImgId?:   string

            maximumPrice?:   number

            memberGrossProfitMargin?:   number

            memberPrice?:   number

            minimumPrice?:   number

            modulePrice?:   number

            name?:   string

            namePrefix?:   string

            onSaleOrgCodes?:    Array < string >

            onSaleStatusAt?:   string

            onSaleStoreNum?:   number

            organizationCode?:   string

            postStatus?:   number

            price?:   number

            purchaseLimit?:   number

            purchaseRestrictions?:   number

            releaseOrgCodes?:    Array < string >

            resourceGroupId?:   string

            resourceName?:   string

            retailGrossProfitMargin?:   number

            retailPrice?:   number

            retailUnitConvertRate?:   number

            retailUnitName?:   string

            saleStatus?:   number

            saleType?:   number

            shelvesTime?:   string

            singleFruitWeight?:   number

            soldQuantity?:   number

            sourceStatus?:   number

            sourceUrl?:   string

            specDesc?:   string

            specWeight?:   number

            spreadRate?:   number

            stock?:   number

            syncDesc?:   string

            syncStatus?:   number

            takeawayAttr?:   string

            timeSaleStatus?:   number

            updatedAt?:   string

        }

        type OrgLeafCategoryQueryDTO = {

            channelId?:   number

            organizationCode?:   string

            organizationType?:   number

            storeType?:   number

        }

        type Organization = {

            code?:   string

            type?:   string

        }

        type OrganizationRemoveReqDTO = {

            deleted?:   number

            organizationCodeList?:    Array < string >

        }

        type OrganizationReqDTO = {

            keyword?:   string

            level?:   number

            parentOrganizationCode?:   string

            type?:   number

        }

        type OrganizationRespDTO = {

            deleted?:   number

            id?:   number

            level?:   number

            organizationCode?:   string

            organizationName?:   string

            parentOrganizationCode?:   string

            type?:   number

        }

        type OrganizationSaveReqDTO = {

            createdOperator?:   string

            level?:   number

            organizationCode?:   string

            organizationName?:   string

            parentOrganizationCode?:   string

            type?:   number

        }

        type OrganizationTreeReqDTO = {

            goodsSn?:   string

            published?:   number

            rootCodeList?:    Array < string >

            storeFilter?:   number

            type?:   number

        }

        type OrganizationTreeRespDTO = {

            children?:    Array < OrganizationTreeRespDTO >

            id?:   number

            level?:   number

            organizationCode?:   string

            organizationName?:   string

            parentOrganizationCode?:   string

            published?:   number

            type?:   number

        }

        type OrganizationUpdateReqDTO = {

            level?:   number

            organizationCode?:   string

            organizationName?:   string

            parentOrganizationCode?:   string

            type?:   number

            updatedOperator?:   string

        }

        type PageWebRespDTOChannelListBeanRespDTO = {

            list?:    Array < ChannelListBeanRespDTO >

            pageNumber?:   number

            pageSize?:   number

            total?:   number

        }

        type PageWebRespDTOResourceGroupListBean = {

            list?:    Array < ResourceGroupListBean >

            pageNumber?:   number

            pageSize?:   number

            total?:   number

        }

        type PageableReqDTO = {

            channelId?:   number

            goodsNameKeyword?:   string

            goodsSnKeyword?:   string

            goodsSnSet?:    Array < string >

            organizationCode?:   string

            pageNumber?:   number

            pageSize?:   number

            stockDate?:   string

            unitType?:   number

            warehouseCode?:   string

        }

        type PageableRespDTOFrontOrgChannelGoodsWebRespDTO = {

            items?:    Array < FrontOrgChannelGoodsWebRespDTO >

            pageNumber?:   number

            pageSize?:   number

            totalElements?:   number

            totalPages?:   number

        }

        type PageableRespDTOGoodsChannelDTO = {

            items?:    Array < GoodsChannelDTO >

            pageNumber?:   number

            pageSize?:   number

            totalElements?:   number

            totalPages?:   number

        }

        type PageableRespDTOGoodsTemplateListRespDTO = {

            items?:    Array < GoodsTemplateListRespDTO >

            pageNumber?:   number

            pageSize?:   number

            totalElements?:   number

            totalPages?:   number

        }

        type PageableRespDTOGoodsWarehouseStockRespDTO = {

            items?:    Array < GoodsWarehouseStockRespDTO >

            pageNumber?:   number

            pageSize?:   number

            totalElements?:   number

            totalPages?:   number

        }

        type PageableRespDTOMarketingPageQueryRespDto = {

            items?:    Array < MarketingPageQueryRespDto >

            pageNumber?:   number

            pageSize?:   number

            totalElements?:   number

            totalPages?:   number

        }

        type PageableRespDTOOrgChannelGoodsRespDTO = {

            items?:    Array < OrgChannelGoodsRespDTO >

            pageNumber?:   number

            pageSize?:   number

            totalElements?:   number

            totalPages?:   number

        }

        type PageableRespDTOQueryWarehouseStockRespDTO = {

            items?:    Array < QueryWarehouseStockRespDTO >

            pageNumber?:   number

            pageSize?:   number

            totalElements?:   number

            totalPages?:   number

        }

        type PageableRespDTOResourceWebRespDTO = {

            items?:    Array < ResourceWebRespDTO >

            pageNumber?:   number

            pageSize?:   number

            totalElements?:   number

            totalPages?:   number

        }

        type PageableRespDTOSaleStockRespDTO = {

            items?:    Array < SaleStockRespDTO >

            pageNumber?:   number

            pageSize?:   number

            totalElements?:   number

            totalPages?:   number

        }

        type PageableRespDTOSkuGoodsResponseDTO = {

            items?:    Array < SkuGoodsResponseDTO >

            pageNumber?:   number

            pageSize?:   number

            totalElements?:   number

            totalPages?:   number

        }

        type PageableRespDTOSpuRes = {

            items?:    Array < SpuRes >

            pageNumber?:   number

            pageSize?:   number

            totalElements?:   number

            totalPages?:   number

        }

        type PageableRespDTOStoreGoodsRespDTO = {

            items?:    Array < StoreGoodsRespDTO >

            pageNumber?:   number

            pageSize?:   number

            totalElements?:   number

            totalPages?:   number

        }

        type PriceCount = {

            count?:   number

            price?:   number

        }

        type PushGoodsReqDTO = {

            channelId?:   number

            goodsSn?:   string

            operateType?:   number

            organizationCode?:   string

            organizationType?:   number

        }

        type QueryConfigValueRequest = {

            configName?:   string

        }

        type QueryDistributionListReq = {

            deptCodes?:    Array < string >

        }

        type QueryGoodsPriceReqDTO = {

            channelId?:   number

            goodsSnList?:    Array < string >

            organizationCode?:   string

        }

        type QueryGoodsPriceRespDTO = {

            goodsSn?:   string

            heartPrice?:   number

            memberPrice?:   number

            retailPrice?:   number

        }

        type QuerySaleGoodsReqDTO = {

            conOrgCode?:   string

            organizationCode?:   string

        }

        type QuerySaleableGoodsRespDTO = {

            customerCode?:   string

            customerName?:   string

            itemCode?:   string

            itemName?:   string

            itemNameEn?:   string

            itemSpec?:   string

            itemWeight?:   string

            memberPrice?:   string

            salePrice?:   string

            saleUnitCode?:   string

            saleUnitName?:   string

            saleUnitNameEn?:   string

        }

        type QuerySkuGoodsPriceObjReq = {

            channelId?:   number

            goodsSn?:   string

            organizationCode?:   string

        }

        type QuerySkuGoodsPriceReqDTO = {

            goodsIdList?:    Array < string >

            goodsList?:    Array < QuerySkuGoodsPriceObjReq >

        }

        type QuerySkuGoodsPriceRespDTO = {

            goodsId?:   string

            goodsSn?:   string

            heartPrice?:   number

            memberPrice?:   number

            retailPrice?:   number

            skuNumber?:   string

            spuNumber?:    Array < string >

            spuNumberStr?:   string

        }

        type QueryWarehouseStockReqDTO = {

            goodsSn?:   string

            idsList?:    Array < number >

            organizationCode?:   string

            spuGoodsNumberList?:    Array < string >

            stockDate?:   string

            unitType?:   number

            warehouseCode?:   string

        }

        type QueryWarehouseStockRespDTO = {

            availableQuantity?:   number

            createdAt?:   string

            customInventory?:   string

            customInventoryEndAt?:   string

            customInventoryStartAt?:   string

            goodsId?:   number

            goodsSn?:   string

            id?:   number

            lockedQuantity?:   number

            organizationCode?:   string

            stockDate?:   string

            stockUnitName?:   string

            totalStock?:   number

            transitQuantity?:   number

            unitType?:   number

            unusableQuantity?:   number

            updatedAt?:   string

            usedQuantity?:   number

            warehouseCode?:   string

        }

        type QueryWarehouseStoreDTO = {

            goodsSnList?:    Array < string >

            orgCode?:   string

            stockDate?:   string

        }

        type ReSyncOrgGoodsReqDTO = {

            channelId?:   number

            operator?:   string

            organizationCode?:   string

            organizationType?:   number

        }

        type ReSyncOrgGoodsRespDTO = {

            channelId?:   number

            goodsTotal?:   number

            organizationCode?:   string

            taskId?:   number

        }

        type RedisSetValueReqDTO = {

            key?:   string

            value?:   string

        }

        type RemoteServiceRequest = {

            args?:    Array < any >

            className?:   string

            methodName?:   string

            parameterCount?:   number

            returnType?:  Type

        }

        type Resource = {

            channelId?:   number

            cornerPosition?:   number

            createdAt?:   string

            createdOperator?:   string

            groupId?:   number

            id?:   number

            isDeleted?:   number

            isEnabled?:   number

            jsonStr?:   string

            keywords?:   string

            labelGroupId?:   number

            name?:   string

            proportion?:   string

            saleType?:   number

            size?:   string

            skuId?:   number

            sort?:   number

            sourceUrl?:   string

            thirdPartyId?:   number

            type?:   number

            updatedAt?:   string

            updatedOperator?:   string

            url?:   string

            version?:   number

            widthAndHeight?:   string

        }

        type ResourceAddGroupReqDTO = {

            activityImg?:    Array < ActivityImg >

            carouselImg?:    Array < CarouselImg >

            categoryCode?:   string

            detailImg?:    Array < DetailImg >

            keywords?:    Array < string >

            mainImg?:    Array < MainImg >

            operator?:   string

            saleType?:   number

            video?:   string

            videoHeadImg?:   string

        }

        type ResourceCancelCornerReqDTO = {

            channelID?:   number

            goodsSn?:   string

            groupID?:   number

            operator?:   string

            organizationCode?:   string

        }

        type ResourceGroupDeleteReqDTO = {

            groupID?:    Array < number >

            operator?:   string

        }

        type ResourceGroupGetReqDTO = {

            channelID?:   number

            groupID?:   number

        }

        type ResourceGroupGetRespDTO = {

            activityImg?:  ActivityImg

            carouselImg?:  CarouselImg

            categoryCode?:   string

            channelID?:   number

            detailImg?:    Array < DetailImg >

            groupID?:   string

            isEnabled?:   boolean

            keywords?:   string

            mainImg?:  MainImg

            saleType?:   number

            video?:   string

            videoHeadImg?:   string

        }

        type ResourceGroupListBean = {

            categoryCode?:   string

            categoryFullName?:   string

            channelID?:   number

            channelName?:   string

            createdAt?:   string

            createdOperator?:   string

            groupID?:   string

            keywords?:   string

            mainImg?:  MainImg

            saleType?:   number

            updatedAt?:   string

        }

        type ResourceGroupListReqDTO = {

            categoryCode?:   string

            channelID?:   number

            createdAtGreaterThan?:   string

            createdAtLessThan?:   string

            isEnabled?:   number

            keywords?:    Array < string >

            pageNumber?:   number

            pageSize?:   number

            saleType?:   number

        }

        type ResourceReqDTO = {

            keywords?:    Array < string >

            name?:   string

            operator?:   string

            size?:   string

            type?:   number

            url?:   string

        }

        type ResourceSetCornerReqDTO = {

            channelID?:   number

            goodsSn?:   string

            groupID?:   number

            mainImgID?:   number

            mainImgUrlWithCorner?:   string

            operator?:   string

            organizationCode?:   string

        }

        type ResourceUpdateGoodsGroupReqDTO = {

            activityImg?:  ActivityImg

            carouselImg?:  CarouselImg

            channelID?:   number

            detailImg?:    Array < DetailImg >

            goodsSn?:   string

            keywords?:    Array < string >

            mainImg?:  MainImg

            operator?:   string

            organizationCode?:   string

            video?:   string

            videoHeadImg?:   string

            videoHeadImgThirdPartyID?:   number

            videoThirdPartyID?:   number

        }

        type ResourceUpdateGroupReqDTO = {

            activityImg?:  ActivityImg

            carouselImg?:  CarouselImg

            categoryCode?:   string

            channelID?:   number

            detailImg?:    Array < DetailImg >

            groupID?:   number

            isEnabled?:   boolean

            keywords?:    Array < string >

            mainImg?:  MainImg

            operator?:   string

            saleType?:   number

            video?:   string

            videoHeadImg?:   string

            videoHeadImgThirdPartyID?:   number

            videoThirdPartyID?:   number

        }

        type ResourceUpdateWebReqDTO = {

            enable?:   number

            keywords?:    Array < string >

            name?:   string

            operator?:   string

            resourceId?:   string

            size?:   string

            url?:   string

        }

        type ResourceWebRespDTO = {

            createdAt?:   string

            createdOperator?:   string

            enabled?:   number

            groupId?:   number

            keywords?:   string

            name?:   string

            resourceId?:   string

            size?:   string

            type?:   number

            updatedAt?:   string

            updatedOperator?:   string

            url?:   string

        }

        type SVipGoodsGoodsRel2DTO = {

            beginTakeTime?:   string

            bidPrice?:   string

            buyerType?:   string

            cityId?:   number

            createdAt?:   string

            deliveryCycle?:   number

            deliveryDate?:   string

            deliveryNumber?:   number

            deliveryType?:   string

            distributionId?:   number

            goodsId?:   number

            goodsName?:   string

            headPic?:   string

            isCombined?:   number

            isCycle?:   number

            isGift?:   number

            masterGoodsId?:   number

            number?:   string

            packageType?:   number

            price?:   string

            relateType?:   string

            shareBidPrice?:   string

            shareVipPrice?:   string

            shortName?:   string

            spec?:   string

            updatedAt?:   string

            vipPrice?:   string

            weight?:   number

        }

        type SaleOrderReqDTO = {

            channelId?:   number

            documentCode?:   string

            documentType?:   number

            goodsList?:    Array < DocumentGoodsDetailReqDTO >

            organizationCode?:   string

            startAt?:   string

            status?:   number

            unitType?:   number

            warehouseCode?:   string

        }

        type SaleStockListReqDTO = {

            channelId?:   number

            channelIds?:    Array < number >

            goodsSns?:    Array < string >

            organizationCode?:   string

            stockDate?:   string

        }

        type SaleStockListRespDTO = {

            goodsSn?:   string

            vendibleQuantity?:   number

        }

        type SaleStockRespDTO = {

            activityQuantity?:   number

            channelId?:   number

            createdAt?:   string

            goodsId?:   number

            goodsSn?:   string

            id?:   number

            isDeleted?:   boolean

            organizationCode?:   string

            presaleQuantity?:   number

            soldQuantity?:   number

            stockDate?:   string

            stockUnitName?:   string

            unitType?:   number

            updatedAt?:   string

            vendibleQuantity?:   number

            version?:   number

            warehouseCode?:   string

        }

        type SaveSkuGoodsRequest = {

            autoRecordCoefficient?:   string

            bidPrice?:   string

            channelSetsList?:    Array < ChannelSet >

            deliveryType?:   number

            distributionId?:   string

            floatUpNumber?:   string

            goodsSaleType?:   string

            groupSupport?:   number

            headPic?:   string

            id?:   number

            inputInfo?:    Array < SkuGoodsExtendInputInfo >

            isDetachable?:   number

            isIce?:   number

            isNewCustomerAvailable?:   number

            isPre?:   number

            isShowAppWeight?:   number

            isSupportCoupon?:   number

            isSupportDisparityRefund?:   number

            level?:   string

            memberPrice?:   string

            memberPriceChoice?:   number

            name?:   string

            number?:   string

            originPlace?:   string

            pickingTip?:   string

            preAdvice?:   string

            price?:   string

            priceShowFormat?:   number

            shareSpec?:   string

            shareTitle?:   string

            shortName?:   string

            spec?:   string

            specQuantity?:   string

            specUnit?:   string

            specialDefinePrice?:   string

            specialDefinePriceEndTime?:   string

            specialDefinePriceStartTime?:   string

            specialDefineStock?:   string

            specialDefineStockEndTime?:   string

            specialDefineStockStartTime?:   string

            subTitle?:   string

            sysCategoryCode?:   string

            sysGenerateVipPrice?:   number

            vipPrice?:   string

            weight?:   number

        }

        type SectionOfCombinationGoodsDTO = {

            basicGoodsId?:   string

            basicGoodsName?:   string

            basicGoodsSaleType?:   number

            basicGoodsSn?:   string

            channelId?:   number

            defaultBasicGoodsName?:   string

            dosage?:   number

            erpCoefficient?:   string

            goodsId?:   string

            goodsSn?:   string

            organizationCode?:   string

            retailUnitCode?:   string

            retailUnitName?:   string

            saleType?:   number

            shelfLife?:   number

            specDesc?:   string

            specWeight?:   number

            weight?:   number

        }

        type SetDTO = {

            carouselImageList?:    Array < Images >

            detailHeadImageList?:    Array < Images >

            detailImageList?:    Array < Images >

            groupId?:   number

            isSelected?:   number

        }

        type SetGoodsSpecialPriceRequest = {

            id?:   number

            lowestSpecialPrice?:   string

            specialPriceGenerateBySys?:   number

        }

        type SetGoodsVipPriceRequest = {

            setGoodsVipPriceList?:    Array < GoodsVipPriceRequest >

        }

        type SetWarehouseStockCustomInventoryReqDTO = {

            channelType?:   number

            customInventory?:   string

            customInventoryEndAt?:   string

            customInventoryStartAt?:   string

            goodsSn?:   string

            id?:   number

            organizationCode?:   string

            stockDate?:   string

        }

        type SetWarehouseStockDTO = {

            availableQuantity?:   number

            goodsSn?:   string

            id?:   number

            organizationCode?:   string

            stockDate?:   string

            updateStockList?:    Array < warehouse_stock >

        }

        type SkuGoods = {

            autoRecordCoefficient?:   string

            bidPrice?:   string

            brandID?:   number

            createTime?:   string

            deliveryCycle?:   number

            deliveryDate?:   string

            deliveryNumber?:   number

            deliveryType?:   string

            distributionId?:   number

            erpCoefficient?:   string

            groupPrice?:   number

            groupSupport?:   string

            headPic?:   string

            id?:   number

            isCombined?:   string

            isCycle?:   string

            isDeliveryOnly?:   number

            isGift?:   string

            isNewCustomerAvailable?:   number

            isOnlyUse?:   string

            isPre?:   string

            isStockSelf?:   string

            isSupportCoupon?:   string

            isVipGoods?:   string

            lastUpdate?:   string

            level?:   string

            lowestSpecialPrice?:   string

            name?:   string

            number?:   string

            operateRecommend?:   string

            originPlace?:   string

            ownedStoreType?:   string

            packageType?:   number

            pickingTip?:   string

            preAdvice?:   string

            price?:   string

            productionPlace?:   string

            publishStatus?:   string

            publishTime?:   string

            saleType?:   string

            shareTitle?:   string

            shareWeChatPic?:   string

            shortName?:   string

            spec?:   string

            specialPriceGenerateBySys?:   string

            spuID?:   number

            spuNumber?:   string

            status?:   string

            subTitle?:   string

            vipPrice?:   string

            vipPriceType?:   number

            weight?:   number

        }

        type SkuGoodsDTO = {

            autoRecordCoefficient?:   string

            bidPrice?:   string

            brandId?:   number

            createdAt?:   string

            deliveryCycle?:   number

            deliveryDate?:   string

            deliveryNumber?:   number

            deliveryType?:   number

            distributionId?:   number

            erpCoefficient?:   string

            goodsDistribution?:  goods_distribution

            goodsLabelList?:    Array < Label >

            goodsResourceList?:    Array < Resource >

            groupPrice?:   number

            groupSupport?:   number

            headPic?:   string

            id?:   number

            isCombined?:   number

            isCycle?:   number

            isDeliveryOnly?:   number

            isGift?:   number

            isNewCustomerAvailable?:   number

            isPre?:   number

            isSupportCoupon?:   number

            isVipGoods?:   number

            labelGroupId?:   number

            lowestSpecialPrice?:   string

            name?:   string

            number?:   string

            operateRecommend?:   number

            operateRecommendTime?:   string

            ownedStoreType?:   string

            packageType?:   number

            pickingTip?:   string

            preAdvice?:   string

            price?:   string

            productionPlace?:   string

            publishStatus?:   string

            publishTime?:   string

            resourceGroupId?:   number

            shareTitle?:   string

            shareWeChatPic?:   string

            shortName?:   string

            spec?:   string

            specialPriceGenerateBySys?:   number

            spuGoods?:  spu_goods

            spuId?:   number

            status?:   number

            subTitle?:   string

            svipGoodsExtend?:  svip_goods_extend

            svipGoodsStock?:  SvipGoodsStockDO

            updatedAt?:   string

            vipPrice?:   string

            vipPriceType?:   number

            weight?:   number

        }

        type SkuGoodsExtendInputInfo = {

            createTime?:   string

            goodsId?:   number

            id?:   number

            lastUpdate?:   string

            operationType?:   number

            priceShowFormat?:   number

            quantity?:   string

            retailUnit?:   string

            servicePrice?:   string

            spuId?:   number

            spuName?:   string

            spuNumber?:   string

            type?:   number

        }

        type SkuGoodsQueryDetailResponse = {

            autoRecordCoefficient?:   string

            bidPrice?:   string

            channelSetsList?:    Array < ChannelSet >

            deliveryType?:   number

            distributionId?:   number

            distributionName?:   string

            floatUpNumber?:   string

            goodsSaleType?:   string

            goodsSpuNumber?:   string

            groupSupport?:   number

            id?:   number

            inputInfo?:    Array < SkuGoodsExtendInputInfo >

            isDetachable?:   number

            isIce?:   number

            isNewCustomerAvailable?:   number

            isPre?:   number

            isShowAppWeight?:   number

            isSupportCoupon?:   number

            isSupportDisparityRefund?:   number

            level?:   string

            memberPrice?:   string

            memberPriceChoice?:   number

            name?:   string

            number?:   string

            originPlace?:   string

            pickingTip?:   string

            preAdvice?:   string

            price?:   string

            priceShowFormat?:   number

            shareSpec?:   string

            shareTitle?:   string

            shortName?:   string

            spec?:   string

            specQuantity?:   string

            specUnit?:   string

            specialDefinePrice?:   string

            specialDefinePriceEndTime?:   string

            specialDefinePriceStartTime?:   string

            specialDefineStock?:   string

            specialDefineStockEndTime?:   string

            specialDefineStockStartTime?:   string

            spuImageInfo?:  SpuImageInfo

            subTitle?:   string

            sysCategoryCode?:   string

            sysGenerateVipPrice?:   number

            vipPrice?:   string

            weight?:   number

        }

        type SkuGoodsResponseDTO = {

            bidPrice?:   string

            channels?:    Array < AdminChannelRes >

            goodsLevel?:   string

            goodsSaleType?:   string

            groupSupport?:   string

            headPic?:   string

            id?:   number

            lastUpdate?:   string

            lowestSpecialPrice?:   string

            name?:   string

            number?:   string

            price?:   string

            publishStatus?:   string

            specialPriceGenerateBySys?:   number

            spuNumber?:   string

            storeGroupNumber?:   number

            upCityNameList?:    Array < string >

            vipPrice?:   string

            vipPriceType?:   number

        }

        type SpuBrandPicReq = {

            imagesList?:    Array < Images >

            type?:   number

        }

        type SpuErpGoodsReqDTO = {

            newWhiteListGoodsList?:    Array < ErpGoods >

            removeWhiteListGoodsList?:    Array < ErpGoods >

            storeCode?:   string

        }

        type SpuGoods = {

            brandId?:   number

            createTime?:   string

            distributionId?:   number

            distributionName?:   string

            expirationLife?:   number

            expirationLifeUnit?:   string

            fleshRate?:   number

            id?:   number

            isOnlyUse?:   string

            lastUpdate?:   string

            laterDayDeliveryNum?:   number

            level?:   string

            nextDayDeliveryNum?:   number

            nextDayTotalStockNum?:   number

            nutritionValue?:   string

            originPlace?:   string

            priceShowFormat?:   number

            publishStatus?:   string

            safeStockNum?:   number

            shelfLife?:   number

            shelfLifeUnit?:   string

            spuHeadPicUrl?:   string

            spuName?:   string

            spuNumber?:   string

            spuType?:   string

            spuTypeName?:   string

            status?:   string

            stockUnit?:   string

            storeWay?:   string

            subTitle?:   string

            superVipDepositoryCode?:   string

            surplusStockNum?:   number

            sysCategoryCode?:   string

            taxCode?:   string

            taxRate?:   number

            totalStockNum?:   number

            warmTip?:   string

        }

        type SpuGoodsListReq = {

            isMatterSpu?:   number

            spuNumber?:   string

            spuNumberList?:    Array < string >

        }

        type SpuGoodsResponseDTO = {

            id?:   number

            level?:   string

            originPlace?:   string

            priceShowFormat?:   string

            publishStatus?:   string

            retailUnit?:   string

            spuName?:   string

            spuNumber?:   string

            sysCategoryCode?:   string

        }

        type SpuImageInfo = {

            spuCarouselImageList?:    Array < Images >

            spuDetailImageList?:    Array < Images >

        }

        type SpuReq = {

            brandID?:   string

            distributionId?:   number

            expirationLife?:   string

            expirationLifeUnit?:   string

            fleshRate?:   string

            id?:   number

            level?:   string

            nutritionValue?:   string

            originPlace?:   string

            pageNumber?:   number

            pageSize?:   number

            price?:   string

            publishStatus?:   string

            shelfLife?:   string

            shelfLifeUnit?:   string

            spuIDList?:    Array < number >

            spuName?:   string

            spuNumber?:   string

            stockUnit?:   string

            storeWay?:   string

            sysCategoryCode?:   string

            taxCode?:   string

            taxRate?:   string

            warmTip?:   string

        }

        type SpuRes = {

            brandId?:   number

            channelSetsList?:    Array < ChannelSetDTO >

            createTime?:   string

            detailHeadVideoList?:    Array < SpuVideo >

            detailVideoList?:    Array < SpuVideo >

            distributionId?:   number

            distributionName?:   string

            expirationLife?:   number

            expirationLifeUnit?:   string

            fleshRate?:   number

            id?:   number

            isOnlyUse?:   string

            lastUpdate?:   string

            laterDayDeliveryNum?:   number

            level?:   string

            nextDayDeliveryNum?:   number

            nextDayTotalStockNum?:   number

            nutritionValue?:   string

            originPlace?:   string

            priceShowFormat?:   number

            publishStatus?:   string

            safeStockNum?:   number

            shelfLife?:   number

            shelfLifeUnit?:   string

            skuCount?:   string

            spuHeadPicUrl?:   string

            spuName?:   string

            spuNumber?:   string

            spuType?:   string

            spuTypeName?:   string

            status?:   string

            stockUnit?:   string

            storeWay?:   string

            subTitle?:   string

            superVipDepositoryCode?:   string

            surplusStockNum?:   number

            sysCategoryCode?:   string

            taxCode?:   string

            taxRate?:   number

            totalStockNum?:   number

            warmTip?:   string

        }

        type SpuVideo = {

            createTime?:   string

            deleted?:   number

            id?:   number

            lastUpdate?:   string

            spuId?:   number

            type?:   number

            video?:  Video

            videoId?:   number

        }

        type SqlOrderInoutDTO = {

            orderType?:   string

            property?:   string

        }

        type StockSnapshotGoodsReqDTO = {

            quantity?:   number

            snapshotId?:   number

        }

        type StockTccConfirmDTO = {

            documentCode?:   string

            documentType?:   number

            organizationCode?:   string

            startAt?:   string

        }

        type StockTccDTO = {

            channelId?:   number

            documentCode?:   string

            documentType?:   number

            goodsList?:    Array < StockSnapshotGoodsReqDTO >

            organizationCode?:   string

            startAt?:   string

            status?:   number

            stockDate?:   string

            unitType?:   number

        }

        type StockUpdateReqDTO = {

            availableQuantity?:   number

            goodsSn?:   string

            storeCode?:   string

        }

        type StoreDO = {

            address?:   string

            brandId?:   number

            businessLicenseUrl?:   string

            cityId?:   number

            createdAt?:   string

            deliverySwitch?:   boolean

            endTime?:   string

            foodBusinessLicenseUrl?:   string

            groupSupport?:   boolean

            id?:   number

            isSupportTake?:   boolean

            isSupportVip?:   boolean

            isSupportVipDelivery?:   boolean

            lat?:   string

            lon?:   string

            managerName?:   string

            managerPhone?:   string

            name?:   string

            number?:   string

            orderPrintBy?:   string

            phone?:   string

            rapidlySupport?:   number

            serviceRadius?:   string

            serviceRadiusShape?:   string

            shortName?:   string

            startTime?:   string

            status?:   string

            totalShape?:   string

            type?:   number

            updatedAt?:   string

        }

        type StoreGoodsRespDTO = {

            channelId?:   number

            goodsName?:   string

            goodsSn?:   string

            isErpStock?:   boolean

            mainImg?:   string

            organizationCode?:   string

            retailPrice?:   number

            saleStatus?:   number

            saleType?:   number

            shortId?:   number

            specDesc?:   string

            vendibleQuantity?:   number

        }

        type SvipGoodsStockDO = {

            createdAt?:   string

            deadlineStockAfter?:   string

            deadlineStockBefore?:   string

            deadlineStockTomorrow?:   string

            distributionId?:   number

            erpCanUseStock?:   string

            erpOnWayStockToday?:   string

            erpOnWayStockTomorrow?:   string

            goodsId?:   number

            id?:   number

            isImport?:   number

            isOnlyUse?:   number

            isStockSelf?:   number

            name?:   string

            number?:   string

            onWayRatio?:   string

            spuId?:   number

            spuNumber?:   string

            stockSafeNum?:   number

            stockSelfNum?:   number

            totalStockNum?:   number

            updatedAt?:   string

        }

        type ThirdChannelGoodsSyncStateDTO = {

            total?:   number

        }

        type Time = {

            date?:   number

            day?:   number

            hours?:   number

            minutes?:   number

            month?:   number

            seconds?:   number

            time?:   number

            timezoneOffset?:   number

            year?:   number

        }

        type Type = {

            typeName?:   string

        }

        type UpdateCommandDTO = {

            arrayFilters?:    Array < JSONObject >

            collect?:   string

            query?:   any

            set?:   any

            updateMany?:   boolean

            upsert?:   boolean

        }

        type UpdateGoodsPriceReqDTO = {

            updateList?:    Array < GoodsPrice >

        }

        type ValueInfo = {

            id?:   string

            isDeleted?:   boolean

            name?:   string

            value?:   string

        }

        type Video = {

            createTime?:   string

            deleted?:   number

            description?:   string

            groupName?:   string

            id?:   number

            lastUpdate?:   string

            maxSize?:   string

            remoteFileName?:   string

            videoCoverUrl?:   string

            videoUrl?:   string

        }

        type WarehouseStockCoverReqDTO = {

            availableQuantity?:   number

            goodsSn?:   string

            operator?:   string

            organizationCode?:   string

            totalStock?:   number

        }

        type WarehouseStockListReqDTO = {

            organizationCode?:   string

            spuGoodsNumberList?:    Array < string >

            stockDate?:   string

        }

        type WarehouseStockListRespDTO = {

            availableQuantity?:   number

            createdAt?:   string

            customInventory?:   string

            customInventoryEndAt?:   string

            customInventoryStartAt?:   string

            goodsSn?:   string

            id?:   number

            isDeleted?:   boolean

            lockedQuantity?:   number

            organizationCode?:   string

            stockDate?:   string

            stockUnitName?:   string

            transitQuantity?:   number

            unusableQuantity?:   number

            updatedAt?:   string

            usedQuantity?:   number

            warehouseCode?:   string

        }

        type WarehouseStockRateReqDTO = {

            goodsSnList?:    Array < string >

            organizationCode?:   string

        }

        type WarehouseStockRateRespDTO = {

            countNum?:   number

            goodsId?:   number

            goodsRate?:   string

            goodsSn?:   string

        }

        type WarehouseStockUpdateReqDTO = {

            goodsSn?:   string

            incrAvailableStock?:   number

            incrStock?:   number

            operator?:   string

            organizationCode?:   string

        }

        type WriteOperateAffectedCategoryDTO = {

            categoryCode?:   string

            categoryType?:   number

            changedName?:   boolean

            changedSort?:   boolean

            channelEnabled?:   boolean

            channelId?:   number

            channelType?:   number

            isCenter?:   boolean

            logicId?:   number

            organizationCode?:   string

            parentCategoryCode?:   string

            sortVal?:   number

        }

        type brand = {

            contacter?:   string

            createdAt?:   string

            id?:   number

            logo?:   string

            name?:   string

            status?:   string

            updatedAt?:   string

        }

        type goods = {

            channelId?:   number

            cityDownStatus?:   string

            cityEffectNum?:   string

            cityWaitNum?:   string

            createdAt?:   string

            createdOperator?:   string

            goodsBasicId?:   number

            goodsChannelId?:   number

            goodsCombinationAssociationGroupId?:   number

            goodsDistributionCenterId?:   number

            goodsPriceId?:   number

            goodsSn?:   string

            goodsUnitId?:   number

            id?:   number

            isDeleted?:   number

            isLocked?:   number

            jsonStr?:   string

            labelGroupId?:   number

            organizationCode?:   string

            organizationType?:   number

            postStatus?:   number

            resourceGroupId?:   number

            saleStatus?:   number

            skuId?:   number

            storeSaleStatus?:   string

            storeStatus?:   number

            storeStockBorder?:   number

            syncDesc?:   string

            syncStatus?:   number

            updatedAt?:   string

            updatedOperator?:   string

            version?:   number

        }

        type goods_city_rel = {

            cityId?:   number

            createdAt?:   string

            downStatus?:   number

            effectNum?:   string

            goodsId?:   number

            id?:   number

            status?:   string

            updatedAt?:   string

            waitNum?:   string

        }

        type goods_distribution = {

            area?:   string

            createdAt?:   string

            deliveryCenterIdCode?:   string

            depositoryCode?:   string

            deptCode?:   string

            id?:   number

            isDeleted?:   boolean

            superVipDepositoryCode?:   string

            updatedAt?:   string

        }

        type sku_goods = {

            autoRecordCoefficient?:   string

            bidPrice?:   string

            brandId?:   number

            createdAt?:   string

            deliveryCycle?:   number

            deliveryDate?:   string

            deliveryNumber?:   number

            deliveryType?:   number

            distributionId?:   number

            erpCoefficient?:   string

            groupPrice?:   number

            groupSupport?:   number

            headPic?:   string

            id?:   number

            isCombined?:   number

            isCycle?:   number

            isDeliveryOnly?:   number

            isGift?:   number

            isNewCustomerAvailable?:   number

            isPre?:   number

            isSupportCoupon?:   number

            isVipGoods?:   number

            labelGroupId?:   number

            lowestSpecialPrice?:   string

            name?:   string

            number?:   string

            operateRecommend?:   number

            operateRecommendTime?:   string

            ownedStoreType?:   string

            packageType?:   number

            pickingTip?:   string

            preAdvice?:   string

            price?:   string

            productionPlace?:   string

            publishStatus?:   string

            publishTime?:   string

            resourceGroupId?:   number

            shareTitle?:   string

            shareWeChatPic?:   string

            shortName?:   string

            spec?:   string

            specialPriceGenerateBySys?:   number

            spuId?:   number

            status?:   number

            subTitle?:   string

            updatedAt?:   string

            vipPrice?:   string

            vipPriceType?:   number

            weight?:   number

        }

        type spu_goods = {

            createdAt?:   string

            id?:   number

            isDeleted?:   boolean

            level?:   string

            nutritionValue?:   string

            publishStatus?:   string

            resourceGroupId?:   number

            spuName?:   string

            spuNumber?:   string

            spuType?:   string

            spuTypeName?:   string

            storeWay?:   string

            taxCode?:   string

            taxRate?:   number

            updatedAt?:   string

            warmTip?:   string

        }

        type store_goods_rel = {

            createdAt?:   string

            goodsId?:   number

            id?:   number

            isDeleted?:   boolean

            saleStatus?:   string

            stockBorder?:   number

            storeId?:   number

            updatedAt?:   string

        }

        type svip_goods_extend = {

            beginTakeTime?:   string

            buyerType?:   string

            createdAt?:   string

            goodsId?:   number

            id?:   number

            isNewExclusive?:   number

            limitBuyDaily?:   number

            limitBuyTotal?:   number

            limitBuyType?:   number

            recommendContent?:   string

            storageType?:   string

            updatedAt?:   string

        }

        type svip_goods_goods_rel = {

            bidPrice?:   string

            createdAt?:   string

            distributionId?:   number

            goodsName?:   string

            id?:   number

            masterGoodsId?:   number

            number?:   string

            price?:   string

            relateType?:   string

            shareBidPrice?:   string

            shareVipPrice?:   string

            slaveGoodsId?:   number

            updatedAt?:   string

            vipPrice?:   string

        }

        type warehouse_stock = {

            availableQuantity?:   number

            createdAt?:   string

            customInventory?:   string

            customInventoryEndAt?:   string

            customInventoryStartAt?:   string

            goodsSn?:   string

            id?:   number

            isDeleted?:   boolean

            lockedQuantity?:   number

            organizationCode?:   string

            stockDate?:   string

            stockUnitName?:   string

            totalStock?:   number

            transitQuantity?:   number

            unusableQuantity?:   number

            updatedAt?:   string

            usedQuantity?:   number

            version?:   number

            warehouseCode?:   string

        }



      /**description
       *
       */

        /**
        * 获取文件服务域签名
        * @method
        * @name 获取文件服务域签名
        * @param  body - apiRequest
        */
        function postAccessSignature (

            parameters : {
              'body'  : ApiRequestAccessSecretReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * check
        * @method
        * @name check

        */
        function getApiCheck (





        ): Promise < AxiosResponse >

        /**
        * check
        * @method
        * @name check

        */
        function headApiCheck (





        ): Promise < AxiosResponse >

        /**
        * check
        * @method
        * @name check

        */
        function postApiCheck (





        ): Promise < AxiosResponse >

        /**
        * check
        * @method
        * @name check

        */
        function putApiCheck (





        ): Promise < AxiosResponse >

        /**
        * check
        * @method
        * @name check

        */
        function deleteApiCheck (





        ): Promise < AxiosResponse >

        /**
        * check
        * @method
        * @name check

        */
        function optionsApiCheck (





        ): Promise < AxiosResponse >

        /**
        * check
        * @method
        * @name check

        */
        function patchApiCheck (





        ): Promise < AxiosResponse >

        /**
        * forward
        * @method
        * @name forward
        * @param  body - apiForwardReq
        */
        function getApiForward (

            parameters : {
              'body'  : ApiForwardReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * forward
        * @method
        * @name forward
        * @param  body - apiForwardReq
        */
        function headApiForward (

            parameters : {
              'body'  : ApiForwardReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * forward
        * @method
        * @name forward
        * @param  body - apiForwardReq
        */
        function postApiForward (

            parameters : {
              'body'  : ApiForwardReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * forward
        * @method
        * @name forward
        * @param  body - apiForwardReq
        */
        function putApiForward (

            parameters : {
              'body'  : ApiForwardReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * forward
        * @method
        * @name forward
        * @param  body - apiForwardReq
        */
        function deleteApiForward (

            parameters : {
              'body'  : ApiForwardReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * forward
        * @method
        * @name forward
        * @param  body - apiForwardReq
        */
        function optionsApiForward (

            parameters : {
              'body'  : ApiForwardReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * forward
        * @method
        * @name forward
        * @param  body - apiForwardReq
        */
        function patchApiForward (

            parameters : {
              'body'  : ApiForwardReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * getToken
        * @method
        * @name getToken

        */
        function getApiGetToken (





        ): Promise < AxiosResponse >

        /**
        * getToken
        * @method
        * @name getToken

        */
        function headApiGetToken (





        ): Promise < AxiosResponse >

        /**
        * getToken
        * @method
        * @name getToken

        */
        function postApiGetToken (





        ): Promise < AxiosResponse >

        /**
        * getToken
        * @method
        * @name getToken

        */
        function putApiGetToken (





        ): Promise < AxiosResponse >

        /**
        * getToken
        * @method
        * @name getToken

        */
        function deleteApiGetToken (





        ): Promise < AxiosResponse >

        /**
        * getToken
        * @method
        * @name getToken

        */
        function optionsApiGetToken (





        ): Promise < AxiosResponse >

        /**
        * getToken
        * @method
        * @name getToken

        */
        function patchApiGetToken (





        ): Promise < AxiosResponse >

        /**
        * setValue
        * @method
        * @name setValue
        * @param  body - request
        */
        function postApiSetValue (

            parameters : {
              'body'  : ApiRequestRedisSetValueReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.9.2.5.查询商品库存列表
        * @method
        * @name 3.9.2.5.查询商品库存列表
        * @param  body - apiRequest
        */
        function postAppletsWarehouseStockListStock (

            parameters : {
              'body'  : ApiRequestWarehouseStockListReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * getAllGoods
        * @method
        * @name getAllGoods
        * @param  body - req
        */
        function postBaseGoodsList (

            parameters : {
              'body'  : ApiRequestGoodsReq,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询品牌
        * @method
        * @name 查询品牌
        * @param  body - apiRequest
        */
        function postBrandV1ListBrand (

            parameters : {
              'body'  : ApiRequestExampleSimpleInputDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * adjustCategoryCodeDecrVal
        * @method
        * @name adjustCategoryCodeDecrVal
        * @param  body - request
        */
        function postCategoryAdjustCategoryCodeDecrVal (

            parameters : {
              'body'  : ApiRequestAdjustRedisKeyDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * categorySort
        * @method
        * @name categorySort
        * @param  body - request
        */
        function postCategoryCategorySort (

            parameters : {
              'body'  : ApiRequestAdminCategorySortReq,

            }


        ): Promise < AxiosResponse >

        /**
        * 1.1.1.新增类目
        * @method
        * @name 1.1.1.新增类目
        * @param  body - request
        */
        function postCategoryCreate (

            parameters : {
              'body'  : ApiRequestListCategoryCreateWebDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 1.1.4.删除类目
        * @method
        * @name 1.1.4.删除类目
        * @param  body - request
        */
        function postCategoryDelete (

            parameters : {
              'body'  : ApiRequestCategoryDelReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 1.1.2.编辑类目
        * @method
        * @name 1.1.2.编辑类目
        * @param  body - request
        */
        function postCategoryEdit (

            parameters : {
              'body'  : ApiRequestListCategoryUpdateWebDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.9.2.3.查询类目商品列表
        * @method
        * @name 3.9.2.3.查询类目商品列表
        * @param  body - apiRequest
        */
        function postCategoryGoods (

            parameters : {
              'body'  : ApiRequestPageableReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * publishCategory
        * @method
        * @name publishCategory
        * @param  body - request
        */
        function postCategoryPublishCategory (

            parameters : {
              'body'  : ApiRequestCategoryReq,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.9.2.4.查询组织渠道类目
        * @method
        * @name 3.9.2.4.查询组织渠道类目
        * @param  body - apiRequest
        */
        function postCategoryQueryByOrg (

            parameters : {
              'body'  : ApiRequestOrgCategoryReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * queryDistributionCategoryList
        * @method
        * @name queryDistributionCategoryList
        * @param  body - request
        */
        function postCategoryQueryDistributionCategoryList (

            parameters : {
              'body'  : ApiRequestCategoryReq,

            }


        ): Promise < AxiosResponse >

        /**
        * 2.4.8.查询机构渠道前台类目中商品列表
        * @method
        * @name 2.4.8.查询机构渠道前台类目中商品列表
        * @param  body - apiRequest
        */
        function postCategoryQueryOrgChannelCategoryGoods (

            parameters : {
              'body'  : ApiRequestListOrgChannelCategoryGoodsWebReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * insertCategory
        * @method
        * @name insertCategory
        * @param  body - request
        */
        function postCategorySaveCategory (

            parameters : {
              'body'  : ApiRequestCategoryReq,

            }


        ): Promise < AxiosResponse >

        /**
        * 1.1.3.查询类目
        * @method
        * @name 1.1.3.查询类目
        * @param  body - request
        */
        function postCategorySearch (

            parameters : {
              'body'  : ApiRequestCategoryQueryReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * updateCategory
        * @method
        * @name updateCategory
        * @param  body - request
        */
        function postCategoryUpdateCategory (

            parameters : {
              'body'  : ApiRequestCategoryReq,

            }


        ): Promise < AxiosResponse >

        /**
        * 1.1.5.新增类目关联
        * @method
        * @name 1.1.5.新增类目关联
        * @param  body - request
        */
        function postCategoryAssociationCreate (

            parameters : {
              'body'  : ApiRequestListCategorySelfAssociationSaveDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 1.1.6.删除类目关联
        * @method
        * @name 1.1.6.删除类目关联
        * @param  body - request
        */
        function postCategoryAssociationDelete (

            parameters : {
              'body'  : ApiRequestListstring,

            }


        ): Promise < AxiosResponse >

        /**
        * 1.1.7.编辑类目关联
        * @method
        * @name 1.1.7.编辑类目关联
        * @param  body - request
        */
        function postCategoryAssociationEdit (

            parameters : {
              'body'  : ApiRequestListCategorySelfAssociationUpdateDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 1.1.8.查询类目关联
        * @method
        * @name 1.1.8.查询类目关联
        * @param  body - request
        */
        function postCategoryAssociationSearch (

            parameters : {
              'body'  : ApiRequestCategorySelfAssociationQueryReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 2.4.6 编辑机构渠道前台类目商品排序
        * @method
        * @name 2.4.6 编辑机构渠道前台类目商品排序
        * @param  body - request
        */
        function postCategoryGoodsChangeSort (

            parameters : {
              'body'  : ApiRequestListOrgCategoryGoodsRelationDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 2.4.5.新增机构渠道前台类目与商品关联
        * @method
        * @name 2.4.5.新增机构渠道前台类目与商品关联
        * @param  body - request
        */
        function postCategoryGoodsCreateAssociation (

            parameters : {
              'body'  : ApiRequestListOrgCategoryGoodsRelationDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 2.4.7 删除机构渠道前台类目与商品关联
        * @method
        * @name 2.4.7 删除机构渠道前台类目与商品关联
        * @param  body - request
        */
        function postCategoryGoodsDeleteAssociation (

            parameters : {
              'body'  : ApiRequestListOrgCategoryGoodsDelReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 1.2.1.新增渠道接口
        * @method
        * @name 1.2.1.新增渠道接口
        * @param  body - apiRequest
        */
        function postChannelAdd (

            parameters : {
              'body'  : ApiRequestChannelAddReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 1.2.2.删除渠道接口
        * @method
        * @name 1.2.2.删除渠道接口
        * @param  body - apiRequest
        */
        function postChannelDelete (

            parameters : {
              'body'  : ApiRequestChannelDeleteReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 1.2.5.查询渠道详情接口
        * @method
        * @name 1.2.5.查询渠道详情接口
        * @param  body - apiRequest
        */
        function postChannelGet (

            parameters : {
              'body'  : ApiRequestChannelGetReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 小程序查询商品收益排行榜
        * @method
        * @name 小程序查询商品收益排行榜
        * @param  body - apiRequest
        */
        function postChannelGoodsIncomeList (

            parameters : {
              'body'  : ApiRequestPageableReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.9.2.2.商品ID查询渠道商品详情
        * @method
        * @name 3.9.2.2.商品ID查询渠道商品详情
        * @param  body - apiRequest
        */
        function postChannelGoodsInfo (

            parameters : {
              'body'  : ApiRequestGoodsInfoReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.9.2.1.查询渠道商品列表
        * @method
        * @name 3.9.2.1.查询渠道商品列表
        * @param  body - apiRequest
        */
        function postChannelGoodsList (

            parameters : {
              'body'  : ApiRequestGoodsListReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 商品搜索
        * @method
        * @name 商品搜索
        * @param  body - apiRequest
        */
        function postChannelGoodsSearch (

            parameters : {
              'body'  : ApiRequestGoodsSearchReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.9.2.3.商品唯一建查询渠道商品详情
        * @method
        * @name 3.9.2.3.商品唯一建查询渠道商品详情
        * @param  body - apiRequest
        */
        function postChannelGoodsUnique (

            parameters : {
              'body'  : ApiRequestListGoodsUniqueReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 1.2.4.查询渠道列表接口
        * @method
        * @name 1.2.4.查询渠道列表接口
        * @param  body - apiRequest
        */
        function postChannelList (

            parameters : {
              'body'  : ApiRequestChannelListReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 1.2.4.查询渠道列表（全量）接口
        * @method
        * @name 1.2.4.查询渠道列表（全量）接口
        * @param  body - apiRequest
        */
        function postChannelListAll (

            parameters : {
              'body'  : ApiRequestChannelListAllReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 1.2.3 门店新开渠道
        * @method
        * @name 1.2.3 门店新开渠道
        * @param  body - apiRequest
        */
        function postChannelOpen (

            parameters : {
              'body'  : ApiRequestListChannelOpenReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 1.2.2.编辑渠道接口
        * @method
        * @name 1.2.2.编辑渠道接口
        * @param  body - apiRequest
        */
        function postChannelUpdate (

            parameters : {
              'body'  : ApiRequestChannelUpdateReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 2.4.1.新增机构渠道前台类目
        * @method
        * @name 2.4.1.新增机构渠道前台类目
        * @param  body - request
        */
        function postChannelOrganizationCreateCategory (

            parameters : {
              'body'  : ApiRequestListOrgCategoryCreateWebDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 2.4.3.删除机构渠道前台类目
        * @method
        * @name 2.4.3.删除机构渠道前台类目
        * @param  body - request
        */
        function postChannelOrganizationDeleteCategory (

            parameters : {
              'body'  : ApiRequestListOrgCategoryDelReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 2.4.2.编辑机构渠道前台类目
        * @method
        * @name 2.4.2.编辑机构渠道前台类目
        * @param  body - request
        */
        function postChannelOrganizationEditCategory (

            parameters : {
              'body'  : ApiRequestListOrgCategoryUpdateWebDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * queryAllOrgLeafCategory
        * @method
        * @name queryAllOrgLeafCategory
        * @param  body - request
        */
        function postChannelOrganizationQueryAllOrgLeafCategory (

            parameters : {
              'body'  : ApiRequestOrgLeafCategoryQueryDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * queryOrgCategoryDetail
        * @method
        * @name queryOrgCategoryDetail
        * @param  body - request
        */
        function postChannelOrganizationQueryOrgCategoryDetail (

            parameters : {
              'body'  : ApiRequestOrgCategoryQueryReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 2.4.4.查询机构渠道前台类目
        * @method
        * @name 2.4.4.查询机构渠道前台类目
        * @param  body - request
        */
        function postChannelOrganizationSearchCategory (

            parameters : {
              'body'  : ApiRequestOrgCategoryTreeQueryReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 城市列表
        * @method
        * @name 城市列表
        * @param  body - apiRequest
        */
        function postCityV1ListCity (

            parameters : {
              'body'  : ApiRequestExampleSimpleInputDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 1.5.1 新增字典
        * @method
        * @name 1.5.1 新增字典
        * @param  body - apiRequest
        */
        function postDictionaryAdd (

            parameters : {
              'body'  : ApiRequestDictionaryAddReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 1.5.3 查询字典
        * @method
        * @name 1.5.3 查询字典
        * @param  body - apiRequest
        */
        function postDictionaryList (

            parameters : {
              'body'  : ApiRequestListDictionaryListReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 1.5.2 编辑字典
        * @method
        * @name 1.5.2 编辑字典
        * @param  body - apiRequest
        */
        function postDictionaryUpdate (

            parameters : {
              'body'  : ApiRequestDictionaryUpdateReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询SPU商品最高限价中位数
        * @method
        * @name 查询SPU商品最高限价中位数
        * @param  body - apiRequest
        */
        function postErpQueryGoodsPriceErpQueryGoodsPrice (

            parameters : {
              'body'  : ApiRequestSkuGoods,

            }


        ): Promise < AxiosResponse >

        /**
        * sku商品列表
        * @method
        * @name sku商品列表
        * @param  body - apiRequest
        */
        function postEshopListSkuGoods (

            parameters : {
              'body'  : ApiRequestExampleInputDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * sku商品列表sql 示例
        * @method
        * @name sku商品列表sql 示例
        * @param  body - apiRequest
        */
        function postEshopListSkuGoodsSql (

            parameters : {
              'body'  : ApiRequestExampleInputDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 新增商品
        * @method
        * @name 新增商品
        * @param  body - apiRequest
        */
        function postGoodsManageAdd (

            parameters : {
              'body'  : ApiRequestListGoodsTemplateReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.1.1.4.1.新增模板商品
        * @method
        * @name 3.1.1.4.1.新增模板商品
        * @param  body - apiRequest
        */
        function postGoodsTemplateAdd (

            parameters : {
              'body'  : ApiRequestListGoodsTemplateReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        *  查询美团渠道类目属性
        * @method
        * @name  查询美团渠道类目属性
        * @param  body - apiRequest
        */
        function postGoodsTemplateCategoryPropertyGet (

            parameters : {
              'body'  : ApiRequestMeituanCategoryPropertyReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        *  查询美团渠道类目属性值
        * @method
        * @name  查询美团渠道类目属性值
        * @param  body - apiRequest
        */
        function postGoodsTemplateCategoryPropertyValueGet (

            parameters : {
              'body'  : ApiRequestMeituanCategoryPropertyValueReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 删除模板商品
        * @method
        * @name 删除模板商品
        * @param  body - apiRequest
        */
        function postGoodsTemplateDelete (

            parameters : {
              'body'  : ApiRequestGoodsTemplateDeleteReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 重复模板商品编码处理
        * @method
        * @name 重复模板商品编码处理
        * @param  body - apiRequest
        */
        function postGoodsTemplateDuplicate (

            parameters : {
              'body'  : ApiRequestGoodsTemplateDuplicateReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * V20200429生成商品编码
        * @method
        * @name V20200429生成商品编码
        * @param  body - apiRequest
        */
        function postGoodsTemplateGenerateCode (

            parameters : {
              'body'  : ApiRequestGoodsGenerateCodeReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.1.1.4.4.查询模板商品详情
        * @method
        * @name 3.1.1.4.4.查询模板商品详情
        * @param  body - apiRequest
        */
        function postGoodsTemplateGet (

            parameters : {
              'body'  : ApiRequestGoodsTemplateDetailReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.1.1.4.3.查询模板商品列表
        * @method
        * @name 3.1.1.4.3.查询模板商品列表
        * @param  body - apiRequest
        */
        function postGoodsTemplateList (

            parameters : {
              'body'  : ApiRequestGoodsTemplateListReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.1.1.4.2.编辑模板商品
        * @method
        * @name 3.1.1.4.2.编辑模板商品
        * @param  body - apiRequest
        */
        function postGoodsTemplateUpdate (

            parameters : {
              'body'  : ApiRequestListGoodsTemplateReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 更新商品基础信息至所有门店
        * @method
        * @name 更新商品基础信息至所有门店
        * @param  body - apiRequest
        */
        function postGoodsBasicReleaseAll (

            parameters : {
              'body'  : ApiRequestGoodsBasicReleaseAllDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * batchQueryDetail
        * @method
        * @name batchQueryDetail
        * @param  body - apiRequest
        */
        function postGoodsBatchQueryDetail (

            parameters : {
              'body'  : ApiRequestListGoodsUniqueKeyReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * getSpuList
        * @method
        * @name getSpuList

        */
        function getGoodsBrandList (





        ): Promise < AxiosResponse >

        /**
        * 取消商品自定义库存
        * @method
        * @name 取消商品自定义库存
        * @param  body - apiRequest
        */
        function postGoodsCancelCustomInventory (

            parameters : {
              'body'  : ApiRequestListCancelGoodsCustomInventoryDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 取消商品定时上下架
        * @method
        * @name 取消商品定时上下架
        * @param  body - apiRequest
        */
        function postGoodsCancelTimeShelves (

            parameters : {
              'body'  : ApiRequestListGoodsUnionKeyReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.3.4.1 变更门店白名单商品
        * @method
        * @name 3.3.4.1 变更门店白名单商品
        * @param  body - request
        */
        function postGoodsChangeWhiteListByStoreCode (

            parameters : {
              'body'  : ApiRequestListSpuErpGoodsReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.2.1.13.查询渠道商品信息
        * @method
        * @name 3.2.1.13.查询渠道商品信息
        * @param  body - apiRequest
        */
        function postGoodsChannelGoods (

            parameters : {
              'body'  : ApiRequestChannelGoodsReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * combinationGoods
        * @method
        * @name combinationGoods
        * @param  body - apiRequest
        */
        function postGoodsCombinationGoods (

            parameters : {
              'body'  : ApiRequestListstring,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.1.2.3.5.删除门店商品
        * @method
        * @name 3.1.2.3.5.删除门店商品
        * @param  body - apiRequest
        */
        function postGoodsDelete (

            parameters : {
              'body'  : ApiRequestListGoodsDeleteReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 清除三方商品
        * @method
        * @name 清除三方商品
        * @param  body - apiRequest
        */
        function postGoodsDeleteChannelGoods (

            parameters : {
              'body'  : ApiRequestListDeleteChannelGoodsDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 删除配送中心未发布商品
        * @method
        * @name 删除配送中心未发布商品
        * @param  body - apiRequest
        */
        function postGoodsDeleteUnPost (

            parameters : {
              'body'  : ApiRequestDeleteUnPostReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.1.2.3.9.查询机构商品详情
        * @method
        * @name 3.1.2.3.9.查询机构商品详情
        * @param  body - apiRequest
        */
        function postGoodsDetail (

            parameters : {
              'body'  : ApiRequestGoodsDetailReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.1.2.3.7.查询机构商品列表（管理端）
        * @method
        * @name 3.1.2.3.7.查询机构商品列表（管理端）
        * @param  body - request
        */
        function postGoodsList (

            parameters : {
              'body'  : ApiRequestPageableReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.1.2.3.8.查询机构商品列表（前端）
        * @method
        * @name 3.1.2.3.8.查询机构商品列表（前端）
        * @param  body - request
        */
        function postGoodsListFront (

            parameters : {
              'body'  : ApiRequestPageableReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * queryThirdGoodsList
        * @method
        * @name queryThirdGoodsList
        * @param  body - request
        */
        function postGoodsListThirdGoods (

            parameters : {
              'body'  : ApiRequestPageableReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * listShopGoods
        * @method
        * @name listShopGoods
        * @param  body - request
        */
        function postGoodsListShopGoods (

            parameters : {
              'body'  : ApiRequestPageableReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 商品迁移公共远程接口
        * @method
        * @name 商品迁移公共远程接口
        * @param  body - apiRequest
        */
        function postGoodsMigrationCommonRemote (

            parameters : {
              'body'  : ApiRequestRemoteServiceRequest,

            }


        ): Promise < AxiosResponse >

        /**
        * 设置商品自定义零售价
        * @method
        * @name 设置商品自定义零售价
        * @param  body - apiRequest
        */
        function postGoodsPriceCount (

            parameters : {
              'body'  : ApiRequestGoodsPriceCountReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 推送商品价格信息至融合
        * @method
        * @name 推送商品价格信息至融合
        * @param  body - request
        */
        function postGoodsPricePushToFusion (

            parameters : {
              'body'  : ApiRequestListFusionGoodsPriceDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.3.4.2 实时同步ERP商品价格
        * @method
        * @name 3.3.4.2 实时同步ERP商品价格
        * @param  body - apiRequest
        */
        function postGoodsPriceRenewal (

            parameters : {
              'body'  : ApiRequestErpGoodsPriceRenewalDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 设置商品自定义零售价
        * @method
        * @name 设置商品自定义零售价
        * @param  body - apiRequest
        */
        function postGoodsPriceSetCustomPrice (

            parameters : {
              'body'  : ApiRequestListGoodsCustomRetailPriceDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.7.1.1.6 同步裸果商品售价
        * @method
        * @name 3.7.1.1.6 同步裸果商品售价
        * @param  body - apiRequest
        */
        function postGoodsPriceSync (

            parameters : {
              'body'  : ApiRequestListGoodsPriceSyncReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.4.5 更新商品价格
        * @method
        * @name 3.4.5 更新商品价格
        * @param  body - apiRequest
        */
        function postGoodsPriceUpdate (

            parameters : {
              'body'  : ApiRequestListGoodsPriceUpdateDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.1.2.3.1.拉取机构商品
        * @method
        * @name 3.1.2.3.1.拉取机构商品
        * @param  body - apiRequest
        */
        function postGoodsPull (

            parameters : {
              'body'  : ApiRequestListGoodsPullAuthorizationReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 推送商品创建信息至融合
        * @method
        * @name 推送商品创建信息至融合
        * @param  body - request
        */
        function postGoodsPushToFusion (

            parameters : {
              'body'  : ApiRequestSetGoodsUnionKeyReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 推送商品创建信息
        * @method
        * @name 推送商品创建信息
        * @param  body - apiRequest
        */
        function postGoodsPushGoods (

            parameters : {
              'body'  : ApiRequestListPushGoodsReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询商品价格0426
        * @method
        * @name 查询商品价格0426
        * @param  body - apiRequest
        */
        function postGoodsQueryGoodsPrice (

            parameters : {
              'body'  : ApiRequestQueryGoodsPriceReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 管理台-查询商品详情
        * @method
        * @name 管理台-查询商品详情
        * @param  body - apiRequest
        */
        function postGoodsQueryMtGoods (

            parameters : {
              'body'  : ApiRequestMtGoodsInfoReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 管理台-组织-渠道-商品编码查询商品详情
        * @method
        * @name 管理台-组织-渠道-商品编码查询商品详情
        * @param  body - apiRequest
        */
        function postGoodsQueryMtGoodsForUnique (

            parameters : {
              'body'  : ApiRequestListMtGoodsUniqueReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        *  查询sku商品价格信息 - 0515
        * @method
        * @name  查询sku商品价格信息 - 0515
        * @param  body - request
        */
        function postGoodsQuerySkuGoodsPrice (

            parameters : {
              'body'  : ApiRequestQuerySkuGoodsPriceReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.1.2.3.2.发布商品至门店
        * @method
        * @name 3.1.2.3.2.发布商品至门店
        * @param  body - apiRequest
        */
        function postGoodsRelease (

            parameters : {
              'body'  : ApiRequestListGoodsReleaseReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 设置商品自定义库存
        * @method
        * @name 设置商品自定义库存
        * @param  body - apiRequest
        */
        function postGoodsSetCustomInventory (

            parameters : {
              'body'  : ApiRequestListGoodsCustomInventoryDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * queryGoodsSnapshotId
        * @method
        * @name queryGoodsSnapshotId
        * @param  body - request
        */
        function postGoodsSnapshotQueryGoodsSnapshotId (

            parameters : {
              'body'  : ApiRequestListGoodsSnapshotIdQueryDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * queryGoodsSnapshotInfo
        * @method
        * @name queryGoodsSnapshotInfo
        * @param  body - request
        */
        function postGoodsSnapshotQueryGoodsSnapshotInfo (

            parameters : {
              'body'  : ApiRequestGoodsSnapshotDetailFilterQueryDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * brandPic
        * @method
        * @name brandPic
        * @param  body - request
        */
        function postGoodsSpuBrandPic (

            parameters : {
              'body'  : ApiRequestSpuBrandPicReq,

            }


        ): Promise < AxiosResponse >

        /**
        * getSpuDetailById
        * @method
        * @name getSpuDetailById
        * @param  body - request
        */
        function postGoodsSpuDetail (

            parameters : {
              'body'  : ApiRequestSpuReq,

            }


        ): Promise < AxiosResponse >

        /**
        * updateSpuDetail
        * @method
        * @name updateSpuDetail
        * @param  body - request
        */
        function postGoodsSpuEdit (

            parameters : {
              'body'  : ApiRequestSpuRes,

            }


        ): Promise < AxiosResponse >

        /**
        * getBrandPic
        * @method
        * @name getBrandPic

        */
        function getGoodsSpuGetBrandPic (





        ): Promise < AxiosResponse >

        /**
        * getSpuList
        * @method
        * @name getSpuList
        * @param  body - request
        */
        function postGoodsSpuList (

            parameters : {
              'body'  : ApiRequestSpuReq,

            }


        ): Promise < AxiosResponse >

        /**
        * publishSpu
        * @method
        * @name publishSpu
        * @param  body - req
        */
        function postGoodsSpuPublish (

            parameters : {
              'body'  : ApiRequestSpuReq,

            }


        ): Promise < AxiosResponse >

        /**
        * syschroSkuBySpu
        * @method
        * @name syschroSkuBySpu
        * @param  body - request
        */
        function postGoodsSpuSynchro (

            parameters : {
              'body'  : ApiRequestSpuReq,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.1.2.1.2.同步果品信息
        * @method
        * @name 3.1.2.1.2.同步果品信息
        * @param  body - apiRequest
        */
        function postGoodsSynchronizeInfo (

            parameters : {
              'body'  : ApiRequestListGoodsSynchronizeInfoReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.1.2.1.3.同步商品基础售价
        * @method
        * @name 3.1.2.1.3.同步商品基础售价
        * @param  body - apiRequest
        */
        function postGoodsSynchronizePrice (

            parameters : {
              'body'  : ApiRequestListGoodsSynchronizePriceReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 商品定时上下架
        * @method
        * @name 商品定时上下架
        * @param  body - apiRequest
        */
        function postGoodsTimeShelves (

            parameters : {
              'body'  : ApiRequestListPageableReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.1.2.3.3.修改基础商品信息
        * @method
        * @name 3.1.2.3.3.修改基础商品信息
        * @param  body - apiRequest
        */
        function postGoodsUpdateBasic (

            parameters : {
              'body'  : ApiRequestListGoodsUpdateBasicReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.1.2.3.4.修改渠道商品信息
        * @method
        * @name 3.1.2.3.4.修改渠道商品信息
        * @param  body - apiRequest
        */
        function postGoodsUpdateChannel (

            parameters : {
              'body'  : ApiRequestListGoodsUpdateChannelReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 编辑会员价至京东渠道
        * @method
        * @name 编辑会员价至京东渠道
        * @param  body - request
        */
        function postGoodsUpdateMemberPrice (

            parameters : {
              'body'  : ApiRequestListGoodsMemberPriceUpdateDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.1.2.6.1.修改商品价格
        * @method
        * @name 3.1.2.6.1.修改商品价格
        * @param  body - apiRequest
        */
        function postGoodsUpdatePrice (

            parameters : {
              'body'  : ApiRequestListGoodsUpdatePriceReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 更新商品推荐信息
        * @method
        * @name 更新商品推荐信息
        * @param  body - request
        */
        function postGoodsUpdateRecommendation (

            parameters : {
              'body'  : ApiRequestListGoodsRecommendationReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.1.2.3.6.修改商品上下架
        * @method
        * @name 3.1.2.3.6.修改商品上下架
        * @param  body - apiRequest
        */
        function postGoodsUpdateSaleStatus (

            parameters : {
              'body'  : ApiRequestListGoodsUpdateSaleStatusReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 更新商品价格信息
        * @method
        * @name 更新商品价格信息
        * @param  body - apiRequest
        */
        function postGoodsUpdateGoodsPriceInfo (

            parameters : {
              'body'  : ApiRequestUpdateGoodsPriceReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 商品列表
        * @method
        * @name 商品列表
        * @param  body - apiRequest
        */
        function postGoodsV1ListGoods (

            parameters : {
              'body'  : ApiRequestExampleSimpleInputDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 商品品类城市关系列表
        * @method
        * @name 商品品类城市关系列表
        * @param  body - apiRequest
        */
        function postGoodsV1ListGoodsCategoryCityRel (

            parameters : {
              'body'  : ApiRequestExampleSimpleInputDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 商品城市关系列表
        * @method
        * @name 商品城市关系列表
        * @param  body - apiRequest
        */
        function postGoodsV1ListGoodsCityRel (

            parameters : {
              'body'  : ApiRequestExampleSimpleInputDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 商品配送中心
        * @method
        * @name 商品配送中心
        * @param  body - apiRequest
        */
        function postGoodsV1ListGoodsDistribution (

            parameters : {
              'body'  : ApiRequestExampleSimpleInputDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 商品标签
        * @method
        * @name 商品标签
        * @param  body - apiRequest
        */
        function postGoodsV1ListGoodsLabel (

            parameters : {
              'body'  : ApiRequestExampleSimpleInputDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 商品关系列表
        * @method
        * @name 商品关系列表
        * @param  body - apiRequest
        */
        function postGoodsV1ListGoodsRel (

            parameters : {
              'body'  : ApiRequestExampleSimpleInputDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 商品资源
        * @method
        * @name 商品资源
        * @param  body - apiRequest
        */
        function postGoodsV1ListGoodsResource (

            parameters : {
              'body'  : ApiRequestExampleSimpleInputDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * sku商品列表
        * @method
        * @name sku商品列表
        * @param  body - apiRequest
        */
        function postGoodsV1ListSkuGoods (

            parameters : {
              'body'  : ApiRequestExampleSimpleInputDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * spu商品列表
        * @method
        * @name spu商品列表
        * @param  body - apiRequest
        */
        function postGoodsV1ListSpuGoods (

            parameters : {
              'body'  : ApiRequestExampleSimpleInputDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 商品门店关系列表
        * @method
        * @name 商品门店关系列表
        * @param  body - apiRequest
        */
        function postGoodsV1ListStoreGoodsRel (

            parameters : {
              'body'  : ApiRequestExampleSimpleInputDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 心享商品扩展属性列表
        * @method
        * @name 心享商品扩展属性列表
        * @param  body - apiRequest
        */
        function postGoodsV1ListSvipGoodsExtend (

            parameters : {
              'body'  : ApiRequestExampleSimpleInputDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 心享商品商品列表
        * @method
        * @name 心享商品商品列表
        * @param  body - apiRequest
        */
        function postGoodsV1ListSvipGoodsGoodsRel (

            parameters : {
              'body'  : ApiRequestExampleSimpleInputDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 心享商品商品列表
        * @method
        * @name 心享商品商品列表
        * @param  body - apiRequest
        */
        function postGoodsV1ListSvipGoodsGoodsRel2 (

            parameters : {
              'body'  : ApiRequestExampleSimpleInputDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 心享商品库存列表
        * @method
        * @name 心享商品库存列表
        * @param  body - apiRequest
        */
        function postGoodsV1ListSvipGoodsStock (

            parameters : {
              'body'  : ApiRequestExampleSimpleInputDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * pushInconsistentGoods
        * @method
        * @name pushInconsistentGoods
        * @param  body - request
        */
        function getManualPushInconsistentGoods (

            parameters : {
              'body'  : ApiRequestListReSyncOrgGoodsReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * pushInconsistentGoods
        * @method
        * @name pushInconsistentGoods
        * @param  body - request
        */
        function headManualPushInconsistentGoods (

            parameters : {
              'body'  : ApiRequestListReSyncOrgGoodsReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * pushInconsistentGoods
        * @method
        * @name pushInconsistentGoods
        * @param  body - request
        */
        function postManualPushInconsistentGoods (

            parameters : {
              'body'  : ApiRequestListReSyncOrgGoodsReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * pushInconsistentGoods
        * @method
        * @name pushInconsistentGoods
        * @param  body - request
        */
        function putManualPushInconsistentGoods (

            parameters : {
              'body'  : ApiRequestListReSyncOrgGoodsReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * pushInconsistentGoods
        * @method
        * @name pushInconsistentGoods
        * @param  body - request
        */
        function deleteManualPushInconsistentGoods (

            parameters : {
              'body'  : ApiRequestListReSyncOrgGoodsReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * pushInconsistentGoods
        * @method
        * @name pushInconsistentGoods
        * @param  body - request
        */
        function optionsManualPushInconsistentGoods (

            parameters : {
              'body'  : ApiRequestListReSyncOrgGoodsReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * pushInconsistentGoods
        * @method
        * @name pushInconsistentGoods
        * @param  body - request
        */
        function patchManualPushInconsistentGoods (

            parameters : {
              'body'  : ApiRequestListReSyncOrgGoodsReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * querySyncFailDetail
        * @method
        * @name querySyncFailDetail
        * @param  body - request
        */
        function getManualQuerySyncFailDetail (

            parameters : {
              'body'  : ApiRequestListReSyncOrgGoodsReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * querySyncFailDetail
        * @method
        * @name querySyncFailDetail
        * @param  body - request
        */
        function headManualQuerySyncFailDetail (

            parameters : {
              'body'  : ApiRequestListReSyncOrgGoodsReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * querySyncFailDetail
        * @method
        * @name querySyncFailDetail
        * @param  body - request
        */
        function postManualQuerySyncFailDetail (

            parameters : {
              'body'  : ApiRequestListReSyncOrgGoodsReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * querySyncFailDetail
        * @method
        * @name querySyncFailDetail
        * @param  body - request
        */
        function putManualQuerySyncFailDetail (

            parameters : {
              'body'  : ApiRequestListReSyncOrgGoodsReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * querySyncFailDetail
        * @method
        * @name querySyncFailDetail
        * @param  body - request
        */
        function deleteManualQuerySyncFailDetail (

            parameters : {
              'body'  : ApiRequestListReSyncOrgGoodsReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * querySyncFailDetail
        * @method
        * @name querySyncFailDetail
        * @param  body - request
        */
        function optionsManualQuerySyncFailDetail (

            parameters : {
              'body'  : ApiRequestListReSyncOrgGoodsReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * querySyncFailDetail
        * @method
        * @name querySyncFailDetail
        * @param  body - request
        */
        function patchManualQuerySyncFailDetail (

            parameters : {
              'body'  : ApiRequestListReSyncOrgGoodsReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 保存活动
        * @method
        * @name 保存活动
        * @param  body - req
        */
        function postMarketingActivitySaveActivity (

            parameters : {
              'body'  : ApiRequestMarketingSaveActivityReqDto,

            }


        ): Promise < AxiosResponse >

        /**
        * 分页列表查询
        * @method
        * @name 分页列表查询
        * @param  body - req
        */
        function postMarketingActivityShowPageQuery (

            parameters : {
              'body'  : ApiRequestMarketingPageQueryReqDto,

            }


        ): Promise < AxiosResponse >

        /**
        * 查看活动详情
        * @method
        * @name 查看活动详情
        * @param  body - req
        */
        function postMarketingActivityShowQuery (

            parameters : {
              'body'  : ApiRequestMarketingPageQueryReqDto,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量查询商品活动和特价
        * @method
        * @name 批量查询商品活动和特价
        * @param  body - req
        */
        function postMarketingActivityShowQueryBatchList (

            parameters : {
              'body'  : ApiRequestMarketingBatchListReqDto,

            }


        ): Promise < AxiosResponse >

        /**
        * 专场展示
        * @method
        * @name 专场展示
        * @param  body - req
        */
        function postMarketingActivityStartSpecialActivity (

            parameters : {
              'body'  : ApiRequestMarketingPageQueryReqDto,

            }


        ): Promise < AxiosResponse >

        /**
        * 终止活动
        * @method
        * @name 终止活动
        * @param  body - req
        */
        function postMarketingActivityStopActivity (

            parameters : {
              'body'  : ApiRequestMarketingPageQueryReqDto,

            }


        ): Promise < AxiosResponse >

        /**
        * 取消专场
        * @method
        * @name 取消专场
        * @param  body - req
        */
        function postMarketingActivityStopSpecialActivity (

            parameters : {
              'body'  : ApiRequestMarketingPageQueryReqDto,

            }


        ): Promise < AxiosResponse >

        /**
        * 物料组列表
        * @method
        * @name 物料组列表
        * @param  body - apiRequest
        */
        function postMaterialGroupList (

            parameters : {
              'body'  : ApiRequestMaterialGroupListDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * aggregate
        * @method
        * @name aggregate
        * @param string collection - collection * @param  body - dto
        */
        function postMongoCommonAggregate (

            parameters : {
              'collection'  ?: string,
              'body'  : AggregateCommandDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * count
        * @method
        * @name count
        * @param string collection - collection * @param  body - dto
        */
        function postMongoCommonCount (

            parameters : {
              'collection'  ?: string,
              'body'  : CountCommandDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * delete
        * @method
        * @name delete
        * @param string collection - collection * @param  body - dto
        */
        function postMongoCommonDelete (

            parameters : {
              'collection'  ?: string,
              'body'  : DeleteCommandDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * find
        * @method
        * @name find
        * @param string collection - collection * @param  body - dto
        */
        function postMongoCommonFind (

            parameters : {
              'collection'  ?: string,
              'body'  : FindCommandDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * insert
        * @method
        * @name insert
        * @param string collection - collection * @param  body - dto
        */
        function postMongoCommonInsert (

            parameters : {
              'collection'  ?: string,
              'body'  : InsertCommandDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * update
        * @method
        * @name update
        * @param string collection - collection * @param  body - dto
        */
        function postMongoCommonUpdate (

            parameters : {
              'collection'  ?: string,
              'body'  : Array<UpdateCommandDTO>,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询商品组织列表
        * @method
        * @name 查询商品组织列表
        * @param  body - apiRequest
        */
        function postOrganizationList (

            parameters : {
              'body'  : ApiRequestOrganizationReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 缓存商品组织
        * @method
        * @name 缓存商品组织

        */
        function postOrganizationRefresh (





        ): Promise < AxiosResponse >

        /**
        * 删除商品组织
        * @method
        * @name 删除商品组织
        * @param  body - apiRequest
        */
        function postOrganizationRemove (

            parameters : {
              'body'  : ApiRequestOrganizationRemoveReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 保存商品组织
        * @method
        * @name 保存商品组织
        * @param  body - apiRequest
        */
        function postOrganizationSave (

            parameters : {
              'body'  : ApiRequestListOrganizationSaveReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询商品组织树
        * @method
        * @name 查询商品组织树
        * @param  body - apiRequest
        */
        function postOrganizationTree (

            parameters : {
              'body'  : ApiRequestOrganizationTreeReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 更新商品组织
        * @method
        * @name 更新商品组织
        * @param  body - apiRequest
        */
        function postOrganizationUpdate (

            parameters : {
              'body'  : ApiRequestOrganizationUpdateReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 新增渠道素材接口
        * @method
        * @name 新增渠道素材接口
        * @param  body - apiRequest
        */
        function postResourceAddChannelGroup (

            parameters : {
              'body'  : ApiRequestChannelResourceGroupDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 1.3.1.新增素材接口
        * @method
        * @name 1.3.1.新增素材接口
        * @param  body - apiRequest
        */
        function postResourceAddGroup (

            parameters : {
              'body'  : ApiRequestResourceAddGroupReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 2.5.3.取消主图角标接口
        * @method
        * @name 2.5.3.取消主图角标接口
        * @param  body - apiRequest
        */
        function postResourceCancelCorner (

            parameters : {
              'body'  : ApiRequestListResourceCancelCornerReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 1.3.2 新增资源
        * @method
        * @name 1.3.2 新增资源
        * @param  body - apiRequest
        */
        function postResourceCreate (

            parameters : {
              'body'  : ApiRequestResourceReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 1.3.3 删除素材
        * @method
        * @name 1.3.3 删除素材
        * @param  body - apiRequest
        */
        function postResourceDeleteGroup (

            parameters : {
              'body'  : ApiRequestResourceGroupDeleteReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 1.3.4 编辑资源
        * @method
        * @name 1.3.4 编辑资源
        * @param  body - apiRequest
        */
        function postResourceEdit (

            parameters : {
              'body'  : ApiRequestResourceUpdateWebReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 1.3.6.查询素材详情接口
        * @method
        * @name 1.3.6.查询素材详情接口
        * @param  body - apiRequest
        */
        function postResourceGetGroup (

            parameters : {
              'body'  : ApiRequestResourceGroupGetReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 1.3.5.查询素材列表接口
        * @method
        * @name 1.3.5.查询素材列表接口
        * @param  body - apiRequest
        */
        function postResourceListGroup (

            parameters : {
              'body'  : ApiRequestResourceGroupListReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 1.3.7 查询资源列表
        * @method
        * @name 1.3.7 查询资源列表
        * @param  body - apiRequest
        */
        function postResourceQuery (

            parameters : {
              'body'  : ApiRequestPageableReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 2.5.2.设置主图角标接口
        * @method
        * @name 2.5.2.设置主图角标接口
        * @param  body - apiRequest
        */
        function postResourceSetCorner (

            parameters : {
              'body'  : ApiRequestListResourceSetCornerReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 2.5.1(门店，运营)修改商品素材接口
        * @method
        * @name 2.5.1(门店，运营)修改商品素材接口
        * @param  body - apiRequest
        */
        function postResourceUpdateGoodsGroup (

            parameters : {
              'body'  : ApiRequestResourceUpdateGoodsGroupReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 1.3.3.编辑素材接口
        * @method
        * @name 1.3.3.编辑素材接口
        * @param  body - apiRequest
        */
        function postResourceUpdateGroup (

            parameters : {
              'body'  : ApiRequestResourceUpdateGroupReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 删除黑白名单
        * @method
        * @name 删除黑白名单
        * @param  body - request
        */
        function postRosterDelete (

            parameters : {
              'body'  : ApiRequestListBlackWhiteListReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 物理删除配送中心及配送中心下所有门店某个渠道的黑白名单
        * @method
        * @name 物理删除配送中心及配送中心下所有门店某个渠道的黑白名单
        * @param  body - request
        */
        function postRosterDeleteBlackWhiteListByCenterCode (

            parameters : {
              'body'  : ApiRequestBlackWhiteListReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.2.1.12.查询黑白名单
        * @method
        * @name 3.2.1.12.查询黑白名单
        * @param  body - request
        */
        function postRosterRosterList (

            parameters : {
              'body'  : ApiRequestBlackWhiteListQueryDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 新增黑白名单
        * @method
        * @name 新增黑白名单
        * @param  body - request
        */
        function postRosterSave (

            parameters : {
              'body'  : ApiRequestListBlackWhiteListReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 开通配送中心及中心下所有门店指定渠道的白名单
        * @method
        * @name 开通配送中心及中心下所有门店指定渠道的白名单
        * @param  body - request
        */
        function postRosterSaveCenterAndStoreWhiteList (

            parameters : {
              'body'  : ApiRequestBlackWhiteListReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.2.7.5 订单取消
        * @method
        * @name 3.2.7.5 订单取消
        * @param  body - request
        */
        function postSaleOrderOrderCancel (

            parameters : {
              'body'  : ApiRequestSaleOrderReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.2.7.1 销售下单
        * @method
        * @name 3.2.7.1 销售下单
        * @param  body - request
        */
        function postSaleOrderOrderCreate (

            parameters : {
              'body'  : ApiRequestSaleOrderReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.2.7.4 订单完成
        * @method
        * @name 3.2.7.4 订单完成
        * @param  body - request
        */
        function postSaleOrderOrderFinish (

            parameters : {
              'body'  : ApiRequestSaleOrderReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.2.7.2 销售单支付
        * @method
        * @name 3.2.7.2 销售单支付
        * @param  body - request
        */
        function postSaleOrderOrderPay (

            parameters : {
              'body'  : ApiRequestSaleOrderReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.2.7.6 整单退
        * @method
        * @name 3.2.7.6 整单退
        * @param  body - request
        */
        function postSaleOrderOrderReturned (

            parameters : {
              'body'  : ApiRequestSaleOrderReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.2.7.3 销售备货
        * @method
        * @name 3.2.7.3 销售备货
        * @param  body - request
        */
        function postSaleOrderOrderStock (

            parameters : {
              'body'  : ApiRequestSaleOrderReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 扣减SKU商品库存
        * @method
        * @name 扣减SKU商品库存
        * @param  body - apiRequest
        */
        function postSaleStockDeductStock (

            parameters : {
              'body'  : ApiRequestDeductionSaleStockReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询SKU商品库存列表
        * @method
        * @name 查询SKU商品库存列表
        * @param  body - apiRequest
        */
        function postSaleStockListStock (

            parameters : {
              'body'  : ApiRequestSaleStockListReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.2.8.3 查询能力-查询销售层库存
        * @method
        * @name 3.2.8.3 查询能力-查询销售层库存
        * @param  body - apiRequest
        */
        function postSaleStockQuerySaleStockByStore (

            parameters : {
              'body'  : ApiRequestPageableReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.8.4 查询门店可售商品列表
        * @method
        * @name 3.8.4 查询门店可售商品列表
        * @param  body - apiRequest
        */
        function postSaleStockQuerySaleableGoods (

            parameters : {
              'body'  : ApiRequestQuerySaleGoodsReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 规格管理-批量排序
        * @method
        * @name 规格管理-批量排序
        * @param  body - apiRequest
        */
        function postSkuSpecBatchSort (

            parameters : {
              'body'  : ApiRequestAdminSpecBatchSortBaseReq,

            }


        ): Promise < AxiosResponse >

        /**
        * 规格管理-批量保存
        * @method
        * @name 规格管理-批量保存
        * @param  body - apiRequest
        */
        function postSkuSpecBatchUpdate (

            parameters : {
              'body'  : ApiRequestListAdminBatchSaveReq,

            }


        ): Promise < AxiosResponse >

        /**
        * 渠道字典列表
        * @method
        * @name 渠道字典列表

        */
        function postSkuSpecChannelList (





        ): Promise < AxiosResponse >

        /**
        * 规格管理-复制sku规格
        * @method
        * @name 规格管理-复制sku规格
        * @param  body - apiRequest
        */
        function postSkuSpecCopyGoods (

            parameters : {
              'body'  : ApiRequestAdminSpecCopySkuReq,

            }


        ): Promise < AxiosResponse >

        /**
        * 规格管理-删除发布
        * @method
        * @name 规格管理-删除发布
        * @param  body - apiRequest
        */
        function postSkuSpecDeletePublish (

            parameters : {
              'body'  : ApiRequestAdminPublishReq,

            }


        ): Promise < AxiosResponse >

        /**
        * 规格管理-获取配送中心下城市门店信息-回显(仅返回门店id集合)/仅展示已发布门店(返回同基本列表一样的数据格式)
        * @method
        * @name 规格管理-获取配送中心下城市门店信息-回显(仅返回门店id集合)/仅展示已发布门店(返回同基本列表一样的数据格式)
        * @param  body - apiRequest
        */
        function postSkuSpecDistributionBackShow (

            parameters : {
              'body'  : ApiRequestAdminSpecDistributionBackShowReq,

            }


        ): Promise < AxiosResponse >

        /**
        * 规格管理-获取配送中心下城市门店信息
        * @method
        * @name 规格管理-获取配送中心下城市门店信息
        * @param  body - apiRequest
        */
        function postSkuSpecDistributionList (

            parameters : {
              'body'  : ApiRequestAdminSpecDistributionReq,

            }


        ): Promise < AxiosResponse >

        /**
        * 规格管理-查询配送中心和商品品级
        * @method
        * @name 规格管理-查询配送中心和商品品级
        * @param  body - apiRequest
        */
        function postSkuSpecGetBasicInfo (

            parameters : {
              'body'  : ApiRequestAdminSkuSpecListReq,

            }


        ): Promise < AxiosResponse >

        /**
        * 规格管理-批量查询门店组别商品
        * @method
        * @name 规格管理-批量查询门店组别商品
        * @param  body - apiRequest
        */
        function postSkuSpecGetStoreGroupSkuList (

            parameters : {
              'body'  : ApiRequestAdminStoreGroupSkuListReq,

            }


        ): Promise < AxiosResponse >

        /**
        * 规格管理-获取sku规格列表
        * @method
        * @name 规格管理-获取sku规格列表
        * @param  body - apiRequest
        */
        function postSkuSpecList (

            parameters : {
              'body'  : ApiRequestAdminSkuSpecListReq,

            }


        ): Promise < AxiosResponse >

        /**
        * 规格管理-一键发布
        * @method
        * @name 规格管理-一键发布
        * @param  body - apiRequest
        */
        function postSkuSpecOneKeyPublish (

            parameters : {
              'body'  : ApiRequestAdminPublishReq,

            }


        ): Promise < AxiosResponse >

        /**
        * 规格管理-发布
        * @method
        * @name 规格管理-发布
        * @param  body - apiRequest
        */
        function postSkuSpecPublish (

            parameters : {
              'body'  : ApiRequestAdminPublishReq,

            }


        ): Promise < AxiosResponse >

        /**
        * 规格管理-上下架
        * @method
        * @name 规格管理-上下架
        * @param  body - apiRequest
        */
        function postSkuSpecPutAway (

            parameters : {
              'body'  : ApiRequestBaseAdminPutAwayReq,

            }


        ): Promise < AxiosResponse >

        /**
        * 规格管理-设置是否默认上架
        * @method
        * @name 规格管理-设置是否默认上架
        * @param  body - apiRequest
        */
        function postSkuSpecSetDefaultPutAway (

            parameters : {
              'body'  : ApiRequestAdminSpecDefaultPutAwayReq,

            }


        ): Promise < AxiosResponse >

        /**
        * 规格管理-获取门店组列表
        * @method
        * @name 规格管理-获取门店组列表
        * @param  body - apiRequest
        */
        function postSkuSpecStoreGroupDataList (

            parameters : {
              'body'  : ApiRequestAdminSkuSpecListReq,

            }


        ): Promise < AxiosResponse >

        /**
        * 新增SkuGoods信息
        * @method
        * @name 新增SkuGoods信息
        * @param  body - apiRequest
        */
        function postSkuGoodsAddSkuGoods (

            parameters : {
              'body'  : ApiRequestsku_goods,

            }


        ): Promise < AxiosResponse >

        /**
        * 新增sku商品
        * @method
        * @name 新增sku商品
        * @param  body - req
        */
        function postSkuGoodsAdminSaveSkuGoods (

            parameters : {
              'body'  : ApiRequestSaveSkuGoodsRequest,

            }


        ): Promise < AxiosResponse >

        /**
        * 更新sku商品
        * @method
        * @name 更新sku商品
        * @param  body - req
        */
        function postSkuGoodsAdminUpdateSkuGoods (

            parameters : {
              'body'  : ApiRequestSaveSkuGoodsRequest,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量操作sku商品
        * @method
        * @name 批量操作sku商品
        * @param  body - req
        */
        function postSkuGoodsBatchUpdateSkuGoods (

            parameters : {
              'body'  : ApiRequestBatchUpdateSkuGoodsRequest,

            }


        ): Promise < AxiosResponse >

        /**
        * 删除SkuGoods信息
        * @method
        * @name 删除SkuGoods信息
        * @param  body - apiRequest
        */
        function postSkuGoodsDeleteSkuGoods (

            parameters : {
              'body'  : ApiRequestsku_goods,

            }


        ): Promise < AxiosResponse >

        /**
        * 生成sku商品编号
        * @method
        * @name 生成sku商品编号
        * @param  body - req
        */
        function postSkuGoodsGenerateSkuGoodsNumber (

            parameters : {
              'body'  : ApiRequestSkuGoods,

            }


        ): Promise < AxiosResponse >

        /**
        * 调整分类-回显
        * @method
        * @name 调整分类-回显
        * @param  body - req
        */
        function postSkuGoodsGetGoodsCategoryShowBack (

            parameters : {
              'body'  : ApiRequestAdminGoodsCategoryShowBackReq,

            }


        ): Promise < AxiosResponse >

        /**
        * 发布分类
        * @method
        * @name 发布分类
        * @param  body - req
        */
        function postSkuGoodsPublishCategory (

            parameters : {
              'body'  : ApiRequestAdminSpecDistributionReq,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据配置名称查询配置活动的系数
        * @method
        * @name 根据配置名称查询配置活动的系数
        * @param  body - req
        */
        function postSkuGoodsQueryConfigNameRatio (

            parameters : {
              'body'  : ApiRequestQueryConfigValueRequest,

            }


        ): Promise < AxiosResponse >

        /**
        * 获取所有有效的配送中心
        * @method
        * @name 获取所有有效的配送中心
        * @param  body - req
        */
        function postSkuGoodsQueryDistributionList (

            parameters : {
              'body'  : ApiRequestQueryDistributionListReq,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询所有有效的商品标签
        * @method
        * @name 查询所有有效的商品标签

        */
        function postSkuGoodsQueryGoodsLableList (





        ): Promise < AxiosResponse >

        /**
        * 查询SkuGoods信息
        * @method
        * @name 查询SkuGoods信息
        * @param  body - apiRequest
        */
        function postSkuGoodsQuerySkuGoods (

            parameters : {
              'body'  : ApiRequestsku_goods,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据商品ID查询已绑定的标签列表
        * @method
        * @name 根据商品ID查询已绑定的标签列表
        * @param  body - req
        */
        function postSkuGoodsQuerySkuGoodsLabelList (

            parameters : {
              'body'  : ApiRequestSkuGoods,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询spu商品信息
        * @method
        * @name 查询spu商品信息
        * @param  body - req
        */
        function postSkuGoodsQuerySpuGoodsBySpuNumber (

            parameters : {
              'body'  : ApiRequestSpuGoodsListReq,

            }


        ): Promise < AxiosResponse >

        /**
        * 调整分类-保存
        * @method
        * @name 调整分类-保存
        * @param  body - req
        */
        function postSkuGoodsSaveGoodsCategoryRel (

            parameters : {
              'body'  : ApiRequestAdminSaveGoodsCategoryRelReq,

            }


        ): Promise < AxiosResponse >

        /**
        * 设置活动价区间
        * @method
        * @name 设置活动价区间
        * @param  body - req
        */
        function postSkuGoodsSetSVipActivityPrice (

            parameters : {
              'body'  : ApiRequestSetGoodsSpecialPriceRequest,

            }


        ): Promise < AxiosResponse >

        /**
        * 设置心享价
        * @method
        * @name 设置心享价
        * @param  body - req
        */
        function postSkuGoodsSetSkuGoodsVipPrice (

            parameters : {
              'body'  : ApiRequestSetGoodsVipPriceRequest,

            }


        ): Promise < AxiosResponse >

        /**
        * 分页查询sku商品
        * @method
        * @name 分页查询sku商品
        * @param  body - req
        */
        function postSkuGoodsShowPageQuery (

            parameters : {
              'body'  : ApiRequestPageableReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询sku商品详情
        * @method
        * @name 查询sku商品详情
        * @param  body - req
        */
        function postSkuGoodsShowQuery (

            parameters : {
              'body'  : ApiRequestSkuGoods,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量设置sku商品标签
        * @method
        * @name 批量设置sku商品标签
        * @param  body - req
        */
        function postSkuGoodsSkuGoodsSetLabel (

            parameters : {
              'body'  : ApiRequestBatchSetSkuGoodsLabelRequest,

            }


        ): Promise < AxiosResponse >

        /**
        * 更新SkuGoods信息
        * @method
        * @name 更新SkuGoods信息
        * @param  body - apiRequest
        */
        function postSkuGoodsUpdateSkuGoods (

            parameters : {
              'body'  : ApiRequestsku_goods,

            }


        ): Promise < AxiosResponse >

        /**
        * 新增SpuGoods信息
        * @method
        * @name 新增SpuGoods信息
        * @param  body - apiRequest
        */
        function postSpuGoodsAddSpuGoods (

            parameters : {
              'body'  : ApiRequestspu_goods,

            }


        ): Promise < AxiosResponse >

        /**
        * 删除SpuGoods信息
        * @method
        * @name 删除SpuGoods信息
        * @param  body - apiRequest
        */
        function postSpuGoodsDeleteSpuGoods (

            parameters : {
              'body'  : ApiRequestspu_goods,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询SpuGoods信息
        * @method
        * @name 查询SpuGoods信息
        * @param  body - apiRequest
        */
        function postSpuGoodsQuerySpuGoods (

            parameters : {
              'body'  : ApiRequestspu_goods,

            }


        ): Promise < AxiosResponse >

        /**
        * 更新SpuGoods信息
        * @method
        * @name 更新SpuGoods信息
        * @param  body - apiRequest
        */
        function postSpuGoodsUpdateSpuGoods (

            parameters : {
              'body'  : ApiRequestspu_goods,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.2.9.3库存分布式事务-取消(cancel)
        * @method
        * @name 3.2.9.3库存分布式事务-取消(cancel)
        * @param  body - apiRequest
        */
        function postStockCancel (

            parameters : {
              'body'  : ApiRequestStockTccConfirmDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.2.9.2库存分布式事务-确认(confirm)
        * @method
        * @name 3.2.9.2库存分布式事务-确认(confirm)
        * @param  body - apiRequest
        */
        function postStockConfirm (

            parameters : {
              'body'  : ApiRequestStockTccConfirmDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * renewal
        * @method
        * @name renewal
        * @param  body - request
        */
        function getStockRenewal (

            parameters : {
              'body'  : ApiRequestListStockUpdateReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * renewal
        * @method
        * @name renewal
        * @param  body - request
        */
        function headStockRenewal (

            parameters : {
              'body'  : ApiRequestListStockUpdateReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * renewal
        * @method
        * @name renewal
        * @param  body - request
        */
        function postStockRenewal (

            parameters : {
              'body'  : ApiRequestListStockUpdateReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * renewal
        * @method
        * @name renewal
        * @param  body - request
        */
        function putStockRenewal (

            parameters : {
              'body'  : ApiRequestListStockUpdateReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * renewal
        * @method
        * @name renewal
        * @param  body - request
        */
        function deleteStockRenewal (

            parameters : {
              'body'  : ApiRequestListStockUpdateReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * renewal
        * @method
        * @name renewal
        * @param  body - request
        */
        function optionsStockRenewal (

            parameters : {
              'body'  : ApiRequestListStockUpdateReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * renewal
        * @method
        * @name renewal
        * @param  body - request
        */
        function patchStockRenewal (

            parameters : {
              'body'  : ApiRequestListStockUpdateReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.2.9.1库存分布式事务-占用(try)
        * @method
        * @name 3.2.9.1库存分布式事务-占用(try)
        * @param  body - apiRequest
        */
        function postStockTry (

            parameters : {
              'body'  : ApiRequestStockTccDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 门店列表
        * @method
        * @name 门店列表
        * @param  body - apiRequest
        */
        function postStoreV1ListStore (

            parameters : {
              'body'  : ApiRequestExampleSimpleInputDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * coverUpdateWarehouseStock
        * @method
        * @name coverUpdateWarehouseStock
        * @param  body - request
        */
        function postWarehouseStockCoverUpdateWarehouseStock (

            parameters : {
              'body'  : ApiRequestSetWarehouseStockCoverReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 电商仓库层库存初始化
        * @method
        * @name 电商仓库层库存初始化
        * @param  body - apiRequest
        */
        function postWarehouseStockEshopStockAuthorization (

            parameters : {
              'body'  : ApiRequestListEshopSaleStockGoodsAuthorizationDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * incrWarehouseTotalStock
        * @method
        * @name incrWarehouseTotalStock
        * @param  body - request
        */
        function postWarehouseStockIncrWarehouseTotalStock (

            parameters : {
              'body'  : ApiRequestSetWarehouseStockUpdateReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * queryErpStoreStock
        * @method
        * @name queryErpStoreStock
        * @param  body - request
        */
        function postWarehouseStockQueryErpStoreStock (

            parameters : {
              'body'  : ApiRequestQueryWarehouseStoreDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.2.8.4 查询能力-查询商品铺市率
        * @method
        * @name 3.2.8.4 查询能力-查询商品铺市率
        * @param  body - apiRequest
        */
        function postWarehouseStockQueryGoodsMarketRate (

            parameters : {
              'body'  : ApiRequestWarehouseStockRateReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.2.8.1 查询能力-查询库存值
        * @method
        * @name 3.2.8.1 查询能力-查询库存值
        * @param  body - apiRequest
        */
        function postWarehouseStockQueryWarehouseStock (

            parameters : {
              'body'  : ApiRequestQueryWarehouseStockReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据筛选条件查询门店仓库层信息
        * @method
        * @name 根据筛选条件查询门店仓库层信息
        * @param  body - apiRequest
        */
        function postWarehouseStockQueryWarehouseStockInfoList (

            parameters : {
              'body'  : ApiRequestQueryWarehouseStockReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 3.2.8.2 查询能力-查询库存值列表
        * @method
        * @name 3.2.8.2 查询能力-查询库存值列表
        * @param  body - apiRequest
        */
        function postWarehouseStockQueryWarehouseStockList (

            parameters : {
              'body'  : ApiRequestPageableReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * searchGoodsWarehouseStock
        * @method
        * @name searchGoodsWarehouseStock
        * @param  body - request
        */
        function postWarehouseStockSearchGoodsWarehouseStock (

            parameters : {
              'body'  : ApiRequestPageableReqDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * setStock
        * @method
        * @name setStock
        * @param  body - request
        */
        function postWarehouseStockSetStock (

            parameters : {
              'body'  : ApiRequestSetWarehouseStockDTO,

            }


        ): Promise < AxiosResponse >

        /**
        * 更新SPU自定义库存
        * @method
        * @name 更新SPU自定义库存
        * @param  body - apiRequest
        */
        function postWarehouseStockUpdateWarehouseStockCustomInventory (

            parameters : {
              'body'  : ApiRequestSetWarehouseStockCustomInventoryReqDTO,

            }


        ): Promise < AxiosResponse >

      }
  }
}
export {}

