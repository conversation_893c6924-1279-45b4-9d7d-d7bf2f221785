let isBuild = true

if (process.env.MODE && process.env.MODE != 'build') {
  isBuild = false
}
const path = require('path')

module.exports = {
  type: 'mysql',
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  username: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  maxQueryExecutionTime: 150,
  charset: 'utf8mb4',
  logging: process.env.NODE_ENV === 'local' ? true : false,
  entities: isBuild
    ? [path.resolve(__dirname, 'dist/business/**/*middleground.code.entity{.ts,.js}')]
    : [path.resolve(__dirname, 'src/business/**/*middleground.code.entity{.ts,.js}')]
}
