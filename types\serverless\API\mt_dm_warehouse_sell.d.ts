/*this is generated code */
import  { AxiosResponse } from 'axios'
declare global{
  namespace API {
    /** mt-dm-warehouse-sell
      * 文档链接：http://op.pagoda.com.cn
      * {"contact":{},"description":"仓配域","title":"1.0.0","version":"1.0.0"}
      * ************:8080
    */
    namespace mt_dm_warehouse_sell {

        type BasDriver对象 = {

            code?:   string

            lastModifiedAt?:   string

            loadingWeight?:   number

            creatorName?:   string

            payAcountNo?:   string

            registeBank?:   string

            remark?:   string

            dualOrgName?:   string

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            driverIdNumber?:   string

            id?:   number

            modifierOrgCode?:   string

            loadingVolume?:   number

            creatorCode?:   string

            entId?:   number

            dualOrgCode?:   string

            mobile?:   string

            plateNum?:   string

            version?:   number

            deleted?:   number

            isEnabled?:   number

            dualOrgId?:   number

            name?:   string

            searchWord?:   string

            modifierName?:   string

            shortNum?:   string

        }

        type SalOrderOutput = {

            head?:  SalOrderHeadOutput

            updateGoodsTypeCode?:   string

            updateTemplateCode?:   string

            details?:    Array < SalOrderDetailOutput >

            isExeBatchPolicy?:   number

        }

        type BasWrapperWithGoodsUpdateInputDTO = {

            mainCardinal?:   number

            mainGoodsClassCode?:   string

            wrapGoodsClassCode?:   string

            dualOrgCode?:   string

            isEnabled?:   number

            isRound?:   number

            id?:   number

            wrapCardinal?:   number

            version?:   number

        }

        type BatchDeleteCustomerOrderInput = {

            ordersInfo?:    Array < DeleteCustomerOrderHeadInput >

        }

        type FindSalCusReturnReserveListInput = {

            conOrgClassCodeList?:    Array < string >

            cusOrgCodeList?:    Array < string >

            goodsCodeList?:    Array < string >

            goodsClassCodeList?:    Array < string >

            entId?:   number

            isEnabled?:   number

            creatorName?:   string

            conOrgCodeList?:    Array < string >

            cusClassCodeList?:    Array < string >

        }

        type ApiResultSalOrderControl对象 = {

            data?:  SalOrderControl对象

            resultCode:   number

            errorMsg?:   string

        }

        type QueryMustAndCycleGoodsInput = {

            cusOrgCode?:   string

            entId?:   number

            goodsTypeCode?:   string

            dualOrgCode?:   string

            goodsId?:   number

            cusOrgId?:   number

            dualOrgName?:   string

            templateCode?:   string

            preArrivalDate?:   string

            cusOrgName?:   string

            dualOrgId?:   number

            inputModel?:   number

            goodsCode?:   string

            isTakeCycle?:   number

            queryModel?:   number

            goodsName?:   string

        }

        type CreateDetailInputDTO = {

            goodsClassCode?:   string

            goodsTypeCode?:   string

            goodsTypeId?:   number

            dualOrgCode?:   string

            goodsId?:   number

            goodsTypeName?:   string

            goodsClassId?:   number

            goodsGroupCode?:   string

            goodsCode?:   string

            goodsClassName?:   string

            goodsName?:   string

        }

        type MakeDailyCostInput = {

            id?:   number

            version?:   number

        }

        type ApiResultPageBasRoute对象 = {

            data?:  PageBasRoute对象

            resultCode:   number

            errorMsg?:   string

        }

        type SalOrderDetailOutput = {

            discountRate?:   number

            orderId?:   number

            goodsTypeName?:   string

            isExeBatchPolicy?:   number

            modifierCode?:   string

            createdAt?:   string

            maxGoodsShelf?:   number

            requestQty?:   number

            conOrgName?:   string

            conOrgId?:   number

            price?:   number

            batchQty?:   number

            id?:   number

            modifierOrgCode?:   string

            goodsName?:   string

            goodsVarId?:   number

            taxAmt?:   number

            goodsAmt?:   number

            creatorCode?:   string

            entId?:   number

            version?:   number

            goodsSpec?:   string

            onhandQty?:   number

            taxRate?:   number

            totalAmt?:   number

            maxOrderQty?:   number

            name?:   string

            goodsVarName?:   string

            conOrgCode?:   string

            salUnitCode?:   string

            discountAmt?:   number

            code?:   string

            d3SaleQty?:   number

            lastModifiedAt?:   string

            sourceTypes?:    Array < number >

            goodsId?:   number

            creatorName?:   string

            discountPrice?:   number

            remark?:   string

            mustFlag?:   number

            creatorOrgCode?:   string

            orderSeqno?:   string

            salUnitRate?:   number

            batchPolicy?:   number

            minGoodsShelf?:   number

            salUnitid?:   number

            goodsTypeCode?:   string

            d3DebtQty?:   number

            d7DebtQty?:   number

            errorMsg?:   string

            deleted?:   number

            sourceNos?:    Array < string >

            d7SaleQty?:   number

            goodsTypeId?:   number

            salUnitName?:   string

            goodsCode?:   string

            modifierName?:   string

        }

        type DeleteSalCusReturnReserveInput = {

            idList?:    Array < number >

        }

        type UpdateBasRoute = {

            isEnable?:   number

            updateList?:    Array < UpdateRouteInput >

        }

        type FindBasRouteListByAuthInput_1 = {

            code?:   string

            codeOrName?:   string

            dualOrgCodeList?:    Array < string >

            isEnabled?:   number

            name?:   string

            receivePlace?:   string

            sendPlace?:   string

        }

        type BasRoute对象 = {

            code?:   string

            lastModifiedAt?:   string

            creatorName?:   string

            dualOrgName?:   string

            receivePlace?:   string

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            id?:   number

            modifierOrgCode?:   string

            tempZoneTypeCode?:   string

            tempZoneTypeId?:   number

            sendPlace?:   string

            creatorCode?:   string

            entId?:   number

            dualOrgCode?:   string

            version?:   number

            deleted?:   number

            waveNum?:   number

            isEnabled?:   number

            dualOrgId?:   number

            name?:   string

            searchWord?:   string

            modifierName?:   string

            tempZoneTypeName?:   string

        }

        type FindBasCusRouteListOutput = {

            code?:   string

            lastModifiedAt?:   string

            cusOrgCode?:   string

            dualOrgCode?:   string

            cusOrgId?:   number

            cusClassCode?:   string

            dualOrgName?:   string

            version?:   number

            receivePlace?:   string

            cusClassName?:   string

            routeId?:   number

            cusOrgName?:   string

            isEnabled?:   number

            loadingSeq?:   number

            dualOrgId?:   number

            name?:   string

            id?:   number

            modifierName?:   string

            tempZoneTypeName?:   string

            cusClassId?:   number

            tempZoneTypeCode?:   string

            tempZoneTypeId?:   number

        }

        type UpdateCustomerOrderInput = {

            head?:  UpdateCustomerOrderHeadInput

            details?:    Array < SalOrderDetail对象 >

        }

        type BatchAdjustControlInput = {

            delayTime?:   number

            templateInfoList?:    Array < CreateDistributeTemplateInput >

            status?:   number

            updateList?:    Array < SalDistributeControl对象 >

        }

        type ApiResultPageDeliveryCenter对象 = {

            data?:  PageDeliveryCenter对象

            resultCode:   number

            errorMsg?:   string

        }

        type FindSalConsignPrintListInput = {

            goodsCodeList?:    Array < string >

            goodsClassCode?:   string

            entId?:   number

            isEnabled?:   number

            conOrgCodeList?:    Array < string >

            conOrgCode?:   string

            goodsTypeCodeList?:    Array < string >

            tempZoneTypeCode?:   string

        }

        type PageCreateDistributeTemplateInput = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < CreateDistributeTemplateInput >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type ApiResultListBasLookupValue对象 = {

            data?:    Array < BasLookupValue对象 >

            resultCode:   number

            errorMsg?:   string

        }

        type PageBasReturnRules对象 = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < BasReturnRules对象 >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type FindDailyTransRouteListByAuthInput = {

            salDepotCodeList?:    Array < string >

            codeOrName?:   string

            driverCodeList?:    Array < string >

            goodsClassCodeList?:    Array < string >

            entId?:   number

            dualOrgCodeList?:    Array < string >

            transDateArrays?:    Array < string >

            cusClassCodeList?:    Array < string >

            conOrgClassCodeList?:    Array < string >

            cusOrgCodeList?:    Array < string >

            salDepotClassCodeList?:    Array < string >

            routeCodeList?:    Array < string >

            transDate?:   string

            conOrgCodeList?:    Array < string >

        }

        type PageableDTOSalOrderPickingCfgSearchInputDTO = {

            pageable?:  Pageable

            dto?:  SalOrderPickingCfgSearchInputDTO

        }

        type PageableDTORetrieveDeliveryCenterInput = {

            pageable?:  Pageable

            dto?:  RetrieveDeliveryCenterInput

        }

        type PageableDTOQueryInsteadOrderInput = {

            pageable?:  Pageable

            dto?:  QueryInsteadOrderInput

        }

        type DeleteCusRouteInput = {

            id?:   number

            version?:   number

        }

        type RetrieveDeliveryCenterInput = {

            createdAtStart?:   string

            code?:   string

            entId?:   number

            lastModifiedAtStart?:   string

            isEnabled?:   number

            creatorName?:   string

            name?:   string

            modifierName?:   string

            createdAtEnd?:   string

            lastModifiedAtEnd?:   string

        }

        type PageDeliveryCenter对象 = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < DeliveryCenter对象 >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type PageableDTOSalAuthAdapter = {

            pageable?:  Pageable

            dto?:  SalAuthAdapter_1

        }

        type PageableDTOQueryStockUpOrderInput = {

            pageable?:  Pageable

            dto?:  QueryStockUpOrderInput

        }

        type ApiResultPageBasReturnGoods对象 = {

            data?:  PageBasReturnGoods对象

            resultCode:   number

            errorMsg?:   string

        }

        type PageSalGoodsGroupDetail对象 = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < SalGoodsGroupDetail对象 >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type ApiResultListCreateDistributeTemplateInput = {

            data?:    Array < CreateDistributeTemplateInput >

            resultCode:   number

            errorMsg?:   string

        }

        type PageableDTOFindBasCusRouteListInput = {

            pageable?:  Pageable

            dto?:  FindBasCusRouteListInput

        }

        type BasLookupValue对象 = {

            code?:   string

            lastModifiedAt?:   string

            creatorName?:   string

            refValue?:   string

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            parentCode?:   string

            id?:   number

            modifierOrgCode?:   string

            refName?:   string

            sourceFlag?:   number

            value?:   string

            creatorCode?:   string

            entId?:   number

            isShowFlag?:   number

            version?:   number

            parentId?:   number

            deleted?:   number

            valueDesc?:   string

            isEnabled?:   number

            name?:   string

            searchWord?:   string

            showOrder?:   number

            modifierName?:   string

            refId?:   number

            refCode?:   string

        }

        type ApiResultBasCusRoute对象 = {

            data?:  BasCusRoute对象

            resultCode:   number

            errorMsg?:   string

        }

        type PageableDTOSalOrgBasketSearchInputDTO = {

            pageable?:  Pageable

            dto?:  SalOrgBasketSearchInputDTO

        }

        type ApiResultPageBasWrapperWithGoods对象 = {

            data?:  PageBasWrapperWithGoods对象

            resultCode:   number

            errorMsg?:   string

        }

        type UpdateInput = {

            mainGoodsClassCode?:   string

            goodsClassCode?:   string

            cusOrgCode?:   string

            wrapGoodsClassCode?:   string

            dualOrgCode?:   string

            cusClassCode?:   string

            conOrgCode?:   string

            id?:   number

            version?:   number

            conOrgClassCode?:   string

        }

        type PageFindSalCusReturnReserveListOutput = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < FindSalCusReturnReserveListOutput >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type PageableDTOSalAuthAdapter_1 = {

            pageable?:  Pageable

            dto?:  SalAuthAdapter_2

        }

        type ApiResultPageBasDriver对象 = {

            data?:  PageBasDriver对象

            resultCode:   number

            errorMsg?:   string

        }

        type PageableDTOFindSalCusReturnReserveListInput = {

            pageable?:  Pageable

            dto?:  FindSalCusReturnReserveListInput

        }

        type SalGoodsGroupHead对象 = {

            creatorCode?:   string

            lastModifiedAt?:   string

            goodsGroupName?:   string

            entId?:   number

            dualOrgCode?:   string

            creatorName?:   string

            goodsTypeNum?:   number

            remark?:   string

            dualOrgName?:   string

            version?:   number

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            deleted?:   number

            dualOrgId?:   number

            goodsGroupCode?:   string

            id?:   number

            modifierName?:   string

            modifierOrgCode?:   string

        }

        type PageBasRoute对象 = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < BasRoute对象 >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type ApiResultPageSalOrderPickingCfgHead对象 = {

            data?:  PageSalOrderPickingCfgHead对象

            resultCode:   number

            errorMsg?:   string

        }

        type BasCusRoute对象 = {

            creatorCode?:   string

            lastModifiedAt?:   string

            cusOrgCode?:   string

            entId?:   number

            cusOrgId?:   number

            creatorName?:   string

            cusClassCode?:   string

            version?:   number

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            cusClassName?:   string

            deleted?:   number

            routeId?:   number

            cusOrgName?:   string

            isEnabled?:   number

            loadingSeq?:   number

            id?:   number

            modifierName?:   string

            cusClassId?:   number

            modifierOrgCode?:   string

        }

        type SalAuthAdapter_2 = {

            salDepotCodeList?:    Array < string >

            createdAtAfter?:   string

            cusOrgCode?:   string

            goodsClassCodeList?:    Array < string >

            entId?:   number

            goodsTypeCode?:   string

            dualOrgCode?:   string

            dualOrgCodeList?:    Array < string >

            templateCodeOrName?:   string

            creatorName?:   string

            createdAtBefore?:   string

            cusClassCodeList?:    Array < string >

            nowTime?:   string

            conOrgClassCodeList?:    Array < string >

            cusOrgCodeList?:    Array < string >

            salDepotClassCodeList?:    Array < string >

            isEnabled?:   number

            conOrgCodeList?:    Array < string >

            isTakeCycle?:   number

        }

        type SalDailyTransExpensesUpdateAppStatusInputDTO = {

            updateStatus?:   number

            updateInput?:    Array < UpdateInput >

        }

        type PageableDTOFindDailyTransRouteListByAuthInput = {

            pageable?:  Pageable

            dto?:  FindDailyTransRouteListByAuthInput

        }

        type SalAuthAdapter_1 = {

            salDepotCodeList?:    Array < string >

            pageNumber?:   number

            templateCodeList?:    Array < string >

            goodsClassCodeList?:    Array < string >

            entId?:   number

            dualOrgCode?:   string

            dualOrgCodeList?:    Array < string >

            templateCodeOrName?:   string

            lastModifiedAtAfter?:   string

            pageSize?:   number

            pageable?:  Pageable

            cusClassCodeList?:    Array < string >

            conOrgClassCodeList?:    Array < string >

            cusOrgCodeList?:    Array < string >

            salDepotClassCodeList?:    Array < string >

            sequence?:   string

            isControlEnabled?:   number

            lastModifiedAtBefore?:   string

            waveNum?:   number

            property?:   string

            conOrgCodeList?:    Array < string >

            delayTime?:   number

            modifierName?:   string

        }

        type PageableDTOQueryAppendOrderInput = {

            pageable?:  Pageable

            dto?:  QueryAppendOrderInput

        }

        type PageableDTOSalGoodsGroupSearchInputDTO = {

            pageable?:  Pageable

            dto?:  SalGoodsGroupSearchInputDTO

        }

        type ApiResultPageFindBasCusRouteListOutput = {

            data?:  PageFindBasCusRouteListOutput

            resultCode:   number

            errorMsg?:   string

        }

        type ApiResultPageFindDailyTransRouteListOutput = {

            data?:  PageFindDailyTransRouteListOutput

            resultCode:   number

            errorMsg?:   string

        }

        type PageSalOrderControl对象 = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < SalOrderControl对象 >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type SalOrderControlUpdateWithSaleInputDTO = {

            isTempZone?:   number

            beginConCycleTime?:   string

            cusOrgCode?:   string

            endOrderTime?:   string

            cusOrgId?:   number

            cusClassCode?:   string

            endConCycleTime?:   string

            remark?:   string

            isCtrlReturn?:   number

            version?:   number

            beginPreOrderTime?:   string

            endPreOrderTime?:   string

            cusClassName?:   string

            waveNum?:   number

            cusOrgName?:   string

            isEnabled?:   number

            beginReturnTime?:   string

            endReturnTime?:   string

            beginOrderTime?:   string

            id?:   number

            cusClassId?:   number

        }

        type PageBasWrapperWithGoods对象 = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < BasWrapperWithGoods对象 >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type SalConsignPrintMapping对象 = {

            mergeConOrgName?:   string

            lastModifiedAt?:   string

            goodsId?:   number

            goodsTypeName?:   string

            creatorName?:   string

            depotId?:   number

            goodsClassId?:   number

            mergeGoodsClassName?:   string

            creatorOrgCode?:   string

            mergeGoodsClassId?:   number

            modifierCode?:   string

            createdAt?:   string

            conOrgName?:   string

            conOrgId?:   number

            id?:   number

            modifierOrgCode?:   string

            goodsName?:   string

            tempZoneTypeCode?:   string

            mergeConOrgId?:   number

            depotName?:   string

            creatorCode?:   string

            mergeGoodsClassCode?:   string

            entId?:   number

            goodsTypeCode?:   string

            mergeConOrgCode?:   string

            goodsClassName?:   string

            version?:   number

            mergeTempZoneTypeCode?:   string

            deleted?:   number

            goodsClassCode?:   string

            goodsTypeId?:   number

            isEnabled?:   number

            depotCode?:   string

            conOrgCode?:   string

            goodsCode?:   string

            modifierName?:   string

        }

        type ApiResultSalConsignPrintMapping对象 = {

            data?:  SalConsignPrintMapping对象

            resultCode:   number

            errorMsg?:   string

        }

        type FindSalCusReturnReserveListOutput = {

            lastModifiedAt?:   string

            cusOrgCode?:   string

            goodsId?:   number

            cusOrgId?:   number

            creatorName?:   string

            cusClassCode?:   string

            goodsClassId?:   number

            remark?:   string

            createdAt?:   string

            conOrgName?:   string

            conOrgId?:   number

            id?:   number

            conOrgClassId?:   number

            cusClassId?:   number

            goodsName?:   string

            conOrgClassName?:   string

            entId?:   number

            goodsClassName?:   string

            version?:   number

            goodsSpec?:   string

            isEnabledStr?:   string

            cusClassName?:   string

            goodsClassCode?:   string

            cusOrgName?:   string

            isEnabled?:   number

            conOrgCode?:   string

            goodsCode?:   string

            modifierName?:   string

            reserveQty?:   number

            conOrgClassCode?:   string

        }

        type UpdateBasDriver = {

            isEnable?:   number

            updateList?:    Array < UpdateDriverInput >

        }

        type UpdateCusRouteInput = {

            routeCode?:   string

            cusOrgCode?:   string

            dualOrgCode?:   string

            cusOrgId?:   number

            cusClassCode?:   string

            index?:   number

            dualOrgName?:   string

            version?:   number

            cusClassName?:   string

            routeId?:   number

            cusOrgName?:   string

            isEnabled?:   number

            loadingSeq?:   number

            dualOrgId?:   number

            name?:   string

            id?:   number

            cusClassId?:   number

            tempZoneTypeCode?:   string

        }

        type SalDistributeTemplate对象 = {

            lastModifiedAt?:   string

            endOrderTime?:   string

            isCtrlTodayPayment?:   number

            creatorName?:   string

            remark?:   string

            dualOrgName?:   string

            templateCode?:   string

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            beginOrderTime?:   string

            id?:   number

            isTakeCycle?:   number

            isDepInput?:   number

            modifierOrgCode?:   string

            creatorCode?:   string

            entId?:   number

            goodsTypeCode?:   string

            dualOrgCode?:   string

            isCtrlReturn?:   number

            version?:   number

            deleted?:   number

            templateName?:   string

            isEnabled?:   number

            dualOrgId?:   number

            modifierName?:   string

            isCtrlDep?:   number

        }

        type DeliveryCenterOrgLink对象 = {

            deliveryCenterCode?:   string

            orgOrgId?:   number

            creatorCode?:   string

            lastModifiedAt?:   string

            orgOrgName?:   string

            entId?:   number

            orgOrgCode?:   string

            creatorName?:   string

            deliveryCenterId?:   number

            version?:   number

            deliveryCenterName?:   string

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            deleted?:   number

            id?:   number

            modifierName?:   string

            modifierOrgCode?:   string

        }

        type CreateDailyTransRouteInput = {

            remark?:   string

            dualOrgName?:   string

            startingPrice?:   number

            balOrgCode?:   string

            routeName?:   string

            driverCode?:   string

            routeId?:   number

            conOrgName?:   string

            conOrgId?:   number

            driverShortNum?:   string

            transDate?:   string

            balOrgId?:   number

            conOrgClassId?:   number

            valuationType?:   number

            routeCode?:   string

            addSalDailyRouteCustInputList?:    Array < AddSalDailyRouteCustInput >

            conOrgClassName?:   string

            dualOrgCode?:   string

            balOrgName?:   string

            excessPrice?:   number

            driverId?:   number

            transExpense?:   number

            dualOrgId?:   number

            driverName?:   string

            conOrgCode?:   string

            conOrgClassCode?:   string

        }

        type BatchUpdateEnableSalCusReturnReserveInput = {

            conOrgClassName?:   string

            entId?:   number

            goodsId?:   number

            goodsClassId?:   number

            idList?:    Array < number >

            goodsClassName?:   string

            goodsSpec?:   string

            goodsClassCode?:   string

            conOrgName?:   string

            conOrgId?:   number

            isEnabled?:   number

            conOrgCode?:   string

            goodsCode?:   string

            conOrgClassId?:   number

            goodsName?:   string

            reserveQty?:   number

            conOrgClassCode?:   string

        }

        type CreateDriverInput = {

            code?:   string

            loadingWeight?:   number

            dualOrgCode?:   string

            mobile?:   string

            payAcountNo?:   string

            registeBank?:   string

            remark?:   string

            dualOrgName?:   string

            plateNum?:   string

            driverIdNumber?:   string

            isEnabled?:   number

            dualOrgId?:   number

            name?:   string

            loadingVolume?:   number

            shortNum?:   string

        }

        type SalOrderPickingCfgHead对象_1 = {

            creatorCode?:   string

            lastModifiedAt?:   string

            entId?:   number

            dualOrgCode?:   string

            creatorName?:   string

            remark?:   string

            dualOrgName?:   string

            version?:   number

            isEnable?:   number

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            isDefault?:   number

            deleted?:   number

            dualOrgId?:   number

            name?:   string

            details?:    Array < SalOrderPickingCfgDetail对象 >

            id?:   number

            modifierName?:   string

            modifierOrgCode?:   string

        }

        type CreateCusRouteInput = {

            routeCode?:   string

            cusOrgCode?:   string

            dualOrgCode?:   string

            cusOrgId?:   number

            cusClassCode?:   string

            index?:   number

            dualOrgName?:   string

            cusClassName?:   string

            routeId?:   number

            cusOrgName?:   string

            isEnabled?:   number

            loadingSeq?:   number

            dualOrgId?:   number

            name?:   string

            cusClassId?:   number

            tempZoneTypeCode?:   string

        }

        type MoveInputDTO = {

            goodsCodeList?:    Array < string >

            sourceInput?:  SalGoodsGroupMoveInputDTO

            destinationInput?:  SalGoodsGroupMoveInputDTO

        }

        type ApiResultPageFindSalCusReturnReserveListOutput = {

            data?:  PageFindSalCusReturnReserveListOutput

            resultCode:   number

            errorMsg?:   string

        }

        type UpdateOrderStatusInput = {

            orderHeadInputs?:    Array < UpdateOrderHeadInput >

        }

        type SalOrderControl对象 = {

            isTempZone?:   number

            beginConCycleTime?:   string

            isDuty?:   number

            isAutoGenOrder?:   number

            isDepInputPre?:   number

            endOrderTime?:   string

            isDistCyc?:   number

            isExeBatchPolicy?:   number

            modifierCode?:   string

            createdAt?:   string

            isAccnt?:   number

            beforeDays?:   number

            beginOrderTime?:   string

            id?:   number

            isDepInput?:   number

            modifierOrgCode?:   string

            isEnabledFin?:   number

            allotType?:   number

            creatorCode?:   string

            entId?:   number

            isCtrlOnePreOrder?:   number

            isCtrlReturn?:   number

            version?:   number

            beginPreOrderTime?:   string

            endPreOrderTime?:   string

            isCtrlOneCycleOrder?:   number

            cusOrgName?:   string

            isEnabled?:   number

            endReturnTime?:   string

            isRouteSplit?:   number

            lastModifiedAt?:   string

            cusOrgCode?:   string

            cusOrgId?:   number

            isCtrlTodayPayment?:   number

            creatorName?:   string

            cusClassCode?:   string

            endConCycleTime?:   string

            allowNeginvOrder?:   number

            remark?:   string

            creatorOrgCode?:   string

            allowPosGapM?:   number

            isVrfyTrans?:   number

            safetyStockMultiple?:   number

            cusClassId?:   number

            isMustSell?:   number

            allowPosGapD?:   number

            isCtrlTodayPaymentPre?:   number

            isSafetyStock?:   number

            cusClassName?:   string

            deleted?:   number

            waveNum?:   number

            beginReturnTime?:   string

            orderGoodsRange?:   number

            modifierName?:   string

            isOneOrder?:   number

            isCtrlDep?:   number

        }

        type FrUnitOutputDTO = {

            unitName?:   string

            unitCode?:   string

            unitId?:   number

        }

        type UpdateRouteInput = {

            code?:   string

            tempZoneType?:   number

            dualOrgCode?:   string

            index?:   number

            dualOrgName?:   string

            version?:   number

            receivePlace?:   string

            isEnabled?:   number

            dualOrgId?:   number

            name?:   string

            id?:   number

            tempZoneTypeName?:   string

            tempZoneTypeCode?:   string

            sendPlace?:   string

        }

        type SalOrgBasketUpdateInputDTO = {

            updateInput?:    Array < UpdateInput >

            ids?:    Array < number >

            status?:   number

        }

        type UpdateCustomerOrderHeadInput = {

            isTempZone?:   number

            orderType?:   number

            bussManCode?:   string

            totalRequestQty?:   number

            cusOrgCode?:   string

            cusOrgId?:   number

            cusClassCode?:   string

            orderStatus?:   number

            remark?:   string

            dualOrgName?:   string

            templateCode?:   string

            totalPieceQty?:   number

            id:   number

            cusClassId?:   number

            externalBillNo?:   string

            seqno:   string

            dualOrgCode?:   string

            preArrivalDate:   string

            version?:   number

            cusClassName?:   string

            waveNum?:   number

            inputMode?:   number

            relatedSeqno?:   string

            cusOrgName?:   string

            externalSysType?:   number

            joinType?:   number

            dualOrgId?:   number

            balCusOrgId?:   number

            isOut?:   number

        }

        type ApiResultSalGoodsGroupHead对象 = {

            data?:  SalGoodsGroupHead对象_1

            resultCode:   number

            errorMsg?:   string

        }

        type PageSalOrgBasket对象 = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < SalOrgBasket对象 >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type ApiResult = {

            data?:   any

            resultCode:   number

            errorMsg?:   string

        }

        type BasWrapperWithGoods对象 = {

            mainCardinal?:   number

            lastModifiedAt?:   string

            wrapGoodsClassCode?:   string

            wrapGoodsClassId?:   number

            creatorName?:   string

            dualOrgName?:   string

            mainGoodsName?:   string

            wrapGoodsName?:   string

            wrapUnitName?:   string

            mainGoodsId?:   number

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            mainGoodsClassId?:   number

            mainGoodsCode?:   string

            wrapGoodsClassName?:   string

            mainGoodsClassName?:   string

            id?:   number

            modifierOrgCode?:   string

            wrapCardinal?:   number

            wrapUnitId?:   number

            mainUnitId?:   number

            creatorCode?:   string

            wrapGoodsId?:   number

            entId?:   number

            dualOrgCode?:   string

            isRound?:   number

            version?:   number

            wrapGoodsCode?:   string

            mainUnitName?:   string

            mainGoodsClassCode?:   string

            deleted?:   number

            isEnabled?:   number

            dualOrgId?:   number

            modifierName?:   string

        }

        type BasReturnRules对象 = {

            creatorCode?:   string

            lastModifiedAt?:   string

            entId?:   number

            creatorName?:   string

            remark?:   string

            version?:   number

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            deleted?:   number

            ruleDesc?:   string

            name?:   string

            showOrder?:   number

            id?:   number

            modifierName?:   string

            modifierOrgCode?:   string

        }

        type PageSalOrderPickingCfgHead对象 = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < SalOrderPickingCfgHead对象_1 >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type PageableDTOSalOrderControlSerachInputDTO = {

            pageable?:  Pageable

            dto?:  SalOrderControlSerachInputDTO

        }

        type GoodsTypeInput = {

            code?:   string

            name?:   string

            id?:   number

        }

        type CreateCustomerOrderHeadInput = {

            isTempZone?:   number

            orderType:   number

            bussManCode?:   string

            cusOrgCode?:   string

            dualOrgCode?:   string

            cusOrgId:   number

            cusClassCode?:   string

            orderStatus?:   number

            remark?:   string

            dualOrgName?:   string

            preArrivalDate:   string

            cusClassName?:   string

            inputMode:   number

            cusOrgName?:   string

            joinType?:   number

            dualOrgId?:   number

            balCusOrgId?:   number

            isOut?:   number

            cusClassId?:   number

        }

        type CreateTemplateInput = {

            operateList?:    Array < CreateDistributeTemplateInput >

            entId?:   number

            isDelete?:   number

            status?:   number

        }

        type ApiResultCreateDistributeTemplateInput = {

            data?:  CreateDistributeTemplateInput

            resultCode:   number

            errorMsg?:   string

        }

        type PageableDTOFindDailyTransRouteListInput = {

            pageable?:  Pageable

            dto?:  FindDailyTransRouteListInput

        }

        type QuerySalOrderHeadInput = {

            isTempZone?:   number

            transitLineNo?:   string

            createdAtAfter?:   string

            preArrivalDateAfter:   string

            cusOrgCode?:    Array < string >

            seqno?:   string

            dualOrgCode?:    Array < string >

            creatorName?:   string

            createdAtBefore?:   string

            orderStatus?:   number

            orderIdentification?:   number

            inputModels:    Array < number >

            queryType:   number

            goodsCodeList?:    Array < string >

            goodsClassCode?:   string

            goodsTypeCodes?:    Array < string >

            updateStatus?:   number

            preArrivalDateBefore:   string

            isOut?:   number

            orderTypeList?:    Array < number >

            waveNu?:   string

        }

        type PageableDTOFindSalConsignPrintListInput = {

            pageable?:  Pageable

            dto?:  FindSalConsignPrintListInput

        }

        type ApiResultSalDailyTransRoute对象 = {

            data?:  SalDailyTransRoute对象

            resultCode:   number

            errorMsg?:   string

        }

        type SalDailyTransExpenseOutputDTO = {

            lastModifiedAt?:   string

            cusOrgCode?:   string

            seqNo?:   string

            cusOrgId?:   number

            creatorName?:   string

            remark?:   string

            balOrgCode?:   string

            routeName?:   string

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            driverCode?:   string

            billFlag?:   number

            docStatus?:   number

            routeId?:   number

            conOrgName?:   string

            costName?:   string

            conOrgId?:   number

            transDate?:   string

            balOrgId?:   number

            cusOrgNames?:   string

            id?:   number

            modifierOrgCode?:   string

            auditorName?:   string

            routeCode?:   string

            creatorCode?:   string

            entId?:   number

            pageable?:  Pageable

            appStatus?:   number

            balOrgName?:   string

            version?:   number

            costCode?:   string

            deleted?:   number

            driverId?:   number

            auditTime?:   string

            transRouteId?:   number

            transExpense?:   number

            cusOrgName?:   string

            auditorCode?:   string

            driverName?:   string

            searchWord?:   string

            conOrgCode?:   string

            modifierName?:   string

            totalExpense?:   number

        }

        type SalGoodsGroupHead对象_1 = {

            creatorCode?:   string

            lastModifiedAt?:   string

            goodsGroupName?:   string

            entId?:   number

            dualOrgCode?:   string

            creatorName?:   string

            goodsTypeNum?:   number

            remark?:   string

            dualOrgName?:   string

            version?:   number

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            deleted?:   number

            dualOrgId?:   number

            details?:    Array < SalGoodsGroupDetail对象 >

            goodsGroupCode?:   string

            id?:   number

            modifierName?:   string

            modifierOrgCode?:   string

        }

        type SalOrderControlUpdateInputDTO = {

            isTempZone?:   number

            isDuty?:   number

            isAutoGenOrder?:   number

            isDepInputPre?:   number

            endOrderTime?:   string

            isDistCyc?:   number

            isExeBatchPolicy?:   number

            modifierCode?:   string

            createdAt?:   string

            isAccnt?:   number

            beforeDays?:   number

            beginOrderTime?:   string

            id?:   number

            isDepInput?:   number

            modifierOrgCode?:   string

            isEnabledFin?:   number

            allotType?:   number

            creatorCode?:   string

            isCtrlOnePreOrder?:   number

            isCtrlReturn?:   number

            version?:   number

            isCtrlOneCycleOrder?:   number

            cusOrgName?:   string

            isEnabled?:   number

            endReturnTime?:   string

            isRouteSplit?:   number

            lastModifiedAt?:   string

            cusOrgCode?:   string

            cusOrgId?:   number

            isCtrlTodayPayment?:   number

            creatorName?:   string

            cusClassCode?:   string

            allowNeginvOrder?:   number

            remark?:   string

            creatorOrgCode?:   string

            allowPosGapM?:   number

            isVrfyTrans?:   number

            safetyStockMultiple?:   number

            cusClassId?:   number

            isMustSell?:   number

            allowPosGapD?:   number

            isCtrlTodayPaymentPre?:   number

            isSafetyStock?:   number

            cusClassName?:   string

            deleted?:   number

            beginReturnTime?:   string

            orderGoodsRange?:   number

            modifierName?:   string

            isOneOrder?:   number

            isCtrlDep?:   number

        }

        type CreateCustomerOrderInput = {

            head?:  CreateCustomerOrderHeadInput

            details?:    Array < SalOrderDetail对象 >

        }

        type AddSalDailyRouteCustInput = {

            cusClassName?:   string

            cusOrgCode?:   string

            cusOrgId?:   number

            cusOrgName?:   string

            cusClassCode?:   string

            cusClassId?:   number

        }

        type ApiResultListBasLookupValueCustomDTO = {

            data?:    Array < BasLookupValueCustomDTO >

            resultCode:   number

            errorMsg?:   string

        }

        type ApiResultPageSalDistributeControl对象 = {

            data?:  PageSalDistributeControl对象

            resultCode:   number

            errorMsg?:   string

        }

        type PageBasDriver对象 = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < BasDriver对象 >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type ApiResultListBasCusRoute对象 = {

            data?:    Array < BasCusRoute对象 >

            resultCode:   number

            errorMsg?:   string

        }

        type ApiResultPageSalConsignPrintMapping对象 = {

            data?:  PageSalConsignPrintMapping对象

            resultCode:   number

            errorMsg?:   string

        }

        type PageableDTOFindBasRouteListInput = {

            pageable?:  Pageable

            dto?:  FindBasRouteListInput

        }

        type SalOrderControlCreateInputDTO = {

            isTempZone?:   number

            beginConCycleTime?:   string

            isDuty?:   number

            isAutoGenOrder?:   number

            isDepInputPre?:   number

            endOrderTime?:   string

            isDistCyc?:   number

            isExeBatchPolicy?:   number

            modifierCode?:   string

            createdAt?:   string

            isAccnt?:   number

            beforeDays?:   number

            beginOrderTime?:   string

            id?:   number

            isDepInput?:   number

            modifierOrgCode?:   string

            isEnabledFin?:   number

            allotType?:   number

            creatorCode?:   string

            entId?:   number

            isCtrlOnePreOrder?:   number

            isCtrlReturn?:   number

            version?:   number

            beginPreOrderTime?:   string

            endPreOrderTime?:   string

            isCtrlOneCycleOrder?:   number

            cusOrgName?:   string

            isEnabled?:   number

            endReturnTime?:   string

            isRouteSplit?:   number

            lastModifiedAt?:   string

            cusOrgCode?:   string

            cusOrgId?:   number

            isCtrlTodayPayment?:   number

            creatorName?:   string

            cusClassCode?:   string

            endConCycleTime?:   string

            allowNeginvOrder?:   number

            remark?:   string

            creatorOrgCode?:   string

            allowPosGapM?:   number

            isVrfyTrans?:   number

            safetyStockMultiple?:   number

            cusClassId?:   number

            isMustSell?:   number

            allowPosGapD?:   number

            isCtrlTodayPaymentPre?:   number

            isSafetyStock?:   number

            cusClassName?:   string

            deleted?:   number

            waveNum?:   number

            beginReturnTime?:   string

            orderGoodsRange?:   number

            modifierName?:   string

            isOneOrder?:   number

            isCtrlDep?:   number

        }

        type CreateSalCusReturnReserveInput = {

            conOrgClassName?:   string

            cusOrgCode?:   string

            entId?:   number

            goodsId?:   number

            cusOrgId?:   number

            cusClassCode?:   string

            goodsClassId?:   number

            remark?:   string

            goodsClassName?:   string

            goodsSpec?:   string

            cusClassName?:   string

            goodsClassCode?:   string

            conOrgName?:   string

            conOrgId?:   number

            cusOrgName?:   string

            isEnabled?:   number

            conOrgCode?:   string

            goodsCode?:   string

            conOrgClassId?:   number

            cusClassId?:   number

            goodsName?:   string

            reserveQty?:   number

            conOrgClassCode?:   string

        }

        type IsEnableCusRouteInput = {

            routeId?:   number

            index?:   number

            id?:   number

            version?:   number

            routeName?:   string

        }

        type UpdateDailyTransRouteInput = {

            remark?:   string

            dualOrgName?:   string

            startingPrice?:   number

            balOrgCode?:   string

            routeName?:   string

            driverCode?:   string

            routeId?:   number

            conOrgName?:   string

            conOrgId?:   number

            driverShortNum?:   string

            transDate?:   string

            balOrgId?:   number

            id?:   number

            conOrgClassId?:   number

            valuationType?:   number

            routeCode?:   string

            addSalDailyRouteCustInputList?:    Array < AddSalDailyRouteCustInput >

            conOrgClassName?:   string

            dualOrgCode?:   string

            balOrgName?:   string

            version?:   number

            excessPrice?:   number

            driverId?:   number

            transExpense?:   number

            dualOrgId?:   number

            driverName?:   string

            conOrgCode?:   string

            conOrgClassCode?:   string

        }

        type PageSalGoodsGroupHead对象 = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < SalGoodsGroupHead对象 >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type SalOrderPickingCfgSearchInputDTO = {

            isDefault?:   number

            orgOrgCodes?:    Array < string >

            name?:   string

            dualOrgCodes?:    Array < string >

            isEnable?:   number

        }

        type SalOrderPickingCfgHeadInputDTO = {

            dualOrgCode?:   string

            dualOrgId?:   number

            name?:   string

            dualOrgName?:   string

            id?:   number

            version?:   number

            isEnable?:   number

        }

        type SalOrderPickingCfgUpdateStatusInputDTO = {

            updateInput?:    Array < UpdateInput >

            status?:   number

        }

        type ApiResultPageFindBasRouteListByAuthInput = {

            data?:  PageFindBasRouteListByAuthInput

            resultCode:   number

            errorMsg?:   string

        }

        type CreateDeliveryCenterInput = {

            subOrgs?:    Array < DeliveryCenterOrgLink对象 >

            deliveryCenter?:  DeliveryCenter对象

        }

        type UpdateOrderHeadInput = {

            isTempZone?:   number

            orderType?:   number

            bussManCode?:   string

            lastModifiedAt?:   string

            totalRequestQty?:   number

            balCusOrgName?:   string

            cusOrgCode?:   string

            cusOrgId?:   number

            creatorName?:   string

            cusClassCode?:   string

            orderStatus?:   number

            remark?:   string

            dualOrgName?:   string

            templateCode?:   string

            totalPieceQty?:   number

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            commitTime?:   string

            auditNote?:   string

            id:   number

            cusClassId?:   number

            modifierOrgCode?:   string

            externalBillNo?:   string

            taxAmt?:   number

            goodsAmt?:   number

            creatorCode?:   string

            seqno:   string

            entId?:   number

            dualOrgCode?:   string

            balCusOrgCode?:   string

            bussManName?:   string

            preArrivalDate?:   string

            version?:   number

            cusClassName?:   string

            deleted?:   number

            totalAmt?:   number

            waveNum?:   number

            inputMode?:   number

            relatedSeqno?:   string

            cusOrgName?:   string

            externalSysType?:   number

            joinType?:   number

            dualOrgId?:   number

            balCusOrgId?:   number

            isOut?:   number

            modifierName?:   string

        }

        type CreateRouteInput = {

            code?:   string

            dualOrgCode?:   string

            isEnabled?:   number

            dualOrgId?:   number

            name?:   string

            index?:   number

            dualOrgName?:   string

            tempZoneTypeName?:   string

            tempZoneTypeCode?:   string

            receivePlace?:   string

            tempZoneTypeId?:   number

            sendPlace?:   string

        }

        type UpdateBasCusRoute = {

            isEnabled?:   number

            updateList?:    Array < IsEnableCusRouteInput >

        }

        type ApiResultSalOrderPickingCfgOutputForAlterDTO = {

            data?:  SalOrderPickingCfgOutputForAlterDTO

            resultCode:   number

            errorMsg?:   string

        }

        type ApiResultPageSalGoodsGroupHead对象 = {

            data?:  PageSalGoodsGroupHead对象

            resultCode:   number

            errorMsg?:   string

        }

        type BasReturnGoods对象 = {

            orgClassCode?:   string

            salUnitCode?:   string

            salUnitId?:   number

            lastModifiedAt?:   string

            goodsId?:   number

            creatorName?:   string

            goodsClassId?:   number

            remark?:   string

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            conOrgName?:   string

            conOrgId?:   number

            returnRuleName?:   string

            orgClassName?:   string

            id?:   number

            modifierOrgCode?:   string

            goodsName?:   string

            creatorCode?:   string

            entId?:   number

            orgClassId?:   number

            goodsClassName?:   string

            version?:   number

            goodsSpec?:   string

            returnRuleId?:   number

            deleted?:   number

            goodsClassCode?:   string

            isEnabled?:   number

            salUnitName?:   string

            conOrgCode?:   string

            goodsCode?:   string

            modifierName?:   string

            returnType?:   number

        }

        type DeliveryCenter对象 = {

            address?:   string

            code?:   string

            creatorCode?:   string

            lastModifiedAt?:   string

            entId?:   number

            creatorName?:   string

            version?:   number

            creatorOrgCode?:   string

            modifierCode?:   string

            wmsCode?:   string

            codeUpper?:   string

            createdAt?:   string

            deleted?:   number

            isEnabled?:   number

            joinType?:   number

            name?:   string

            showOrder?:   number

            id?:   number

            modifierName?:   string

            modifierOrgCode?:   string

            checkWarnAmt?:   number

        }

        type PageSalDistributeControl对象 = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < SalDistributeControl对象 >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type ApiResultListSalDailyTransExpenseOutputDTO = {

            data?:    Array < SalDailyTransExpenseOutputDTO >

            resultCode:   number

            errorMsg?:   string

        }

        type PageableDTOSalDailyTransExpenseSearchInputDTO = {

            pageable?:  Pageable

            dto?:  SalDailyTransExpenseSearchInputDTO

        }

        type OrderStockUpInput = {

            orderHeadInputs?:    Array < UpdateOrderHeadInput >

            pickingHeadId?:   number

        }

        type SalDailyOtherExpensesUpdateAppStatusInputDTO = {

            updateStatus?:   number

            updateInput?:    Array < UpdateInput >

        }

        type UpdateSalCusReturnReserveInput = {

            conOrgClassName?:   string

            cusOrgCode?:   string

            entId?:   number

            goodsId?:   number

            cusOrgId?:   number

            cusClassCode?:   string

            goodsClassId?:   number

            remark?:   string

            goodsClassName?:   string

            goodsSpec?:   string

            cusClassName?:   string

            goodsClassCode?:   string

            conOrgName?:   string

            conOrgId?:   number

            cusOrgName?:   string

            isEnabled?:   number

            conOrgCode?:   string

            goodsCode?:   string

            id?:   number

            conOrgClassId?:   number

            cusClassId?:   number

            goodsName?:   string

            reserveQty?:   number

            conOrgClassCode?:   string

        }

        type SalOrderCanOperationOutput = {

            orderGoodsRange?:   number

            isTakeCycle?:   number

        }

        type UpdateSalConsignPrintMappingInput = {

            mergeConOrgName:   string

            depotName:   string

            mergeGoodsClassCode?:   string

            goodsTypeCode?:   string

            goodsId?:   number

            goodsTypeName?:   string

            mergeConOrgCode:   string

            depotId:   number

            goodsClassId?:   number

            goodsClassName?:   string

            mergeGoodsClassName?:   string

            mergeGoodsClassId?:   number

            mergeTempZoneTypeCode?:   string

            goodsClassCode?:   string

            conOrgName:   string

            goodsTypeId?:   number

            conOrgId:   number

            depotCode:   string

            conOrgCode:   string

            goodsCode?:   string

            id:   number

            goodsName?:   string

            tempZoneTypeCode?:   string

            mergeConOrgId:   number

        }

        type SalGoodsGroupDetail对象 = {

            creatorCode?:   string

            lastModifiedAt?:   string

            entId?:   number

            goodsTypeCode?:   string

            goodsId?:   number

            goodsTypeName?:   string

            creatorName?:   string

            goodsClassId?:   number

            goodsClassName?:   string

            version?:   number

            headId?:   number

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            deleted?:   number

            goodsClassCode?:   string

            goodsTypeId?:   number

            goodsCode?:   string

            id?:   number

            modifierName?:   string

            modifierOrgCode?:   string

            goodsName?:   string

        }

        type ApiResultPageCreateDistributeTemplateInput = {

            data?:  PageCreateDistributeTemplateInput

            resultCode:   number

            errorMsg?:   string

        }

        type ApiResultListFrUnitOutputDTO = {

            data?:    Array < FrUnitOutputDTO >

            resultCode:   number

            errorMsg?:   string

        }

        type FindBasRouteListInput = {

            code?:   string

            codeOrName?:   string

            entId?:   number

            dualOrgCodeList?:    Array < string >

            isEnabled?:   number

            isExport?:   boolean

            name?:   string

            tempZoneTypeCode?:   string

            receivePlace?:   string

            sendPlace?:   string

        }

        type QueryInsteadOrderInput = {

            isTempZone?:   number

            transitLineNo?:   string

            createdAtAfter?:   string

            preArrivalDateAfter:   string

            cusOrgCode?:    Array < string >

            seqno?:   string

            dualOrgCode?:    Array < string >

            creatorName?:   string

            createdAtBefore?:   string

            orderStatus?:   number

            orderIdentification?:   number

            inputModels:    Array < number >

            queryType:   number

            goodsCodeList?:    Array < string >

            goodsClassCode?:   string

            goodsTypeCodes?:    Array < string >

            updateStatus?:   number

            preArrivalDateBefore:   string

            isOut?:   number

            orderTypeList?:    Array < number >

            waveNu?:   string

        }

        type UpdateDeliveryCenterInput = {

            subOrgs?:    Array < DeliveryCenterOrgLink对象 >

            deleteIds?:    Array < number >

            deliveryCenter?:  DeliveryCenter对象

        }

        type SalOrgBasketSearchInputDTO = {

            goodsClassCode?:    Array < string >

            lastModifiedAt?:    Array < string >

            lastModifiedAtBefore?:   string

            dualOrgCode?:    Array < string >

            lastModifiedAtAfter?:   string

            conOrgCode?:    Array < string >

            goodsCode?:    Array < string >

            status?:   number

        }

        type SalOrderPickingCfgDetail对象 = {

            creatorCode?:   string

            lastModifiedAt?:   string

            entId?:   number

            creatorName?:   string

            elementCode?:   string

            version?:   number

            elementName?:   string

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            deleted?:   number

            rankNum?:   number

            id?:   number

            modifierName?:   string

            cfgId?:   number

            modifierOrgCode?:   string

        }

        type DeleteCustomerOrderHeadInput = {

            seqno:   string

            id:   number

        }

        type FindGoodsInMoveInputDTO = {

            goodsClassList?:    Array < string >

            pageable?:  Pageable

            id?:   number

            goodsCodeOrName?:   string

            goodsType?:   string

        }

        type BasWrapperWithGoodsSearchInputDTO = {

            mainUnitIds?:    Array < number >

            mainGoodsIds?:    Array < number >

            wrapUnitIds?:    Array < number >

            goodsClassCodeList?:    Array < string >

            mainGoodsCode?:    Array < string >

            dualOrgCode?:    Array < string >

            dualOrgCodeList?:    Array < string >

            isEnabled?:   number

            wrapGoodsIds?:    Array < number >

            wrapGoodsCode?:    Array < string >

        }

        type SalGoodsGroupSearchInputDTO = {

            entId?:   number

            orgOrgCodes?:    Array < string >

            name?:   string

            dualOrgCodes?:    Array < string >

            pageable?:  Pageable

            isEnable?:   number

        }

        type SalOrderPickingCfgDetailInputDTO = {

            rankNum?:   number

            elementCode?:   number

            id?:   number

            elementName?:   string

        }

        type FindBasDriverListInput = {

            code?:   string

            codeOrName?:   string

            entId?:   number

            dualOrgCodeList?:    Array < string >

            isEnabled?:   number

            isExport?:   boolean

            mobile?:   string

            name?:   string

            tempZoneTypeCode?:   string

        }

        type FindBasRouteListByAuthInput = {

            code?:   string

            lastModifiedAt?:   string

            creatorName?:   string

            dualOrgName?:   string

            receivePlace?:   string

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            cusList?:    Array < FindCusRouteListByAuthOutput >

            id?:   number

            modifierOrgCode?:   string

            tempZoneTypeCode?:   string

            tempZoneTypeId?:   number

            sendPlace?:   string

            creatorCode?:   string

            entId?:   number

            dualOrgCode?:   string

            pageable?:  Pageable

            version?:   number

            deleted?:   number

            waveNum?:   number

            isEnabled?:   number

            dualOrgId?:   number

            name?:   string

            modifierName?:   string

            tempZoneTypeName?:   string

        }

        type SalDailyTransExpenseSearchInputDTO = {

            routeCode?:    Array < string >

            cusOrgCode?:    Array < string >

            inDocStatusList?:    Array < number >

            seqNo?:   string

            entId?:   number

            transDateBefore?:   string

            cusOrgId?:    Array < number >

            underDeptCode?:   string

            appStatus?:   number

            queryType?:   number

            driverCode?:    Array < string >

            costCode?:   string

            docStatus?:   number

            authorityOrgCodeList?:    Array < string >

            conOrgId?:    Array < number >

            updateStatus?:   number

            transDate?:    Array < string >

            authorityCusCodeList?:    Array < string >

            conOrgCode?:    Array < string >

            transDateAfter?:   string

        }

        type UpdateSalDistributeControlInput = {

            isDelete?:   number

            status?:   number

            updateList?:    Array < SalDistributeControl对象 >

        }

        type AdjustMentReturnGoodsInput = {

            isEnabled?:   number

            delIdInputList?:    Array < DelOrderIdInput >

            returnType?:   number

            returnRuleId?:   number

        }

        type DeleteRouteInput = {

            name?:   string

            index?:   number

            id?:   number

            version?:   number

        }

        type SalOrderControlSerachInputDTO = {

            isTempZone?:   number

            isAutoGenOrder?:   number

            isDepInputPre?:   number

            queryPath?:   number

            cusOrgId?:   number

            isCtrlTodayPayment?:   number

            catClassCodeList?:    Array < string >

            allowNeginvOrder?:   number

            isDistCyc?:   number

            isExeBatchPolicy?:   number

            isAccnt?:   number

            conOrgId?:   number

            isVrfyTrans?:   number

            isDepInput?:   number

            isMustSell?:   number

            isEnabledFin?:   number

            conOrgCodes?:    Array < string >

            allotType?:   number

            entId?:   number

            cusOrgCodes?:    Array < string >

            isCtrlReturn?:   number

            isCtrlTodayPaymentPre?:   number

            updateName?:   string

            isSafetyStock?:   number

            waveNum?:    Array < number >

            isEnabled?:   number

            orderGoodsRange?:   number

            dualOrgCodes?:    Array < string >

            isRouteSplit?:   number

            isOneOrder?:   number

            isCtrlDep?:   number

        }

        type FindBasCusRouteListInput_1 = {

            cusOrgCodeList?:    Array < string >

            routeCode?:   string

            routeCodeList?:    Array < string >

            cusOrgCode?:   string

            entId?:   number

            cusOrgName?:   string

            dualOrgCodeList?:    Array < string >

            isEnabled?:   number

            modifierName?:   string

        }

        type SalOrderPickingCfgHead对象 = {

            creatorCode?:   string

            lastModifiedAt?:   string

            entId?:   number

            dualOrgCode?:   string

            creatorName?:   string

            remark?:   string

            dualOrgName?:   string

            version?:   number

            isEnable?:   number

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            isDefault?:   number

            deleted?:   number

            dualOrgId?:   number

            name?:   string

            id?:   number

            modifierName?:   string

            modifierOrgCode?:   string

        }

        type QueryAppendOrderInput = {

            isTempZone?:   number

            transitLineNo?:   string

            createdAtAfter?:   string

            preArrivalDateAfter:   string

            cusOrgCode?:    Array < string >

            seqno?:   string

            dualOrgCode?:    Array < string >

            creatorName?:   string

            createdAtBefore?:   string

            orderStatus?:   number

            orderIdentification?:   number

            inputModels:    Array < number >

            queryType:   number

            goodsCodeList?:    Array < string >

            goodsClassCode?:   string

            goodsTypeCodes?:    Array < string >

            updateStatus?:   number

            preArrivalDateBefore:   string

            isOut?:   number

            orderTypeList?:    Array < number >

            waveNu?:   string

        }

        type PageableDTOReturnRulesInput = {

            pageable?:  Pageable

            dto?:  ReturnRulesInput

        }

        type BasLookupValueCustomDTO = {

            code?:   string

            lastModifiedAt?:   string

            creatorName?:   string

            refValue?:   string

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            parentCode?:   string

            id?:   number

            modifierOrgCode?:   string

            sourceFlag?:   number

            value?:   string

            creatorCode?:   string

            entId?:   number

            pageable?:  Pageable

            isShowFlag?:   number

            version?:   number

            parentId?:   number

            deleted?:   number

            valueDesc?:   string

            isEnabled?:   number

            name?:   string

            searchWord?:   string

            showOrder?:   number

            modifierName?:   string

            refId?:   number

            refCode?:   string

        }

        type FindDailyTransRouteListInput = {

            salDepotCodeList?:    Array < string >

            driverCodeList?:    Array < string >

            goodsClassCodeList?:    Array < string >

            entId?:   number

            dualOrgCode?:   string

            dualOrgCodeList?:    Array < string >

            pageable?:  Pageable

            transDateArrays?:    Array < string >

            cusClassCodeList?:    Array < string >

            conOrgClassCodeList?:    Array < string >

            cusOrgCodeList?:    Array < string >

            salDepotClassCodeList?:    Array < string >

            routeCodeList?:    Array < string >

            waveNum?:   number

            createDateArrays?:    Array < string >

            conOrgCodeList?:    Array < string >

        }

        type PageableDTOFindBasCusRouteListInput_1 = {

            pageable?:  Pageable

            dto?:  FindBasCusRouteListInput_1

        }

        type PageableDTOFindBasDriverListInput = {

            pageable?:  Pageable

            dto?:  FindBasDriverListInput

        }

        type SalOrderHeadOutput = {

            isTempZone?:   number

            orderType?:   number

            bussManCode?:   string

            lastModifiedAt?:   string

            totalRequestQty?:   number

            balCusOrgName?:   string

            cusOrgCode?:   string

            cusOrgId?:   number

            creatorName?:   string

            cusClassCode?:   string

            orderStatus?:   number

            remark?:   string

            dualOrgName?:   string

            templateCode?:   string

            totalPieceQty?:   number

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            commitTime?:   string

            auditNote?:   string

            id?:   number

            cusClassId?:   number

            modifierOrgCode?:   string

            externalBillNo?:   string

            taxAmt?:   number

            goodsAmt?:   number

            creatorCode?:   string

            seqno?:   string

            entId?:   number

            dualOrgCode?:   string

            balCusOrgCode?:   string

            bussManName?:   string

            preArrivalDate?:   string

            version?:   number

            cusClassName?:   string

            deleted?:   number

            totalAmt?:   number

            waveNum?:   number

            inputMode?:   number

            relatedSeqno?:   string

            cusOrgName?:   string

            externalSysType?:   number

            joinType?:   number

            dualOrgId?:   number

            balCusOrgId?:   number

            isOut?:   number

            modifierName?:   string

        }

        type PageSalOrderHeadOutput = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < SalOrderHeadOutput >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type SalGoodsGroupMoveInputDTO = {

            dualOrgCode?:   string

            goodsGroupCode?:   string

            version?:   number

        }

        type PageableDTOFindBasRouteListByAuthInput = {

            pageable?:  Pageable

            dto?:  FindBasRouteListByAuthInput_1

        }

        type PageableDTOFindGoodsInMoveInputDTO = {

            pageable?:  Pageable

            dto?:  FindGoodsInMoveInputDTO

        }

        type ApiResultPageSalOrgBasket对象 = {

            data?:  PageSalOrgBasket对象

            resultCode:   number

            errorMsg?:   string

        }

        type SalDistributeControl对象 = {

            lastModifiedAt?:   string

            cusOrgCode?:   string

            endOrderTime?:   string

            cusOrgId?:   number

            creatorName?:   string

            cusClassCode?:   string

            dualOrgName?:   string

            templateCode?:   string

            templateId?:   number

            orderTimeStr?:   string

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            beginOrderTime?:   string

            id?:   number

            cusClassId?:   number

            modifierOrgCode?:   string

            creatorCode?:   string

            entId?:   number

            dualOrgCode?:   string

            version?:   number

            cusClassName?:   string

            deleted?:   number

            isControlEnabled?:   number

            waveNum?:   number

            templateName?:   string

            cusOrgName?:   string

            dualOrgId?:   number

            delayTime?:   number

            modifierName?:   string

        }

        type UpdateOrderStatusOutput = {

            total?:   number

            finished?:   number

            unfinished?:   number

        }

        type AddSalConsignPrintMappingInput_1 = {

            mergeConOrgName:   string

            depotName:   string

            mergeGoodsClassCode?:   string

            goodsTypeCode?:   string

            goodsId?:   number

            goodsTypeName?:   string

            mergeConOrgCode:   string

            depotId:   number

            goodsClassId?:   number

            goodsClassName?:   string

            mergeGoodsClassName?:   string

            mergeGoodsClassId?:   number

            mergeTempZoneTypeCode?:   string

            goodsClassCode?:   string

            conOrgName:   string

            goodsTypeId?:   number

            conOrgId:   number

            depotCode:   string

            conOrgCode:   string

            goodsCode?:   string

            goodsName?:   string

            tempZoneTypeCode?:   string

            mergeConOrgId:   number

        }

        type ApiResultBasDriver对象 = {

            data?:  BasDriver对象

            resultCode:   number

            errorMsg?:   string

        }

        type SalDailyTransExpenseUpdateInputDTO = {

            driverCode?:   string

            driverId?:   number

            dualOrgCode?:   string

            transExpense?:   number

            driverName?:   string

            remark?:   string

            id?:   number

            version?:   number

        }

        type UpdateEnableSalCusReturnReserveInput = {

            isEnabled?:   number

            idList?:    Array < number >

        }

        type PageFindBasRouteListByAuthInput = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < FindBasRouteListByAuthInput >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type UpdateDriverInput = {

            code?:   string

            loadingWeight?:   number

            dualOrgCode?:   string

            mobile?:   string

            payAcountNo?:   string

            registeBank?:   string

            remark?:   string

            dualOrgName?:   string

            plateNum?:   string

            version?:   number

            driverIdNumber?:   string

            isEnabled?:   number

            dualOrgId?:   number

            name?:   string

            id?:   number

            loadingVolume?:   number

            shortNum?:   string

        }

        type FindBasCusRouteListInput = {

            cusOrgCodeList?:    Array < string >

            routeCodeList?:    Array < string >

            entId?:   number

            dualOrgCodeList?:    Array < string >

            isEnabled?:   number

            modifierName?:   string

        }

        type Pageable = {

            pageNumber?:   number

            pageSize?:   number

            sort?:   string

        }

        type SalOrderDetail对象 = {

            requestQty:   number

            goodsClassCode:   string

            totalAmt?:   number

            goodsTypeCode:   string

            goodsTypeId:   number

            goodsId:   number

            goodsTypeName:   string

            goodsClassId:   number

            remark?:   string

            goodsCode:   string

            goodsClassName:   string

            goodsName:   string

        }

        type Sort = {

            unsorted?:   boolean

            sorted?:   boolean

            empty?:   boolean

        }

        type ReturnRulesInput = {

            ruleDesc?:   string

            name?:   string

        }

        type BasCostTypeDetailCustomDTO = {

            code?:   string

            lastModifiedAt?:   string

            creatorName?:   string

            refValue?:   string

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            parentCode?:   string

            id?:   number

            modifierOrgCode?:   string

            sourceFlag?:   number

            value?:   string

            creatorCode?:   string

            entId?:   number

            pageable?:  Pageable

            version?:   number

            parentId?:   number

            deleted?:   number

            valueDesc?:   string

            isEnabled?:   number

            searchWord?:   string

            showOrder?:   number

            modifierName?:   string

            refId?:   number

            refCode?:   string

        }

        type ReturnGoodsOnManagerInput = {

            goodsCodeList?:    Array < string >

            conOrgIdList?:    Array < number >

            entId?:   number

            orgClassCodeList?:    Array < string >

            isEnabled?:   number

            conOrgCodeList?:    Array < string >

            goodsTypeCodeList?:    Array < string >

            goodsTypeIdList?:    Array < number >

            goodsIdList?:    Array < number >

            returnType?:   number

            returnRuleId?:   number

        }

        type SalAuthAdapter = {

            salDepotCodeList?:    Array < string >

            goodsClassCodeList?:    Array < string >

            dualOrgCode?:   string

            dualOrgCodeList?:    Array < string >

            templateList?:    Array < SalDistributeTemplate对象 >

            dualOrgName?:   string

            cusClassCodeList?:    Array < string >

            conOrgClassCodeList?:    Array < string >

            cusOrgCodeList?:    Array < string >

            salDepotClassCodeList?:    Array < string >

            isControlEnabled?:   number

            excepCusOrgCodeList?:    Array < string >

            dualOrgId?:   number

            conOrgCodeList?:    Array < string >

            delayTime?:   number

        }

        type AddSalConsignPrintMappingInput = {

            mergeConOrgName:   string

            depotName:   string

            mergeGoodsClassCode?:   string

            entId?:   number

            mergeConOrgCode:   string

            depotId:   number

            goodsTypeCodeList?:    Array < string >

            mergeGoodsClassName?:   string

            mergeGoodsClassId?:   number

            goodsCodeList?:    Array < string >

            mergeTempZoneTypeCode?:   string

            isEnabled?:   number

            depotCode:   string

            tempZoneTypeCode?:   string

            mergeConOrgId:   number

        }

        type DeleteDriverInput = {

            id?:   number

            version?:   number

        }

        type CreateDistributeTemplateInput = {

            lastModifiedAt?:   string

            endOrderTime?:   string

            isCtrlTodayPayment?:   number

            creatorName?:   string

            remark?:   string

            dualOrgName?:   string

            templateCode?:   string

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            beginOrderTime?:   string

            id?:   number

            isTakeCycle?:   number

            goodsTypes?:    Array < GoodsTypeInput >

            isDepInput?:   number

            modifierOrgCode?:   string

            creatorCode?:   string

            entId?:   number

            goodsTypeCode?:   string

            dualOrgCode?:   string

            pageable?:  Pageable

            isCtrlReturn?:   number

            version?:   number

            deleted?:   number

            templateName?:   string

            isEnabled?:   number

            dualOrgId?:   number

            modifierName?:   string

            isCtrlDep?:   number

        }

        type SalDailyTransRoute对象 = {

            lastModifiedAt?:   string

            creatorName?:   string

            remark?:   string

            dualOrgName?:   string

            startingPrice?:   number

            balOrgCode?:   string

            routeName?:   string

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            driverCode?:   string

            routeId?:   number

            conOrgName?:   string

            conOrgId?:   number

            driverShortNum?:   string

            transDate?:   string

            balOrgId?:   number

            id?:   number

            conOrgClassId?:   number

            modifierOrgCode?:   string

            valuationType?:   number

            routeCode?:   string

            conOrgClassName?:   string

            creatorCode?:   string

            seqno?:   string

            entId?:   number

            dualOrgCode?:   string

            balOrgName?:   string

            version?:   number

            excessPrice?:   number

            deleted?:   number

            driverId?:   number

            transExpense?:   number

            dualOrgId?:   number

            driverName?:   string

            searchWord?:   string

            conOrgCode?:   string

            modifierName?:   string

            conOrgClassCode?:   string

        }

        type DeleteDailyTransRouteInput = {

            id?:   number

            version?:   number

        }

        type SalOrderPickingCfgOutputForAlterDTO = {

            head?:  SalOrderPickingCfgHead对象

            details?:    Array < SalOrderPickingCfgDetail对象 >

        }

        type SalOrderControlUpdateStatusInputDTO = {

            updatePath?:   number

            updateStatus?:   number

            updateInput?:    Array < UpdateInput >

        }

        type PageFindBasCusRouteListOutput = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < FindBasCusRouteListOutput >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type ApiResultBasCostTypeDetailCustomDTO = {

            data?:  BasCostTypeDetailCustomDTO

            resultCode:   number

            errorMsg?:   string

        }

        type ApiResultBasRoute对象 = {

            data?:  BasRoute对象

            resultCode:   number

            errorMsg?:   string

        }

        type SalOrgBasket对象 = {

            lastModifiedAt?:   string

            goodsId?:   number

            goodsTypeName?:   string

            creatorName?:   string

            goodsClassId?:   number

            remark?:   string

            dualOrgName?:   string

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            conOrgName?:   string

            conOrgId?:   number

            id?:   number

            conOrgClassId?:   number

            modifierOrgCode?:   string

            goodsName?:   string

            conOrgClassName?:   string

            creatorCode?:   string

            entId?:   number

            goodsTypeCode?:   string

            dualOrgCode?:   string

            goodsClassName?:   string

            version?:   number

            deleted?:   number

            goodsClassCode?:   string

            goodsTypeId?:   number

            dualOrgId?:   number

            conOrgCode?:   string

            goodsCode?:   string

            modifierName?:   string

            conOrgClassCode?:   string

            status?:   number

        }

        type PageableDTOReturnGoodsOnManagerInput = {

            pageable?:  Pageable

            dto?:  ReturnGoodsOnManagerInput

        }

        type FindCusRouteListByAuthOutput = {

            cusClassName?:   string

            cusOrgCode?:   string

            cusOrgId?:   number

            cusOrgName?:   string

            cusClassCode?:   string

            cusClassId?:   number

        }

        type SalOrderPickingCfgInputDTO = {

            head?:  SalOrderPickingCfgHeadInputDTO

            details?:    Array < SalOrderPickingCfgDetailInputDTO >

        }

        type ApiResultListSalGoodsGroupHead对象 = {

            data?:    Array < SalGoodsGroupHead对象 >

            resultCode:   number

            errorMsg?:   string

        }

        type PageableDTOBasWrapperWithGoodsSearchInputDTO = {

            pageable?:  Pageable

            dto?:  BasWrapperWithGoodsSearchInputDTO

        }

        type BasWrapperWithGoodsUpdateStatusInputDTO = {

            updateStatus?:   number

            updateInput?:    Array < UpdateInput >

        }

        type SalDailyOtherExpense对象 = {

            underEmpId?:   number

            underDeptId?:   number

            lastModifiedAt?:   string

            seqNo?:   string

            creatorName?:   string

            remark?:   string

            dualOrgName?:   string

            underDeptCode?:   string

            balOrgCode?:   string

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            driverCode?:   string

            billFlag?:   number

            docStatus?:   number

            conOrgName?:   string

            costName?:   string

            conOrgId?:   number

            otherExpense?:   number

            transDate?:   string

            balOrgId?:   number

            id?:   number

            modifierOrgCode?:   string

            auditorName?:   string

            underEmpCode?:   string

            creatorCode?:   string

            underDeptName?:   string

            entId?:   number

            dualOrgCode?:   string

            appStatus?:   number

            balOrgName?:   string

            version?:   number

            costCode?:   string

            deleted?:   number

            underEmpName?:   string

            driverId?:   number

            auditTime?:   string

            dualOrgId?:   number

            auditorCode?:   string

            driverName?:   string

            searchWord?:   string

            conOrgCode?:   string

            modifierName?:   string

        }

        type ApiResultPageSalOrderControl对象 = {

            data?:  PageSalOrderControl对象

            resultCode:   number

            errorMsg?:   string

        }

        type ApiResultPageBasReturnRules对象 = {

            data?:  PageBasReturnRules对象

            resultCode:   number

            errorMsg?:   string

        }

        type SalDailyOtherExpenseSearchInputDTO = {

            lastModifiedAt?:   string

            seqNo?:   string

            entId?:   number

            transDateBefore?:   string

            underDeptCode?:    Array < string >

            appStatus?:   number

            version?:   number

            queryType?:   number

            modifierCode?:   string

            driverCode?:    Array < string >

            costCode?:    Array < string >

            docStatus?:   number

            docStatusList?:    Array < number >

            authorityOrgCodeList?:    Array < string >

            updateStatus?:   number

            notInAppStatusList?:    Array < number >

            transDate?:    Array < string >

            conOrgCode?:    Array < string >

            modifierName?:   string

            modifierOrgCode?:   string

            transDateAfter?:   string

        }

        type FindDailyTransRouteListOutput = {

            lastModifiedAt?:   string

            cusNames?:   string

            remark?:   string

            cusCodes?:   string

            dualOrgName?:   string

            startingPrice?:   number

            balOrgCode?:   string

            routeName?:   string

            modifierCode?:   string

            driverCode?:   string

            totalTransExpense?:   number

            docStatus?:    Array < number >

            routeId?:   number

            conOrgName?:   string

            conOrgId?:   number

            driverShortNum?:   string

            transDate?:   string

            balOrgId?:   number

            id?:   number

            conOrgClassId?:   number

            cusClassIds?:   string

            valuationType?:   number

            cusClassCodes?:   string

            routeCode?:   string

            cusIds?:   string

            conOrgClassName?:   string

            seqno?:   string

            dualOrgCode?:   string

            appStatus?:    Array < number >

            balOrgName?:   string

            version?:   number

            excessPrice?:   number

            driverId?:   number

            cusClassNames?:   string

            transExpense?:   number

            dualOrgId?:   string

            driverName?:   string

            conOrgCode?:   string

            modifierName?:   string

            conOrgClassCode?:   string

        }

        type SalDailyTransExpense对象 = {

            lastModifiedAt?:   string

            seqNo?:   string

            creatorName?:   string

            remark?:   string

            dualOrgName?:   string

            balOrgCode?:   string

            routeName?:   string

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            driverCode?:   string

            billFlag?:   number

            docStatus?:   number

            routeId?:   number

            conOrgName?:   string

            costName?:   string

            conOrgId?:   number

            transDate?:   string

            balOrgId?:   number

            id?:   number

            modifierOrgCode?:   string

            auditorName?:   string

            routeCode?:   string

            creatorCode?:   string

            entId?:   number

            dualOrgCode?:   string

            appStatus?:   number

            balOrgName?:   string

            version?:   number

            costCode?:   string

            deleted?:   number

            driverId?:   number

            auditTime?:   string

            transRouteId?:   number

            transExpense?:   number

            dualOrgId?:   number

            auditorCode?:   string

            driverName?:   string

            searchWord?:   string

            conOrgCode?:   string

            modifierName?:   string

        }

        type PageBasReturnGoods对象 = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < BasReturnGoods对象 >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type QueryStockUpOrderInput = {

            isTempZone?:   number

            transitLineNo?:   string

            createdAtAfter?:   string

            preArrivalDateAfter:   string

            cusOrgCode?:    Array < string >

            seqno?:   string

            dualOrgCode?:    Array < string >

            creatorName?:   string

            createdAtBefore?:   string

            orderStatus?:   number

            orderIdentification?:   number

            inputModels:    Array < number >

            queryType:   number

            goodsCodeList?:    Array < string >

            goodsClassCode?:   string

            goodsTypeCodes?:    Array < string >

            updateStatus?:   number

            preArrivalDateBefore:   string

            isOut?:   number

            orderTypeList?:    Array < number >

            waveNu?:   string

        }

        type PageFindDailyTransRouteListOutput = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < FindDailyTransRouteListOutput >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type ApiResultPageSalGoodsGroupDetail对象 = {

            data?:  PageSalGoodsGroupDetail对象

            resultCode:   number

            errorMsg?:   string

        }

        type PageSalConsignPrintMapping对象 = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < SalConsignPrintMapping对象 >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type DelOrderIdInput = {

            id?:   number

            version?:   number

        }

        type PageableDTOSalDailyOtherExpenseSearchInputDTO = {

            pageable?:  Pageable

            dto?:  SalDailyOtherExpenseSearchInputDTO

        }



      /**description
       * 仓配域
       */

        /**
        * 单品包装物带出清单的创建
        * @method
        * @name 单品包装物带出清单的创建
        * @param  body - dtos * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellBasWrapperWithGoodsCreate (

            parameters : {
              'body'  : Array<BasWrapperWithGoods对象>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 删除单品组
        * @method
        * @name 删除单品组
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalGoodsGroupHeadDelete (

            parameters : {
              'body'  : UpdateInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量创建日线路维护列表
        * @method
        * @name 批量创建日线路维护列表
        * @param  body - inputList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalDailyTransRouteCreate (

            parameters : {
              'body'  : Array<CreateDailyTransRouteInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据主键查询记录SalGoodsGroupHead记录
        * @method
        * @name 根据主键查询记录SalGoodsGroupHead记录
        * @param integer id - id * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getSellSalGoodsGroupHeadGetById (

            parameters : {
              'id'  : number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量调整
        * @method
        * @name 批量调整
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalConsignPrintMappingAdjust (

            parameters : {
              'body'  : AddSalConsignPrintMappingInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 发货机构信息的获取
        * @method
        * @name 发货机构信息的获取
        * @param string driverCode - driverCode * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getSellSalDailyOtherExpenseGetOrgMsg (

            parameters : {
              'driverCode'  : string,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量新增
        * @method
        * @name 批量新增
        * @param  body - inputList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalCusReturnReserveBatchCreate (

            parameters : {
              'body'  : Array<CreateSalCusReturnReserveInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询列表信息
        * @method
        * @name 查询列表信息
        * @param  body - pageableDTO * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalDailyTransExpensePage (

            parameters : {
              'body'  : PageableDTOSalDailyTransExpenseSearchInputDTO,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 一日多配客户设置批量修改（包括启用、停用、删除）
        * @method
        * @name 一日多配客户设置批量修改（包括启用、停用、删除）
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalDistributeControlUpdate (

            parameters : {
              'body'  : UpdateSalDistributeControlInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 客户配送控制参数创建
        * @method
        * @name 客户配送控制参数创建
        * @param  body - dtos * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalOrderControlCreate (

            parameters : {
              'body'  : Array<SalOrderControlCreateInputDTO>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 拣货方案的修改
        * @method
        * @name 拣货方案的修改
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalOrderPickingCfgUpdate (

            parameters : {
              'body'  : SalOrderPickingCfgInputDTO,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 修改客户销售订单
        * @method
        * @name 修改客户销售订单
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function putSellOrderUpdate (

            parameters : {
              'body'  : UpdateCustomerOrderInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 创建紧急客户录入订单
        * @method
        * @name 创建紧急客户录入订单
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellOrderCreateCustomerOrder (

            parameters : {
              'body'  : CreateCustomerOrderInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 通过线路查询客户
        * @method
        * @name 通过线路查询客户
        * @param string key - key * @param integer routeId - routeId * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getSellBasCusRouteFindCusByRouteIdAndNameOrCode (

            parameters : {
              'key'  ?: string,
              'routeId'  : number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 客户配送控制参数启用/停用
        * @method
        * @name 客户配送控制参数启用/停用
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalOrderControlUpdateIsEnabled (

            parameters : {
              'body'  : SalOrderControlUpdateStatusInputDTO,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据主键查询记录SalConsignPrintMapping记录
        * @method
        * @name 根据主键查询记录SalConsignPrintMapping记录
        * @param integer id - id * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getSellSalConsignPrintMappingGetById (

            parameters : {
              'id'  : number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 客户订单控制更新
        * @method
        * @name 客户订单控制更新
        * @param  body - dtos * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalOrderControlUpdate (

            parameters : {
              'body'  : Array<SalOrderControlUpdateInputDTO>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据主键查询记录BasDriver记录
        * @method
        * @name 根据主键查询记录BasDriver记录
        * @param integer id - id * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getSellBasDriverGetById (

            parameters : {
              'id'  : number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量修改退货商品
        * @method
        * @name 批量修改退货商品
        * @param  body - entities * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellBasReturnGoodsUpdate (

            parameters : {
              'body'  : Array<BasReturnGoods对象>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询代录订单列表
        * @method
        * @name 查询代录订单列表
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellOrderFindInsteadOrders (

            parameters : {
              'body'  : PageableDTOQueryInsteadOrderInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 拣货方案的创建
        * @method
        * @name 拣货方案的创建
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalOrderPickingCfgCreate (

            parameters : {
              'body'  : SalOrderPickingCfgInputDTO,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量更新
        * @method
        * @name 批量更新
        * @param  body - inputList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalCusReturnReserveBatchUpdate (

            parameters : {
              'body'  : Array<UpdateSalCusReturnReserveInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 费用信息的获取
        * @method
        * @name 费用信息的获取
        * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getSellSalDailyTransExpenseGetCostTypeDetail (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询   司机定义列表
        * @method
        * @name 查询   司机定义列表
        * @param  body - basDriverDTO * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellBasDriverPage (

            parameters : {
              'body'  : PageableDTOFindBasDriverListInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 单品包装物带出清单启用与停用
        * @method
        * @name 单品包装物带出清单启用与停用
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellBasWrapperWithGoodsUpdateIsEnabled (

            parameters : {
              'body'  : BasWrapperWithGoodsUpdateStatusInputDTO,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量修改退货规则
        * @method
        * @name 批量修改退货规则
        * @param  body - entities * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellBasReturnGoodsUpdateReturnRules (

            parameters : {
              'body'  : Array<BasReturnRules对象>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 获取单个模板信心
        * @method
        * @name 获取单个模板信心
        * @param integer id - id * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getSellSalDistributeTemplateGetById (

            parameters : {
              'id'  : number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询权限路线定义列表
        * @method
        * @name 查询权限路线定义列表
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellBasRoutePageAuth (

            parameters : {
              'body'  : PageableDTOFindBasRouteListByAuthInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 一日多配模板设置列表查询
        * @method
        * @name 一日多配模板设置列表查询
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalDistributeTemplatePage (

            parameters : {
              'body'  : PageableDTOSalAuthAdapter_1,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 移出或移入页面的商品查询
        * @method
        * @name 移出或移入页面的商品查询
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalGoodsGroupHeadListInMove (

            parameters : {
              'body'  : PageableDTOFindGoodsInMoveInputDTO,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 启用/停用
        * @method
        * @name 启用/停用
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalCusReturnReserveUpdateIsEnabled (

            parameters : {
              'body'  : UpdateEnableSalCusReturnReserveInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * test
        * @method
        * @name test
        * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellBasRouteTestSendMq (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 单品包装物带出清单的对应的单位的选择（录入）
        * @method
        * @name 单品包装物带出清单的对应的单位的选择（录入）
        * @param string goodsCode - goodsCode * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getSellBasWrapperWithGoodsList (

            parameters : {
              'goodsCode'  : string,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询备货订单列表
        * @method
        * @name 查询备货订单列表
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellOrderFindStockUpOrders (

            parameters : {
              'body'  : PageableDTOQueryStockUpOrderInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 配送中心修改
        * @method
        * @name 配送中心修改
        * @param  body - pageableDTO * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellDeliveryCenterRetrieve (

            parameters : {
              'body'  : PageableDTORetrieveDeliveryCenterInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批零查询模板
        * @method
        * @name 批零查询模板
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalDistributeTemplateList (

            parameters : {
              'body'  : SalAuthAdapter_2,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 将符合查询条件的订单的状态全部转为录入完成状态
        * @method
        * @name 将符合查询条件的订单的状态全部转为录入完成状态
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function putSellOrderAllDraftToFormal (

            parameters : {
              'body'  : QuerySalOrderHeadInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查看日费用
        * @method
        * @name 查看日费用
        * @param integer id - id * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getSellSalDailyTransRouteGetExpenseById (

            parameters : {
              'id'  : number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询客户路线定义列表
        * @method
        * @name 查询客户路线定义列表
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellBasCusRoutePage (

            parameters : {
              'body'  : PageableDTOFindBasCusRouteListInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量删除路线定义记录
        * @method
        * @name 批量删除路线定义记录
        * @param  body - inputList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellBasRouteDelete (

            parameters : {
              'body'  : Array<DeleteRouteInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量修改日线路
        * @method
        * @name 批量修改日线路
        * @param  body - list * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalDailyTransRouteUpdate (

            parameters : {
              'body'  : Array<UpdateDailyTransRouteInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询路线定义列表
        * @method
        * @name 查询路线定义列表
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellBasRoutePage (

            parameters : {
              'body'  : PageableDTOFindBasRouteListInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 创建客户录入订单
        * @method
        * @name 创建客户录入订单
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellOrderCreateUrgentCustomerOrder (

            parameters : {
              'body'  : CreateCustomerOrderInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 草稿状态转正式状态
        * @method
        * @name 草稿状态转正式状态
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function putSellOrderDraftToFormal (

            parameters : {
              'body'  : UpdateOrderStatusInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 一日多配客户设置批量创建
        * @method
        * @name 一日多配客户设置批量创建
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalDistributeControlCreate (

            parameters : {
              'body'  : Array<SalAuthAdapter>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量修改司机定义记录
        * @method
        * @name 批量修改司机定义记录
        * @param  body - inputs * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellBasDriverUpdate (

            parameters : {
              'body'  : Array<UpdateDriverInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 更新
        * @method
        * @name 更新
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalCusReturnReserveUpdate (

            parameters : {
              'body'  : UpdateSalCusReturnReserveInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据主键查询记录SalDailyTransRoute记录
        * @method
        * @name 根据主键查询记录SalDailyTransRoute记录
        * @param integer id - id * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getSellSalDailyTransRouteGetById (

            parameters : {
              'id'  : number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 订单备货
        * @method
        * @name 订单备货
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellOrderStockUp (

            parameters : {
              'body'  : OrderStockUpInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 移入
        * @method
        * @name 移入
        * @param  body - moveInputDTO * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalGoodsGroupHeadMoveIn (

            parameters : {
              'body'  : MoveInputDTO,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询单品组列表
        * @method
        * @name 查询单品组列表
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalGoodsGroupHeadPage (

            parameters : {
              'body'  : PageableDTOSalGoodsGroupSearchInputDTO,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 修改其他费用
        * @method
        * @name 修改其他费用
        * @param  body - dtos * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalDailyOtherExpenseUpdate (

            parameters : {
              'body'  : Array<SalDailyOtherExpense对象>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 客户配送控制参数删除
        * @method
        * @name 客户配送控制参数删除
        * @param  body - dtos * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalOrderControlDelete (

            parameters : {
              'body'  : Array<UpdateInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 锁定订单
        * @method
        * @name 锁定订单
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function putSellOrderLock (

            parameters : {
              'body'  : UpdateOrderStatusInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 拣货方案的因素的查询
        * @method
        * @name 拣货方案的因素的查询
        * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getSellSalOrderPickingCfgGetEnumInfo (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 状态的全部变更
        * @method
        * @name 状态的全部变更
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalDailyOtherExpenseUpdateAppStatusAll (

            parameters : {
              'body'  : SalDailyOtherExpenseSearchInputDTO,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询必上商品和周期发货商品信息
        * @method
        * @name 查询必上商品和周期发货商品信息
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellOrderFindMustAndCycleGoods (

            parameters : {
              'body'  : QueryMustAndCycleGoodsInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 单品包装物带出清单的查询
        * @method
        * @name 单品包装物带出清单的查询
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellBasWrapperWithGoodsPage (

            parameters : {
              'body'  : PageableDTOBasWrapperWithGoodsSearchInputDTO,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量启用enableDriver/禁用disableDriver
        * @method
        * @name 批量启用enableDriver/禁用disableDriver
        * @param  body - inputs * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellBasDriverUpdateIsEnable (

            parameters : {
              'body'  : UpdateBasDriver,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 其他费用状态的变更
        * @method
        * @name 其他费用状态的变更
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalDailyOtherExpenseUpdateAppStatus (

            parameters : {
              'body'  : SalDailyOtherExpensesUpdateAppStatusInputDTO,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据主键查询记录SalOrderPickingCfgHead记录
        * @method
        * @name 根据主键查询记录SalOrderPickingCfgHead记录
        * @param integer id - id * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getSellSalOrderPickingCfgGetById (

            parameters : {
              'id'  : number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 单品包装物带出清单的更新
        * @method
        * @name 单品包装物带出清单的更新
        * @param  body - dtos * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellBasWrapperWithGoodsUpdate (

            parameters : {
              'body'  : Array<BasWrapperWithGoodsUpdateInputDTO>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 费用信息的获取(包含送货费用)
        * @method
        * @name 费用信息的获取(包含送货费用)
        * @param string createdAt -  * @param string employeeCode -  * @param string employeeName -  * @param string entCode -  * @param integer entId -  * @param string lastModifiedAt -  * @param string oaid -  * @param string orgCode -  * @param string userCode -  * @param string userName -  * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getSellSalDailyOtherExpenseListCostTypeDetail (

            parameters : {
              'createdAt'  ?: string,
              'employeeCode'  ?: string,
              'employeeName'  ?: string,
              'entCode'  ?: string,
              'entId'  ?: number,
              'lastModifiedAt'  ?: string,
              'oaid'  ?: string,
              'orgCode'  ?: string,
              'userCode'  ?: string,
              'userName'  ?: string,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 修改费用
        * @method
        * @name 修改费用
        * @param  body - dtos * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalDailyTransExpenseUpdate (

            parameters : {
              'body'  : Array<SalDailyTransExpenseUpdateInputDTO>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询列表
        * @method
        * @name 查询列表
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalConsignPrintMappingPage (

            parameters : {
              'body'  : PageableDTOFindSalConsignPrintListInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询司机定义列表
        * @method
        * @name 查询司机定义列表
        * @param  body - basDriverDTO * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellBasDriverPageAuth (

            parameters : {
              'body'  : PageableDTOFindBasDriverListInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 配送中心修改
        * @method
        * @name 配送中心修改
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function putSellDeliveryCenterUpdate (

            parameters : {
              'body'  : UpdateDeliveryCenterInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 移出或移入页面已创建的单品组类型的查询
        * @method
        * @name 移出或移入页面已创建的单品组类型的查询
        * @param string dualOrgCode - dualOrgCode * @param string goodsGroupCode - goodsGroupCode * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getSellSalGoodsGroupHeadFindGoodsGroupTypeInMove (

            parameters : {
              'dualOrgCode'  : string,
              'goodsGroupCode'  : string,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 新增页面单品组类型（除了机构已关联的单品组）的查詢
        * @method
        * @name 新增页面单品组类型（除了机构已关联的单品组）的查詢
        * @param string dualOrgCode - dualOrgCode * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getSellSalGoodsGroupHeadListType (

            parameters : {
              'dualOrgCode'  : string,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 审核查询列表信息
        * @method
        * @name 审核查询列表信息
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalDailyOtherExpensePageAudit (

            parameters : {
              'body'  : PageableDTOSalDailyOtherExpenseSearchInputDTO,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询客户录入订单列表
        * @method
        * @name 查询客户录入订单列表
        * @param string createdAtAfter - 录入日期后 * @param string createdAtBefore - 录入日期前 * @param string creatorName - 录入人名称 * @param array cusOrgCode - 客户Code * @param array dualOrgCode - 机构Code * @param string goodsClassCode - 商品分类代码，前端传值 0：非鲜果 1：鲜果 2:所有 * @param array goodsCodeList - 商品代码 * @param array goodsTypeCodes - 商品类型代码 * @param array inputModels - 订单录入的形式 1:客户录入 2: 代录 3: 系统自动 4: 追加  5:机构订单跨区销售 * @param integer isOut - 是否门店（0否，1是） * @param integer isTempZone - 是否分温区 * @param integer orderIdentification - 下单标识 * @param integer orderStatus - 订单状态 * @param array orderTypeList - 订单类型 * @param integer pageablePageNumber - 分页页数,从0开始 * @param integer pageablePageSize - 分页大小 * @param string pageableSort - 排序,使用 property,asc / property,desc 的格式,property为属性名 * @param string preArrivalDateAfter - 要求到货日期后 * @param string preArrivalDateBefore - 要求到货日期前 * @param integer queryType - 页面方面形式：客户录入页面10, 代录页面20, 备货页面30, 审核40， 机构订单50 * @param string seqno - 订单号 * @param string transitLineNo - 线路号 * @param integer updateStatus - 更新状态 * @param string waveNu - 波次号 * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getSellOrderFindCustomerOrders (

            parameters : {
              'createdAtAfter'  ?: string,
              'createdAtBefore'  ?: string,
              'creatorName'  ?: string,
              'cusOrgCode'  ?: Array<string>,
              'dualOrgCode'  ?: Array<string>,
              'goodsClassCode'  ?: string,
              'goodsCodeList'  ?: Array<string>,
              'goodsTypeCodes'  ?: Array<string>,
              'inputModels'  : Array<number>,
              'isOut'  ?: number,
              'isTempZone'  ?: number,
              'orderIdentification'  ?: number,
              'orderStatus'  ?: number,
              'orderTypeList'  ?: Array<number>,
              'pageablePageNumber'  ?: number,
              'pageablePageSize'  ?: number,
              'pageableSort'  ?: string,
              'preArrivalDateAfter'  : string,
              'preArrivalDateBefore'  : string,
              'queryType'  : number,
              'seqno'  ?: string,
              'transitLineNo'  ?: string,
              'updateStatus'  ?: number,
              'waveNu'  ?: string,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 校验客户是否可以下单
        * @method
        * @name 校验客户是否可以下单
        * @param string cusOrgCode - 客户编码 * @param integer cusOrgId - 客户ID * @param string dualOrgCode - 配送中心编码 * @param integer inputModel - 订单录入方式(1:客户录入 2: 代录 3: 系统自动 4：追加 5: 机构跨区销售) * @param integer orderType - 订单类型{1:手工,2:加急,3:自动,4:分货,5:调拨,6:提货单,7:周转筐,8:心享} * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getSellOrderCanOrder (

            parameters : {
              'cusOrgCode'  : string,
              'cusOrgId'  : number,
              'dualOrgCode'  : string,
              'inputModel'  : number,
              'orderType'  : number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 导出
        * @method
        * @name 导出
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalOrderControlExportSalOrderControl (

            parameters : {
              'body'  : SalOrderControlSerachInputDTO,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 删除订单
        * @method
        * @name 删除订单
        * @param integer id - id * @param string seqno - seqno * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function deleteSellOrderDelete (

            parameters : {
              'id'  : number,
              'seqno'  : string,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量生成日费用
        * @method
        * @name 批量生成日费用
        * @param  body - list * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalDailyTransRouteMakeExpense (

            parameters : {
              'body'  : Array<MakeDailyCostInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量创建司机定义记录
        * @method
        * @name 批量创建司机定义记录
        * @param  body - inputs * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellBasDriverCreate (

            parameters : {
              'body'  : Array<CreateDriverInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量创建客户路线定义记录
        * @method
        * @name 批量创建客户路线定义记录
        * @param  body - inputList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellBasCusRouteCreate (

            parameters : {
              'body'  : Array<CreateCusRouteInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 配送中心详情页面查看子机构信息
        * @method
        * @name 配送中心详情页面查看子机构信息
        * @param integer id - id * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function putSellDeliveryCenterRetrieveOrgById (

            parameters : {
              'id'  : number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 删除拣货方案
        * @method
        * @name 删除拣货方案
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalOrderPickingCfgDelete (

            parameters : {
              'body'  : UpdateInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量修改模板（启用停用、删除）
        * @method
        * @name 批量修改模板（启用停用、删除）
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalDistributeTemplateUpdate (

            parameters : {
              'body'  : CreateTemplateInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量启用enableCusRoute/禁用disableCusRoute
        * @method
        * @name 批量启用enableCusRoute/禁用disableCusRoute
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellBasCusRouteUpdateIsEnable (

            parameters : {
              'body'  : UpdateBasCusRoute,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 删除费用
        * @method
        * @name 删除费用
        * @param  body - dtos * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalDailyTransExpenseDelete (

            parameters : {
              'body'  : Array<UpdateInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 更新周转筐定义的状态
        * @method
        * @name 更新周转筐定义的状态
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalOrgBasketUpdate (

            parameters : {
              'body'  : SalOrgBasketUpdateInputDTO,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 退货商品管理的查询
        * @method
        * @name 退货商品管理的查询
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellBasReturnGoodsPageReturnGoods (

            parameters : {
              'body'  : PageableDTOReturnGoodsOnManagerInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 配送中心新增
        * @method
        * @name 配送中心新增
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellDeliveryCenterCreate (

            parameters : {
              'body'  : CreateDeliveryCenterInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量修改
        * @method
        * @name 批量修改
        * @param  body - inputList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalConsignPrintMappingUpdate (

            parameters : {
              'body'  : Array<UpdateSalConsignPrintMappingInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 全部取消锁定
        * @method
        * @name 全部取消锁定
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function putSellOrderUnlockAll (

            parameters : {
              'body'  : QuerySalOrderHeadInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量调整
        * @method
        * @name 批量调整
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalCusReturnReserveAdjust (

            parameters : {
              'body'  : BatchUpdateEnableSalCusReturnReserveInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 列表查询
        * @method
        * @name 列表查询
        * @param  body - pageableDTO * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalCusReturnReservePage (

            parameters : {
              'body'  : PageableDTOFindSalCusReturnReserveListInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询权限下日线路维护列表
        * @method
        * @name 查询权限下日线路维护列表
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalDailyTransRoutePageAuth (

            parameters : {
              'body'  : PageableDTOFindDailyTransRouteListByAuthInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询日线路维护列表
        * @method
        * @name 查询日线路维护列表
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalDailyTransRoutePage (

            parameters : {
              'body'  : PageableDTOFindDailyTransRouteListInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 自测机构商品授权
        * @method
        * @name 自测机构商品授权
        * @param integer entId - entId * @param  body - inputDTOList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalGoodsGroupHeadAutoCreateDetail (

            parameters : {
              'entId'  : number,
              'body'  : Array<CreateDetailInputDTO>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 导出
        * @method
        * @name 导出
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellBasDriverExportDriver (

            parameters : {
              'body'  : FindBasDriverListInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查看配送中心详情
        * @method
        * @name 查看配送中心详情
        * @param integer id - id * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function putSellDeliveryCenterRetrieveById (

            parameters : {
              'id'  : number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 周转筐定义的创建
        * @method
        * @name 周转筐定义的创建
        * @param  body - dtos * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalOrgBasketCreate (

            parameters : {
              'body'  : Array<SalOrgBasket对象>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 删除其他费用
        * @method
        * @name 删除其他费用
        * @param  body - dtos * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalDailyOtherExpenseDelete (

            parameters : {
              'body'  : Array<UpdateInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 一日多配客户设置列表查询
        * @method
        * @name 一日多配客户设置列表查询
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalDistributeControlPage (

            parameters : {
              'body'  : PageableDTOSalAuthAdapter,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 取消锁定订单
        * @method
        * @name 取消锁定订单
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function putSellOrderUnlock (

            parameters : {
              'body'  : UpdateOrderStatusInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量调整
        * @method
        * @name 批量调整
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalDistributeControlAdjust (

            parameters : {
              'body'  : BatchAdjustControlInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 客户配送控制参数（配送）更新
        * @method
        * @name 客户配送控制参数（配送）更新
        * @param  body - dtos * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalOrderControlUpdateSale (

            parameters : {
              'body'  : Array<SalOrderControlUpdateWithSaleInputDTO>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 审核查询列表信息
        * @method
        * @name 审核查询列表信息
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalDailyTransExpensePageAudit (

            parameters : {
              'body'  : PageableDTOSalDailyTransExpenseSearchInputDTO,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 退货商品管理的批量调整
        * @method
        * @name 退货商品管理的批量调整
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellBasReturnGoodsAdjust (

            parameters : {
              'body'  : AdjustMentReturnGoodsInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量创建路线定义记录
        * @method
        * @name 批量创建路线定义记录
        * @param  body - inputList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellBasRouteCreate (

            parameters : {
              'body'  : Array<CreateRouteInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询周转筐定义列表
        * @method
        * @name 查询周转筐定义列表
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalOrgBasketPage (

            parameters : {
              'body'  : PageableDTOSalOrgBasketSearchInputDTO,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 新增
        * @method
        * @name 新增
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalCusReturnReserveCreate (

            parameters : {
              'body'  : CreateSalCusReturnReserveInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据主键查询记录SalOrderControl记录
        * @method
        * @name 根据主键查询记录SalOrderControl记录
        * @param integer id - id * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getSellSalOrderControlGetById (

            parameters : {
              'id'  : number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据主键查询记录BasCusRoute记录
        * @method
        * @name 根据主键查询记录BasCusRoute记录
        * @param integer id - id * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getSellBasCusRouteGetById (

            parameters : {
              'id'  : number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * findSalOrderControls
        * @method
        * @name findSalOrderControls
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalOrderControlPage (

            parameters : {
              'body'  : PageableDTOSalOrderControlSerachInputDTO,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询路线定义列表
        * @method
        * @name 查询路线定义列表
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellBasRoutePageWriteAuth (

            parameters : {
              'body'  : PageableDTOFindBasRouteListInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 更新拣货方案的默认状态
        * @method
        * @name 更新拣货方案的默认状态
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalOrderPickingCfgUpdateDefault (

            parameters : {
              'body'  : SalOrderPickingCfgUpdateStatusInputDTO,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量删除BasWrapperWithGoods记录
        * @method
        * @name 批量删除BasWrapperWithGoods记录
        * @param  body - idList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellBasWrapperWithGoodsDelete (

            parameters : {
              'body'  : Array<number>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量删除订单
        * @method
        * @name 批量删除订单
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function deleteSellOrderBatchDelete (

            parameters : {
              'body'  : BatchDeleteCustomerOrderInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量修改客户路线定义记录
        * @method
        * @name 批量修改客户路线定义记录
        * @param  body - inputList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellBasCusRouteUpdate (

            parameters : {
              'body'  : Array<UpdateCusRouteInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量创建模板
        * @method
        * @name 批量创建模板
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalDistributeTemplateCreate (

            parameters : {
              'body'  : CreateTemplateInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 退货规则的查询
        * @method
        * @name 退货规则的查询
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellBasReturnGoodsPageRules (

            parameters : {
              'body'  ?: PageableDTOReturnRulesInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 创建费用
        * @method
        * @name 创建费用
        * @param  body - dtos * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalDailyTransExpenseCreate (

            parameters : {
              'body'  : Array<SalDailyTransExpense对象>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 将符合查询条件的订单的状态全部转为已锁定状态
        * @method
        * @name 将符合查询条件的订单的状态全部转为已锁定状态
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function putSellOrderLockAll (

            parameters : {
              'body'  : QuerySalOrderHeadInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 费用信息的获取
        * @method
        * @name 费用信息的获取
        * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getSellSalDailyOtherExpenseGetCostTypeDetail (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据id删除配送中心
        * @method
        * @name 根据id删除配送中心
        * @param  body - ids * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellDeliveryCenterDelete (

            parameters : {
              'body'  : Array<number>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 状态的全部变更
        * @method
        * @name 状态的全部变更
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalDailyTransExpenseUpdateAppStatusAll (

            parameters : {
              'body'  : SalDailyTransExpenseSearchInputDTO,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * isControlExpense
        * @method
        * @name isControlExpense
        * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getSellSalDailyTransRouteIsControlExpenseTime (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 作废订单
        * @method
        * @name 作废订单
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function putSellOrderDisable (

            parameters : {
              'body'  : UpdateOrderStatusInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据订单id获取订单信息
        * @method
        * @name 根据订单id获取订单信息
        * @param integer orderId - orderId * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getSellOrderGetById (

            parameters : {
              'orderId'  : number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 正式状态转草稿状态
        * @method
        * @name 正式状态转草稿状态
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function putSellOrderFormalToDraft (

            parameters : {
              'body'  : UpdateOrderStatusInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量创建
        * @method
        * @name 批量创建
        * @param  body - inputList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getSellSalConsignPrintMappingCreate (

            parameters : {
              'body'  : Array<AddSalConsignPrintMappingInput_1>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量取消合单打印设置
        * @method
        * @name 批量取消合单打印设置
        * @param  body - ids * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalConsignPrintMappingCancelPrintMapping (

            parameters : {
              'body'  : Array<number>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量修改路线定义记录
        * @method
        * @name 批量修改路线定义记录
        * @param  body - inputList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellBasRouteUpdate (

            parameters : {
              'body'  : Array<UpdateRouteInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 单品组的创建
        * @method
        * @name 单品组的创建
        * @param  body - dtos * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalGoodsGroupHeadCreate (

            parameters : {
              'body'  : Array<SalGoodsGroupHead对象>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 删除
        * @method
        * @name 删除
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalCusReturnReserveDelete (

            parameters : {
              'body'  : DeleteSalCusReturnReserveInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量删除司机定义记录
        * @method
        * @name 批量删除司机定义记录
        * @param  body - inputs * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellBasDriverDelete (

            parameters : {
              'body'  : Array<DeleteDriverInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量删除客户路线定义记录
        * @method
        * @name 批量删除客户路线定义记录
        * @param  body - list * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellBasCusRouteDelete (

            parameters : {
              'body'  : Array<DeleteCusRouteInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询审核订单列表
        * @method
        * @name 查询审核订单列表
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellOrderFindAuditOrders (

            parameters : {
              'body'  : PageableDTOQueryAppendOrderInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 客户线路导出
        * @method
        * @name 客户线路导出
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellBasRouteExportRoutes (

            parameters : {
              'body'  : FindBasRouteListInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 下载客户订单控制模板
        * @method
        * @name 下载客户订单控制模板
        * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getSellSalOrderControlExportSalOrderContorlTemplate (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 状态的变更
        * @method
        * @name 状态的变更
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalDailyTransExpenseUpdateAppStatus (

            parameters : {
              'body'  : SalDailyTransExpensesUpdateAppStatusInputDTO,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询拣货方案列表
        * @method
        * @name 查询拣货方案列表
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalOrderPickingCfgPage (

            parameters : {
              'body'  : PageableDTOSalOrderPickingCfgSearchInputDTO,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询列表信息
        * @method
        * @name 查询列表信息
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalDailyOtherExpensePage (

            parameters : {
              'body'  : PageableDTOSalDailyOtherExpenseSearchInputDTO,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据主键查询记录BasRoute记录
        * @method
        * @name 根据主键查询记录BasRoute记录
        * @param integer id - id * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getSellBasRouteGetById (

            parameters : {
              'id'  : number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询线路温区
        * @method
        * @name 查询线路温区
        * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getSellBasRouteWqList (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 创建其他费用
        * @method
        * @name 创建其他费用
        * @param  body - dtos * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalDailyOtherExpenseCreate (

            parameters : {
              'body'  : Array<SalDailyOtherExpense对象>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 删除周转筐定义
        * @method
        * @name 删除周转筐定义
        * @param  body - updateInput * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalOrgBasketDelete (

            parameters : {
              'body'  : Array<UpdateInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量删除日线路
        * @method
        * @name 批量删除日线路
        * @param  body - list * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalDailyTransRouteDelete (

            parameters : {
              'body'  : Array<DeleteDailyTransRouteInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 日线路新增时选择送货客户
        * @method
        * @name 日线路新增时选择送货客户
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellBasCusRoutePageSuperQuery (

            parameters : {
              'body'  : PageableDTOFindBasCusRouteListInput_1,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量启用enableRoute/停用disableRoute
        * @method
        * @name 批量启用enableRoute/停用disableRoute
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellBasRouteUpdateIsEnable (

            parameters : {
              'body'  : UpdateBasRoute,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 更新拣货方案的可用状态
        * @method
        * @name 更新拣货方案的可用状态
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalOrderPickingCfgUpdateIsEnabled (

            parameters : {
              'body'  : SalOrderPickingCfgUpdateStatusInputDTO,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 移出
        * @method
        * @name 移出
        * @param  body - moveInputDTO * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postSellSalGoodsGroupHeadMoveOut (

            parameters : {
              'body'  : MoveInputDTO,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

      }
  }
}
export {}

