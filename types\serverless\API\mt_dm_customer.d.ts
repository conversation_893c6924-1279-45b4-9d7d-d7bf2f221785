/*this is generated code */
import  { AxiosResponse } from 'axios'
declare global{
  namespace API {
    /** mt-dm-customer
      * 文档链接：http://op.pagoda.com.cn/?#/technical-doc
      * {"version":"1.0.0","title":"1.0.0","contact":{}}
      * 10.8.65.3
    */
    namespace mt_dm_customer {

        type AccountMemberReq = {

            bizHead?:  HeadReq

            memberIds?:    Array < number >

        }

        type AccountMemberResp = {

            fruitCoinAccountBalance?:   number

            integralAccountBalance?:   number

            integralAccountTotal?:   number

            mainAccountBalance?:   number

            memberId?:   number

        }

        type AccountPasswordDTO = {

            identity?:   string

            memberId?:   number

            memberNum?:   number

            params?:   any

            password?:   string

            recordId?:   number

        }

        type AccountPasswordRecordDTO = {

            identity?:   string

            memberId?:   number

            memberNum?:   number

            recordId?:   number

        }

        type AccountPasswordRecordResp = {

            accountPasswordDTOS?:    Array < AccountPasswordRecordDTO >

            size?:   number

        }

        type AccountPasswordReq = {

            identity?:   string

            memberId?:   number

            memberNum?:   number

            password?:   string

        }

        type AccountPasswordResp = {

            createTime?:   string

            deleteTime?:   string

            identity?:   string

            memberId?:   number

            memberNum?:   number

            password?:   string

            recordId?:   number

            updateTime?:   string

        }

        type ActivityInfoResp = {

            amountDiscountActivityList?:    Array < DiscountActivityInfo >

            giveActivityList?:    Array < GiveActivityInfo >

        }

        type AddBlackWhiteListReq = {

            bizHead?:  HeadReq

            controlType?:   number

            identity?:   string

            identityScene?:   string

            invalidTime?:   string

            listStatus?:   string

            listType?:   string

            memberId?:   string

            remark?:   string

            source?:   string

            validTime?:   string

        }

        type ApiResult = {

            data?:   any

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultAccountPasswordDTO = {

            data?:  AccountPasswordDTO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultAccountPasswordRecordResp = {

            data?:  AccountPasswordRecordResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultAccountPasswordResp = {

            data?:  AccountPasswordResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultBatchQueryInBlackWhiteResp = {

            data?:  BatchQueryInBlackWhiteResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultBlackWhiteListReq = {

            data?:  BlackWhiteListReq

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultBlackWhiteList = {

            data?:  BlackWhiteList

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultBusinessWithdraw = {

            data?:  BusinessWithdraw

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultCancelMemberInfoByIdentityResp = {

            data?:  CancelMemberInfoByIdentityResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultCancelPaymentResp = {

            data?:  CancelPaymentResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultCloseTradeOrderResp = {

            data?:  CloseTradeOrderResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultCouponBatchTemplateReqDTO = {

            data?:  CouponBatchTemplateReqDTO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultCouponBuyOrderResp = {

            data?:  CouponBuyOrderResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultCouponTemplateReq = {

            data?:  CouponTemplateReq

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultCouponTypeTemplate = {

            data?:  CouponTypeTemplate

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultCreateTradeOrderResp = {

            data?:  CreateTradeOrderResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultCreateWalletRechargeOrderResp = {

            data?:  CreateWalletRechargeOrderResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultDenominationReq = {

            data?:  DenominationReq

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultIsRechargeResp = {

            data?:  IsRechargeResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListAccountMemberResp = {

            data?:    Array < AccountMemberResp >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListBusinessAccountRecord = {

            data?:    Array < BusinessAccountRecord >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListBusinessEarningsDetail = {

            data?:    Array < BusinessEarningsDetail >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListBusinessEarnings = {

            data?:    Array < BusinessEarnings >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListBusinessWithdraw = {

            data?:    Array < BusinessWithdraw >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListCouponBatchRespDTO = {

            data?:    Array < CouponBatchRespDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListCouponBuyOrderRespDTO = {

            data?:    Array < CouponBuyOrderRespDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListCouponOrderDetailResp = {

            data?:    Array < CouponOrderDetailResp >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListCouponOrderResp = {

            data?:    Array < CouponOrderResp >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListDenominationManagerDTO = {

            data?:    Array < DenominationManagerDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListEquityTypeResp = {

            data?:    Array < EquityTypeResp >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListInvitePayMemberResp = {

            data?:    Array < InvitePayMemberResp >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListMemberEquityReceiveRecordResp = {

            data?:    Array < MemberEquityReceiveRecordResp >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListMemberLevelLinkedInfoRespDTO = {

            data?:    Array < MemberLevelLinkedInfoRespDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListPayMemberDetailPeriodRespDTO = {

            data?:    Array < PayMemberDetailPeriodRespDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListPayMemberIdentifyResp = {

            data?:    Array < PayMemberIdentifyResp >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListPayMemberPeriodInfoResp = {

            data?:    Array < PayMemberPeriodInfoResp >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListPayMemberPeriodResp = {

            data?:    Array < PayMemberPeriodResp >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListPayMemberPeriodSetPackageValueResp = {

            data?:    Array < PayMemberPeriodSetPackageValueResp >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListPayMemberRedeemCodeBatchFuzzyResp = {

            data?:    Array < PayMemberRedeemCodeBatchFuzzyResp >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListPayMemberSaleableResp = {

            data?:    Array < PayMemberSaleableResp >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListPayMemberSaveDetailMoneyRespDTO = {

            data?:    Array < PayMemberSaveDetailMoneyRespDTO >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListPayMemberSaveMoneyDetail = {

            data?:    Array < PayMemberSaveMoneyDetail >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListPayMemberTypeResp = {

            data?:    Array < PayMemberTypeResp >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListQueryMemberEquityListResp = {

            data?:    Array < QueryMemberEquityListResp >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListSmsTemplate = {

            data?:    Array < SmsTemplate >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListTempMemberInfoResp = {

            data?:    Array < TempMemberInfoResp >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListint = {

            data?:    Array < number >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListlong = {

            data?:    Array < number >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListmember_relation = {

            data?:    Array < member_relation >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListstring = {

            data?:    Array < string >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultMemberBaseInfo4IdentityResp = {

            data?:  MemberBaseInfo4IdentityResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultMemberBaseInfo4MemberIdsResp = {

            data?:  MemberBaseInfo4MemberIdsResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultMemberBaseInfo4PhonesResp = {

            data?:  MemberBaseInfo4PhonesResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultMemberBaseInfo4UnionIdResp = {

            data?:  MemberBaseInfo4UnionIdResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultMemberBaseInfoByJoinConditionResp = {

            data?:  MemberBaseInfoByJoinConditionResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultMemberBaseInfoResp = {

            data?:  MemberBaseInfoResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultMemberEquityMonthListByTypeCodeResp = {

            data?:  MemberEquityMonthListByTypeCodeResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultMemberGrowthBalanceQueryResp = {

            data?:  MemberGrowthBalanceQueryResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultMemberLevelResp = {

            data?:  MemberLevelResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultMemberVisitorResp = {

            data?:  MemberVisitorResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultMutexBatchResp = {

            data?:  MutexBatchResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPageBlackWhiteListReq = {

            data?:  PageBlackWhiteListReq

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPageBusinessWithdraw = {

            data?:  PageBusinessWithdraw

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPageCouponBatchTemplate = {

            data?:  PageCouponBatchTemplate

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPageCouponTypeTemplate = {

            data?:  PageCouponTypeTemplate

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPageDenominationManagerDTO = {

            data?:  PageDenominationManagerDTO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPageMemberBehaviorRecord = {

            data?:  PageMemberBehaviorRecord

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPageMemberEquity = {

            data?:  PageMemberEquity

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPageMemberGrowthModifyRecordQueryResp = {

            data?:  PageMemberGrowthModifyRecordQueryResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPageMemberLevelModifyRecordResp = {

            data?:  PageMemberLevelModifyRecordResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPageMemberNotifyConfig = {

            data?:  PageMemberNotifyConfig

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPageMemberTransferDetailResp = {

            data?:  PageMemberTransferDetailResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPagePayMemberInfoWillExpireResp = {

            data?:  PagePayMemberInfoWillExpireResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPagePayMemberQueryRedeemCodeBatchResp = {

            data?:  PagePayMemberQueryRedeemCodeBatchResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPagePayMemberQueryRedeemCodeDetailResp = {

            data?:  PagePayMemberQueryRedeemCodeDetailResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPagePayMemberSaveMoneyDetail = {

            data?:  PagePayMemberSaveMoneyDetail

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPagePayMemberTypeResp = {

            data?:  PagePayMemberTypeResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPageSmsTemplate = {

            data?:  PageSmsTemplate

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPartRefundResDTO = {

            data?:  PartRefundResDTO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPayMemberBuyResp = {

            data?:  PayMemberBuyResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPayMemberInfoRespDTO = {

            data?:  PayMemberInfoRespDTO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPayMemberModifyRecordQueryResp = {

            data?:  PayMemberModifyRecordQueryResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPayMemberQueryRedeemCodeBatchResp = {

            data?:  PayMemberQueryRedeemCodeBatchResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPayMemberQueryRedeemCodeResp = {

            data?:  PayMemberQueryRedeemCodeResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPayMemberRecord = {

            data?:  PayMemberRecord

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPayMemberRefundRecordInfoResp = {

            data?:  PayMemberRefundRecordInfoResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPayMemberSaveTotalMoneyRespDTO = {

            data?:  PayMemberSaveTotalMoneyRespDTO

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPayMemberTypeResp = {

            data?:  PayMemberTypeResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPreCreateTradeOrderReq = {

            data?:  PreCreateTradeOrderReq

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultQueryBusinessAccountResp = {

            data?:  QueryBusinessAccountResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultQueryBusinessEarningsSumMoneyResp = {

            data?:  QueryBusinessEarningsSumMoneyResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultQueryOpenIdsByMemberIdsResp = {

            data?:  QueryOpenIdsByMemberIdsResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultQueryPayStatusResp = {

            data?:  QueryPayStatusResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultRedPackResp = {

            data?:  RedPackResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultRedeemPayMemberResp = {

            data?:  RedeemPayMemberResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultShoppingSettlementResp = {

            data?:  ShoppingSettlementResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultTransactionPaymentResp = {

            data?:  TransactionPaymentResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultUseCouponResp = {

            data?:  UseCouponResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultWalletRechargeReq = {

            data?:  WalletRechargeReq

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultWechatMiniProgramLoginInfoResp = {

            data?:  WechatMiniProgramLoginInfoResp

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultboolean = {

            data?:   boolean

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultint = {

            data?:   number

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultlong = {

            data?:   number

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultmember_relation = {

            data?:  member_relation

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultobject = {

            data?:   any

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultstring = {

            data?:   string

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResult获取离线会员码响应 = {

            data?:  获取离线会员码响应

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResult解析离线会员码响应 = {

            data?:  解析离线会员码响应

            errorMsg?:   string

            resultCode:   number

        }

        type AppInfoReq = {

            address?:   string

            client?:   string

            deviceId?:   string

            deviceTime?:   string

            ip?:   string

            ipLegal?:   boolean

            latitude?:   number

            longitude?:   number

            unionId?:   string

            verCode?:   string

        }

        type BasicMemberEquityMonthListInfo = {

            amount?:   number

            couponTypeId?:   number

            equityCode?:   number

            equityType?:   string

            limitLevelIds?:   string

            receiveType?:   string

            relateCode?:   string

            setPackageValue?:   string

            typeCode?:   number

        }

        type BatchQueryInBlackWhiteReq = {

            batchQueryCsExecutor?:  ExecutorCompletionServiceApiResultListlong

            batchQueryPool?:  ThreadPoolExecutor

            batchSize?:   number

            bizHead?:  HeadReq

            controlType?:   string

            listType?:   string

            memberIds?:    Array < string >

            queryTime?:   string

        }

        type BatchQueryInBlackWhiteResp = {

            inListTypeMemberIds?:    Array < number >

            invalidMemberIds?:    Array < string >

            notInListTypeMemberIds?:    Array < number >

        }

        type BindingCouponReq = {

            couponCode?:   string

            head?:  HeadReq

            identity?:   string

            memberId?:   number

        }

        type BlackWhiteList = {

            channel?:   string

            channelValue?:   string

            controlType?:   number

            createTime?:  Timestamp

            deleteChannel?:   string

            deleteChannelValue?:   string

            deleteOperatorCode?:   string

            deleteOperatorName?:   string

            deleteTime?:  Timestamp

            identity?:   string

            identityScene?:   string

            invalidTime?:  Timestamp

            listId?:   number

            listStatus?:   string

            listType?:   string

            memberId?:   number

            modifyTime?:  Timestamp

            operatorCode?:   string

            operatorName?:   string

            remark?:   string

            source?:   string

            validTime?:  Timestamp

        }

        type BlackWhiteListReq = {

            beAddedTime?:   number

            beDeletedTime?:   number

            channel?:   string

            channelValue?:   string

            controlType:   number

            createTime?:  Timestamp

            deleteTime?:  Timestamp

            identity?:   string

            identityScene?:   string

            invalidTime?:  Timestamp

            listId?:   number

            listStatus?:   string

            listType:   string

            memberId?:   number

            modifyTime?:  Timestamp

            operatorCode?:   string

            operatorName?:   string

            remark?:   string

            source?:   string

            validTime?:  Timestamp

        }

        type BocmPayBaseDTO = {

            consoleNum?:   string

            orderNo?:   string

            payTime?:   string

        }

        type BocmPayDTO = {

            consoleNum?:   string

            noPreferentialAmount?:   number

            orderNo?:   string

            payAmount?:   number

            payTime?:   string

            qrCode?:   string

        }

        type BocmRefundBaseDTO = {

            consoleNum?:   string

            refundNo?:   string

            refundTime?:   string

        }

        type BocmRefundDTO = {

            consoleNum?:   string

            orderNo?:   string

            refundAmount?:   number

            refundNo?:   string

            refundTime?:   string

        }

        type BusinessAccountRecord = {

            accountRecordId?:   number

            businessAccountId?:   number

            channelCode?:   string

            channelValue?:   string

            createCode?:   string

            createOp?:   string

            createTime?:   string

            deleteTime?:   string

            modifyAfter?:   number

            modifyBefore?:   number

            modifyType?:   number

            modifyValue?:   number

            orderNum?:   string

            updateTime?:   string

        }

        type BusinessAccountRecordReq = {

            businessAccountId?:   number

            createTimeEnd?:   string

            createTimeStart?:   string

            modifyType?:   number

            orderNum?:   string

        }

        type BusinessEarnings = {

            businessAccountId?:   number

            businessEarningsId?:   number

            channelCode?:   string

            channelValue?:   string

            createTime?:   string

            deleteTime?:   string

            earningsStatus?:   number

            modifyType?:   number

            money?:   number

            orderNum?:   string

            relationId?:   string

            updateTime?:   string

        }

        type BusinessEarningsDetail = {

            businessAccountId?:   number

            businessEarningsId?:   number

            createTime?:   string

            deleteTime?:   string

            earningsCode?:   string

            earningsCount?:   string

            earningsDetailId?:   number

            earningsDetailType?:   number

            earningsName?:   string

            earningsUnit?:   string

            money?:   number

            orderNum?:   string

            updateTime?:   string

        }

        type BusinessInfo = {

            attach:   string

            description:   string

            notifyUrl:   string

            subject:   string

            targetUrl:   string

        }

        type BusinessInfoReq = {

            bizHead?:  HeadDTO

            bizType?:   number

            relationId?:   string

        }

        type BusinessWithdraw = {

            bizHead?:  HeadDTO

            businessAccountId?:   number

            businessWithdrawId?:   number

            createCode?:   string

            createOp?:   string

            createTime?:   string

            deleteTime?:   string

            gatheringCode?:   string

            gatheringNote?:   string

            gatheringOp?:   string

            gatheringPhone?:   string

            gatheringWay?:   number

            opNote?:   string

            orderNum?:   string

            relationId?:   string

            status?:   number

            updateTime?:   string

            withdrawCode?:   string

            withdrawMoney?:   number

            withdrawValue?:   string

        }

        type BusinessWithdrawRecordPageReq = {

            businessAccountId?:   number

            createTimeEnd?:   string

            createTimeStart?:   string

            gatheringWay?:   number

            limit?:   number

            orderNum?:   string

            pageNum?:   number

        }

        type BusinessWithdrawRecordReq = {

            businessAccountId?:   number

            createTimeEnd?:   string

            createTimeStart?:   string

            gatheringWay?:   number

            orderNum?:   string

        }

        type CancelMemberInfoByIdentityResp = {

            cancelChannel?:   string

            cancelNotes?:   string

            cancelOperatorName?:   string

            cancelTime?:   string

            identity:   string

            memberId:   number

        }

        type CancelPaymentReq = {

            payNo:   string

        }

        type CancelPaymentResp = {

            payNo?:   string

            refundInfo?:    Array < CancelRefundInfo >

        }

        type CancelRefundInfo = {

            refundAmount?:   number

            refundChannel?:   string

            refundNo?:   string

        }

        type CheckedActivity = {

            activityCode:   string

            goodIds:    Array < string >

        }

        type CloseTradeOrderReq = {

            tradeNo?:   string

        }

        type CloseTradeOrderResp = {

            closeFlag?:   number

            remark?:   string

            tradeNo?:   string

        }

        type ConsumeDetailCartReq = {

            availableCoupon?:   boolean

            goodsCode?:   string

        }

        type ConsumeStoreReq = {

            memberId?:   number

            storeCode?:   string

        }

        type CostInfo = {

            costAmount?:   number

            costCode?:   string

            standardAmount?:   number

        }

        type CountRelationMemberIdsReqDTO = {

            channel?:   string

            channelValue?:   string

            createTimeEnd?:   string

            createTimeStart?:   string

            currentPage?:   number

            isExpire?:   boolean

            memberId?:   number

            pageSize?:   number

            parentMemberId?:   number

            relationType?:   string

        }

        type CouponBatchExternal = {

            batchNum?:   string

            couponTypeId?:   number

            createTime?:   string

            deleteTime?:   string

            externalBatchId?:   number

            externalBatchNum?:   string

            modifyTime?:   string

            systemCode?:   string

            systemName?:   string

        }

        type CouponBatchMerchant = {

            batchNum?:   string

            businessManager?:   string

            couponTypeId?:   number

            createTime?:   string

            deleteTime?:   string

            id?:   number

            merchantCode?:   string

            merchantName?:   string

            modifyTime?:   string

            salesDiscount?:   string

        }

        type CouponBatchRespDTO = {

            batchNum?:   string

            batchStatus?:   string

            channelSeparation?:   string

            couponCategory?:   string

            couponName?:   string

            couponPattern?:   string

            couponType?:   string

            couponValue?:   number

            couponWay?:   string

            endDateTime?:   string

            expireCount?:   number

            id?:   number

            limitAmount?:   number

            limitChannel?:   string

            receiveCount?:   number

            startDateTime?:   string

            status?:   string

            totalCount?:   number

            useCount?:   number

        }

        type CouponBatchStatusVaryReq = {

            batchNum?:   string

            bizHead?:  HeadReq

            status?:   number

            verifyRebutReason?:   string

            workerCode?:   string

        }

        type CouponBatchTemplate = {

            auditLevel?:   number

            batchNum?:   string

            couponName?:   string

            couponPattern?:   number

            couponTypeId?:   number

            createTime?:   string

            deleteTime?:   string

            delivery?:   number

            expireCount?:   number

            invoice?:   number

            isAheadCreate?:   number

            isCash?:   number

            isOverlying?:   number

            isReGifted?:   number

            isSplit?:   number

            lastOperator?:   string

            modifyTime?:   string

            personUseTimeLimit?:   number

            receiveCount?:   number

            status?:   number

            stopReason?:   string

            stopTime?:   string

            stopper?:   string

            totalCount?:   number

            useCount?:   number

            useRule?:   number

            verifier?:   string

            verifyRebutReason?:   string

            verifyTime?:   string

            vipFlag?:   number

        }

        type CouponBatchTemplateReqDTO = {

            auditLevel?:   number

            batchNum?:   string

            couponBatchExternal?:  CouponBatchExternal

            couponBatchMerchants?:  CouponBatchMerchant

            couponName?:   string

            couponPattern?:   number

            couponTypeId?:   number

            createTime?:   string

            deleteTime?:   string

            delivery?:   number

            endTime?:   string

            expireCount?:   number

            feeDept?:   string

            feeJoint?:    Array < FeeJoint >

            invoice?:   number

            isAheadCreate?:   number

            isCash?:   number

            isOverlying?:   number

            isReGifted?:   number

            isSplit?:   number

            lastOperator?:   string

            modifyTime?:   string

            personUseTimeLimit?:   number

            receiveCount?:   number

            startTime?:   string

            status?:   number

            stopReason?:   string

            stopTime?:   string

            stopper?:   string

            totalCount?:   number

            useCount?:   number

            useRule?:   number

            verifier?:   string

            verifyRebutReason?:   string

            verifyTime?:   string

            vipFlag?:   number

        }

        type CouponBuyOrderReq = {

            bizHead?:  HeadReq

            deviceInfo?:  DeviceInfoReq

            goodsList?:    Array < GoodsListReq >

            memberId?:   number

            thirdOrderNo?:   string

        }

        type CouponBuyOrderResp = {

            orderNo?:   string

            tradeNo?:   string

        }

        type CouponBuyOrderRespDTO = {

            afterReceiveTime?:   number

            batchNum?:   string

            beginTime?:   string

            couponName?:   string

            couponType?:   string

            couponValue?:   number

            couponWay?:   string

            endTime?:   string

            feeDept?:    Array < string >

            limitAmount?:   number

            relativeExpireTime?:   string

            remainingCount?:   number

            useChannel?:   string

        }

        type CouponInfo = {

            couponCode?:   string

            reasonCode?:   string

        }

        type CouponOrderDetailResp = {

            code?:   string

            count?:   number

            createTime?:  Timestamp

            deleteTime?:  Timestamp

            detailId?:   number

            memberId?:   number

            memberNum?:   number

            orderNo?:   string

            status?:   string

            unitPrice?:   number

            updateTime?:  Timestamp

        }

        type CouponOrderResp = {

            amount?:   number

            channelNum?:   string

            consoleNo?:   string

            createTime?:  Timestamp

            deleteTime?:  Timestamp

            ipAddress?:   string

            memberId?:   number

            memberNum?:   number

            orderNo?:   string

            orderStatus?:   string

            recordId?:   number

            thirdOrderNo?:   string

            tradeNo?:   string

            updateTime?:  Timestamp

        }

        type CouponRespDTO = {

            activityType?:   string

            afterReceiveTime?:   number

            batchNum?:   string

            channelLimits?:    Array < CouponTypeChannel >

            couponCode?:   string

            couponDomain?:   number

            couponName?:   string

            couponType?:   number

            couponTypeCode?:   string

            couponValue?:   number

            createEndTime?:   string

            createStartTime?:   string

            createTime?:   string

            creator?:   string

            deleteTime?:   string

            description?:   string

            endDateTime?:   string

            goodsCategoryLimits?:    Array < CouponTypeGoodsCategoryLimit >

            goodsLimits?:    Array < CouponTypeGoodsLimit >

            goodsjoinFlag?:   number

            id?:   number

            isCash?:   number

            lastOperator?:   string

            limitAmount?:   number

            maxCouponQuantity?:   number

            maxCouponValue?:   number

            merchantName?:   string

            modifyTime?:   string

            orgCode?:   string

            relativeExpireTime?:   number

            shopJoinFlag?:   number

            startDateTime?:   string

            status?:   number

            stopReason?:   string

            stopTime?:   string

            stopper?:   string

            storeGroupLimits?:    Array < CouponTypeStoreGroupLimit >

            storeLimits?:    Array < CouponTypeStoreLimit >

        }

        type CouponRuleReq = {

            batchNum?:   string

            couponNum?:   string

        }

        type CouponSearchReq = {

            balance?:   number

            batchNum?:   string

            beginTime?:   string

            couponCode?:   string

            couponType?:   number

            couponTypeId?:   number

            couponTypeName?:   string

            createTime?:   string

            deleteTime?:   string

            expireTime?:   string

            gainChannel?:   string

            modifyTime?:   string

            pageNum?:   number

            pageSize?:   number

            receiveTime?:   string

            receiver?:   string

            satisfyAmount?:   number

            status?:   number

            stopReason?:   string

            stopTime?:   string

            stopper?:   string

            useChannel?:   string

            useTime?:   string

        }

        type CouponTemplateReq = {

            activityType?:   string

            afterReceiveTime?:   number

            batchNum?:   string

            bizHead?:  HeadReq

            channelLimits?:    Array < CouponTypeChannel >

            couponDomain?:   number

            couponName?:   string

            couponType?:   number

            couponTypeChannels?:    Array < CouponTypeChannel >

            couponTypeCode?:   string

            couponTypeGoodsCategoryLimits?:    Array < CouponTypeGoodsCategoryLimit >

            couponTypeStoreGroupLimits?:    Array < CouponTypeStoreGroupLimit >

            couponValue?:   number

            createEndTime?:   string

            createStartTime?:   string

            createTime?:   string

            creator?:   string

            deleteTime?:   string

            description?:   string

            endDateTime?:   string

            goodsCategoryLimits?:    Array < CouponTypeGoodsCategoryLimit >

            goodsLimits?:    Array < CouponTypeGoodsLimit >

            goodsjoinFlag?:   number

            id?:   number

            isCash?:   number

            lastOperator?:   string

            limitAmount?:   number

            maxCouponQuantity?:   number

            maxCouponValue?:   number

            merchantName?:   string

            modifyTime?:   string

            orgCode?:   string

            relativeExpireTime?:   number

            shopJoinFlag?:   number

            startDateTime?:   string

            status?:   number

            stopReason?:   string

            stopTime?:   string

            stopper?:   string

            storeGroupLimits?:    Array < CouponTypeStoreGroupLimit >

            storeLimits?:    Array < CouponTypeStoreLimit >

            updateTime?:   string

        }

        type CouponTypeChannel = {

            channelNo?:   string

            couponTypeId?:   number

            createTime?:   string

            deleteTime?:   string

            modifyTime?:   string

            type?:   number

        }

        type CouponTypeGoodsCategoryLimit = {

            categoryCode?:   string

            categoryName?:   string

            couponTypeId?:   number

            createTime?:   string

            deleteTime?:   string

            modifyTime?:   string

        }

        type CouponTypeGoodsLimit = {

            couponTypeId?:   number

            createTime?:   string

            deleteTime?:   string

            goodsCode?:   string

            goodsName?:   string

            modifyTime?:   string

            price?:   number

            quantity?:   number

        }

        type CouponTypeStoreGroupLimit = {

            couponTypeId?:   number

            createTime?:   string

            deleteTime?:   string

            groupCode?:   string

            groupId?:   string

            groupName?:   string

            modifyTime?:   string

        }

        type CouponTypeStoreLimit = {

            couponTypeId?:   number

            createTime?:   string

            deleteTime?:   string

            modifyTime?:   string

            storeCode?:   string

            storeName?:   string

        }

        type CouponTypeTemplate = {

            activityType?:   string

            afterReceiveTime?:   number

            couponDomain?:   number

            couponName?:   string

            couponType?:   number

            couponTypeCode?:   string

            couponValue?:   number

            createTime?:   string

            creator?:   string

            deleteTime?:   string

            description?:   string

            endDateTime?:   string

            goodsjoinFlag?:   number

            id?:   number

            isCash?:   number

            lastOperator?:   string

            limitAmount?:   number

            maxCouponQuantity?:   number

            maxCouponValue?:   number

            merchantName?:   string

            modifyTime?:   string

            orgCode?:   string

            relativeExpireTime?:   number

            shopJoinFlag?:   number

            startDateTime?:   string

            status?:   number

            stopReason?:   string

            stopTime?:   string

            stopper?:   string

        }

        type CouponTypeUpdateReq = {

            activityType?:   string

            afterReceiveTime?:   number

            batchNum?:   string

            bizHead?:  HeadReq

            channelLimits?:    Array < CouponTypeChannel >

            couponDomain?:   number

            couponName?:   string

            couponType?:   number

            couponValue?:   number

            createEndTime?:   string

            createStartTime?:   string

            createTime?:   string

            creator?:   string

            description?:   string

            endDateTime?:   string

            goodsCategoryLimits?:    Array < CouponTypeGoodsCategoryLimit >

            goodsLimits?:    Array < CouponTypeGoodsLimit >

            goodsjoinFlag?:   number

            id?:   number

            isCash?:   number

            lastOperator?:   string

            limitAmount?:   number

            maxCouponQuantity?:   number

            maxCouponValue?:   number

            orgCode?:   string

            relativeExpireTime?:   number

            shopJoinFlag?:   number

            startDateTime?:   string

            status?:   number

            stopReason?:   string

            stopTime?:   string

            stopper?:   string

            storeGroupLimits?:    Array < CouponTypeStoreGroupLimit >

            storeLimits?:    Array < CouponTypeStoreLimit >

            updateTime?:   string

        }

        type CouponrFeedBackReqDTO = {

            activityType?:   string

            batchNum?:   string

            beginTime?:   string

            couponWay?:   string

            endTime?:   string

            status?:   string

        }

        type CreateNotifyConfigReqDTO = {

            bizHead?:  HeadReq

            configId?:   number

            description?:   string

            enable?:   boolean

            extraRule?:   string

            memberIdentifyCode?:   number

            memberLevelId?:   number

            needContinue?:   number

            notifyTime?:   string

            notifyType?:   string

            payMemberTypeCode?:   number

            priority?:   number

            template?:   string

            triggerTiming?:   number

            triggerTimingUnit?:   string

            triggerType?:   number

        }

        type CreateRelationReq = {

            bizHead?:  HeadReq

            expireTimeOffset?:   number

            expireTimeUnit?:   string

            memberId?:   number

            needCheckCycle?:   boolean

            needRenew?:   boolean

            parentMemberId?:   number

            relationType?:   string

            relationTypeInRange?:   boolean

        }

        type CreateTradeOrderReq = {

            activityList?:    Array < string >

            costList?:    Array < CostInfo >

            goodsList:    Array < GoodsInfoReq >

            memberId:   string

            merchantChannelCode:   string

            merchantNo:   string

            orderPayableAmount:   number

            orgCode:   string

            preAssetsInfo?:  PreAssetsInfo

            terminalInfo:  TerminalInfo

            uuid?:   string

        }

        type CreateTradeOrderResp = {

            discountAmount?:   number

            goodsTotalAmount?:   number

            payAmount?:   number

            tradeNo?:   string

        }

        type CreateWalletRechargeOrderResp = {

            activityCode?:   string

            activityName?:   string

            discountAmount?:   string

            rechargeAmount?:   number

            rechargeCode?:   number

            rechargeOrderNo?:   string

            tradeNo?:   string

        }

        type DenominationManagerDTO = {

            channel?:   string

            createTime?:   string

            deleteTime?:   string

            denomination?:   number

            denominationId?:   number

            denominationName?:   string

            isDelete?:   string

            status?:   string

            timeInterval?:   any

            updateTime?:   string

        }

        type DenominationReq = {

            activityCode?:   string

            activityName?:   string

            bizHead?:  HeadReq

            channel?:   string

            createTime?:   string

            denomination?:   number

            denominationCode?:   string

            denominationId?:   number

            denominationName?:   string

            isDelete?:   string

            memberId?:   string

            status?:   string

        }

        type DeviceInfoReq = {

            deviceNo?:   string

            ipAddress?:   string

        }

        type DiscountActivityInfo = {

            activityCode?:   string

            activityName?:   string

            activityTotalAmount?:   number

        }

        type DiscountDetail = {

            discountCode?:   string

            saveMoney?:   number

            tradeNum?:   string

            tradeTime?:   string

        }

        type EquityTypeResp = {

            desc?:   string

            key?:   string

        }

        type ExecutorCompletionServiceApiResultListlong = {

        }

        type ExternalMemberQueryRulesUpdateReq = {

            birthday?:    Array < string >

            bizHead?:  HeadReq

            comment?:   string

            name?:    Array < string >

            nick?:    Array < string >

            sex?:    Array < string >

        }

        type FeeJoint = {

            batchNum?:   string

            couponTypeId?:   number

            createTime?:   string

            deleteTime?:   string

            feeDept?:   string

            feeRate?:   number

            modifyTime?:   string

            rateId?:   number

        }

        type GetCouponReq = {

            batchNum?:   string

            dealSerialNum?:   string

            head?:  HeadReq

            identity?:   string

            memberId?:   number

        }

        type GiveActivityInfo = {

            activityCode?:   string

            activityName?:   string

            giveList?:    Array < GiveInfo >

        }

        type GiveInfo = {

            count?:   number

            goodsCode?:   string

            goodsName?:   string

            goodsType?:   string

            unitDescribe?:   string

        }

        type GoodsInfoReq = {

            amount:   number

            goodsId:   string

            price:   number

        }

        type GoodsInfoResp = {

            amount?:   number

            discountTotalPrice?:   number

            goodsId?:   string

            price?:   number

            totalMoney?:   number

        }

        type GoodsListReq = {

            amount?:   number

            code?:   string

            count?:   number

        }

        type HeadDTO = {

            channel?:   string

            channelValue?:   string

            operatorCode?:   string

            operatorName?:   string

        }

        type HeadReq = {

            channel?:   string

            channelValue?:   string

            operatorCode?:   string

            operatorName?:   string

        }

        type IntegralInfo = {

            availableIntegral?:   number

            integralActivityCode?:   string

            money?:   number

        }

        type InvitePayMemberReq = {

            bizHead?:  HeadReq

            endTime?:   string

            memberIdList?:    Array < number >

            startTime?:   string

            taskId?:   string

            typeCode?:   number

        }

        type InvitePayMemberResp = {

            identifyCode?:   number

            memberId:   number

            payMemberEndTime?:   string

            payMemberStartTime?:   string

            payMemberTypeCode?:   number

        }

        type IsRechargeReq = {

            bizHead?:  HeadDTO

            couponCode?:   string

        }

        type IsRechargeResp = {

            coupon?:  CouponRespDTO

            memberNum?:   string

            recharge?:   boolean

        }

        type LinkedNodeMemberLevelLinkedInfoRespDTO = {

            cacheControl?:   boolean

            first?:   boolean

            last?:   boolean

            next?:  LinkedNodeMemberLevelLinkedInfoRespDTO

            pre?:  LinkedNodeMemberLevelLinkedInfoRespDTO

            value?:  MemberLevelLinkedInfoRespDTO

        }

        type LoginVerifyReq = {

            appInfo?:  AppInfoReq

            bizHead?:  HeadReq

            identity?:   string

            verifyCode?:   string

            verifyType?:   string

        }

        type MemberBaseInfo4IdentityResp = {

            birthday?:   string

            identifyCode?:   number

            identity?:   string

            levelId?:   number

            levelName?:   string

            memberId?:   number

            memberName?:   string

            memberNum?:   number

            memberStatus?:   string

            nickName?:   string

            payMemberEndTime?:   string

            payMemberStartTime?:   string

            registerChannel?:   string

            registerTime?:   string

            sex?:   string

            wechatBindStatus?:   string

            wechatBindTime?:   string

        }

        type MemberBaseInfo4MemberIdsResp = {

            invalidMemberIds?:    Array < string >

            memberInfos?:    Array < MemberBaseInfoDTO >

            noMappingMemberIds?:    Array < number >

        }

        type MemberBaseInfo4PhonesResp = {

            invalidPhones?:    Array < string >

            memberInfos?:    Array < MemberBaseInfoDTO >

            noMappingPhones?:    Array < string >

        }

        type MemberBaseInfo4UnionIdResp = {

            invalidUnionIds?:    Array < string >

            memberInfos?:    Array < OpenAccountBinding >

            noMappingUnionId?:    Array < string >

        }

        type MemberBaseInfoByJoinConditionResp = {

            bindingStatus?:   string

            bindingTime?:   string

            identity?:   string

            memberId?:   number

            openId?:   string

            subscribe?:   number

            unBindingTime?:   string

            unionId?:   string

            xcxOpenId?:   string

        }

        type MemberBaseInfoDTO = {

            icon?:   string

            identifyCode?:   number

            identity?:   string

            levelId?:   number

            memberId?:   number

            memberName?:   string

            memberNum?:   number

            memberStatus?:   string

            nickName?:   string

            payMemberEndTime?:   string

            payMemberStartTime?:   string

        }

        type MemberBaseInfoResp = {

            age?:   number

            birthday?:   string

            icon?:   string

            idCardNo?:   string

            identifyCode?:   number

            identity?:   string

            levelId?:   number

            memberId?:   number

            memberName?:   string

            memberNum?:   number

            memberStatus?:   string

            name?:   string

            nick?:   string

            nickName?:   string

            payMemberEndTime?:   string

            payMemberStartTime?:   string

            sex?:   string

        }

        type MemberBehaviorRecord = {

            behaviorType?:   string

            channel?:   string

            channelValue?:   string

            createTime?:   string

            deleteTime?:   string

            deviceId?:   string

            identity?:   string

            ip?:   string

            latitude?:   number

            longitude?:   number

            memberId?:   number

            memberNum?:   number

            recordId?:   number

            status?:   string

            unionId?:   string

            updateTime?:   string

            verCode?:   string

            verifyType?:   string

        }

        type MemberBehaviorRecordQueryReqDTO = {

            behaviorType?:   string

            channel?:   string

            channelValue?:   string

            createTimeEnd?:   string

            createTimeStart?:   string

            currentPage?:   number

            deviceId?:   string

            identity?:   string

            ip?:   string

            latitudeEnd?:   number

            latitudeStart?:   number

            longitudeEnd?:   number

            longitudeStart?:   number

            memberId?:   number

            memberNum?:   number

            pageSize?:   number

            status?:   string

            unionId?:   string

            verCode?:   string

            verifyType?:   string

        }

        type MemberCancelRecordSearchConditionReq = {

            endDateTime?:  Timestamp

            memberId?:   number

            memberNum?:   number

            startDatetime?:  Timestamp

        }

        type MemberCancelReq = {

            bizHead:  HeadReq

            cancelNote?:   string

            memberId:   number

        }

        type MemberCaptchaRegisterReq = {

            appInfo?:  AppInfoReq

            belongBrand?:   string

            bizHead?:  HeadReq

            captcha?:   string

            identity?:   string

            referrerNum?:   string

        }

        type MemberCodeValidReq = {

            bizHead?:  HeadReq

            memberCode?:   string

            memberId?:   number

        }

        type MemberConfigReq = {

            bizHead?:  HeadReq

            configName:   string

            configValue:   string

            memberId:   number

        }

        type MemberCouponDTO = {

            activityType?:   string

            batchNum?:   string

            beginExpireTime?:   string

            bizHead?:  HeadDTO

            couponCode?:   string

            couponStatus?:   string

            endExpireTime?:   string

            isCash?:   string

            memberId?:   number

            orginSortFlag?:   string

            pageNum?:   number

            pageSize?:   number

            sortField?:   any

        }

        type MemberCouponReq = {

            beginExpireTime?:   string

            couponCode?:   string

            endExpireTime?:   string

            head?:  HeadReq

            memberId?:   number

            pageNum?:   number

            pageSize?:   number

            status?:   number

        }

        type MemberEquity = {

            amount?:   number

            couponTypeId?:   number

            createTime?:   string

            deleteTime?:   string

            endTime?:   string

            equityCode?:   number

            equityStatus?:   string

            equityType?:   string

            giftValue?:   string

            isFirstOpen?:   boolean

            modifyTime?:   string

            operatorCode?:   string

            operatorName?:   string

            receiveEndTime?:   number

            receiveStartTime?:   number

            receiveType?:   string

            relateCode?:   string

            remark?:   string

            reviewOperatorCode?:   string

            reviewOperatorName?:   string

            reviewTime?:   string

            setPackageValue?:   string

            startTime?:   string

        }

        type MemberEquityListReq = {

            bizHead?:  HeadReq

            endTime?:   string

            equityCode?:   number

            equityStatus?:   string

            equityType?:   string

            giftValue?:   string

            pageNum?:   number

            pageSize?:   number

            receiveType?:   string

            setPackageValue?:   string

            startTime?:   string

        }

        type MemberEquityMonthListByIdentifyReq = {

            bizHead?:  HeadReq

            equityType?:   string

            limitLevelIds?:    Array < number >

            receiveType?:   string

            typeCode?:   number

        }

        type MemberEquityMonthListByTypeCodeResp = {

            packageValueMap?:   any

            typeCode?:   number

        }

        type MemberEquityReceiveRecordResp = {

            beginTime?:   string

            couponAmount?:   number

            couponCategory?:   string

            couponName?:   string

            couponValue?:   number

            couponWay?:   string

            endTime?:   string

            equityCode?:   number

            equityType?:   string

            expireTime?:   string

            identity?:   string

            integralAmount?:   number

            memberId?:   number

            receiveMonth?:   string

            receiveTime?:   string

            receiveType?:   string

            relateCode?:   string

            satisfyAmount?:   number

            setPackageValue?:   string

            startTime?:   string

            status?:   string

            year?:   string

        }

        type MemberEquityReq = {

            amount?:   number

            bizHead?:  HeadReq

            couponTypeId?:   number

            endTime?:   string

            equityCode?:   number

            equityType?:   string

            giftValue?:   string

            isFirstOpen?:   boolean

            memberLevelList?:    Array < number >

            receiveEndTime?:   number

            receiveStartTime?:   number

            receiveType?:   string

            relateCode?:   string

            setPackageValue?:   string

            startTime?:   string

            typeCodeList?:    Array < number >

        }

        type MemberEquityReviewBatchReq = {

            bizHead?:  HeadReq

            memberEquityReviewReqList?:    Array < MemberEquityReviewReq >

        }

        type MemberEquityReviewReq = {

            bizHead?:  HeadReq

            equityCode?:   number

            equityStatus?:   string

            equityType?:   string

            identity?:   string

            memberId?:   number

            month?:   number

            remark?:   string

            year?:   number

        }

        type MemberGrowthBalanceQueryResp = {

            growthBalance?:   number

            memberId?:   number

            memberNum?:   number

        }

        type MemberGrowthModifyRecordQueryReq = {

            pageable?:  Pageable

            memberId?:   number

            modifyType?:   string

            orderNum?:   string

            modifyTimeStart?:   string

            modifyTimeEnd?:   string

            modifyValueStart?:   number

            modifyValueEnd?:   number

            modifyChannel?:   string

            currentPage?:   number

            pageSize?:   number

        }

        type MemberGrowthModifyRecordQueryResp = {

            growthValueAfter?:   number

            growthValueBefore?:   number

            identity?:   string

            memberId?:   number

            memberNum?:   number

            modifyChannel?:   string

            modifyChannelValue?:   string

            modifyGrowthValue?:   number

            modifyTime?:   string

            modifyType?:   string

            orderNum?:   string

        }

        type MemberGrowthReq = {

            bizHead:  HeadReq

            memberId:   number

            growthChangeValue:   number

            orderNum:   string

            modifyType?:   string

        }

        type MemberIdOpenIdMappingDTO = {

            memberId?:   number

            openId?:   string

        }

        type MemberLevelLinkedInfoRespDTO = {

            createTime?:   string

            defaultLevel?:   string

            growthValueLower?:   number

            growthValueUpper?:   number

            levelDesc?:   string

            levelId?:   number

            levelName?:   string

            linkedNode?:  LinkedNodeMemberLevelLinkedInfoRespDTO

            modifyTime?:   string

            msgChannels?:    Array < number >

            newsInformLower?:   number

            newsInformUpper?:   number

            reduceGrowthValue?:   number

            upgradedBeforeMemberLevel?:  MemberLevelLinkedInfoRespDTO

        }

        type MemberLevelModifyRecordReq = {

            pageable?:  Pageable

            memberId?:   number

            modifyType?:   string

            oldLevelId?:   number

            newLevelId?:   number

            modifyTimeStart?:   string

            modifyTimeEnd?:   string

            currentPage?:   number

            pageSize?:   number

        }

        type MemberLevelModifyRecordResp = {

            growthValueAfter?:   number

            identity?:   string

            memberId?:   number

            memberNum?:   number

            modifyTime?:   string

            modifyType?:   string

            newLevelId?:   number

            oldLevelId?:   number

        }

        type MemberLevelReq = {

            bizHead:  HeadReq

            levelId?:   number

            levelName:   string

            defaultLevel?:   number

            growthValueLower:   number

            growthValueUpper:   number

            reduceGrowthValue:   number

            newsInformLower?:   number

            newsInformUpper?:   number

            levelDesc?:   string

            msgChannels?:    Array < number >

        }

        type MemberLevelResp = {

            defaultLevel?:   string

            expireTime?:   string

            growthValueLower?:   number

            growthValueUpper?:   number

            identity?:   string

            levelDesc?:   string

            levelId?:   number

            levelName?:   string

            memberId?:   number

            memberNum?:   number

            msgChannels?:    Array < number >

            newsInformLower?:   number

            newsInformUpper?:   number

            reduceGrowthValue?:   number

        }

        type MemberLockReq = {

            bizHead?:  HeadReq

            lockNote?:   string

            lockType?:   string

            memberId?:   number

            memberNum?:   number

        }

        type MemberNoCaptchaRegisterBatchReq = {

            appInfo?:  AppInfoReq

            bizHead?:  HeadReq

            memberRegisterBatchList?:    Array < MemberRegisterBatchReq >

        }

        type MemberNoCaptchaRegisterReq = {

            ageGroup?:   number

            appInfo?:  AppInfoReq

            belongBrand?:   string

            birthday?:   string

            bizHead?:  HeadReq

            icon?:   string

            identity?:   string

            incoming?:   number

            job?:   string

            memberType?:   string

            nickName?:   string

            referrerNum?:   string

            sex?:   string

        }

        type MemberNotifyConfig = {

            channel?:   string

            configId?:   number

            createTime?:   string

            deleteTime?:   string

            description?:   string

            enable?:   boolean

            extraRule?:   string

            memberIdentifyCode?:   number

            memberLevelId?:   number

            needContinue?:   number

            notifyTime?:   string

            notifyType?:   string

            operatorCode?:   string

            operatorName?:   string

            payMemberTypeCode?:   number

            priority?:   number

            template?:   string

            triggerTiming?:   number

            triggerTimingUnit?:   string

            triggerType?:   number

            updateTime?:   string

        }

        type MemberPayInfo = {

            payConsoleNum?:   string

            payMoney?:   string

            payStatus?:   string

            payTime?:   string

            payWay?:   string

            thirdSerialNum?:   string

        }

        type MemberRegisterBatchReq = {

            ageGroup?:   number

            belongBrand?:   string

            birthday?:   string

            icon?:   string

            identity?:   string

            incoming?:   number

            job?:   string

            memberType?:   string

            nickName?:   string

            referrerNum?:   string

            sex?:   string

        }

        type MemberTransferDetailResp = {

            applyNote?:   string

            attachment?:   string

            channel?:   string

            channelValue?:   string

            createTime?:  Timestamp

            memberId?:   number

            memberNum?:   number

            modifyTime?:  Timestamp

            newIdentity?:   string

            oldIdentity?:   string

            operatorCode?:   string

            operatorName?:   string

            recordId?:   number

            reviewNote?:   string

            reviewOperatorCode?:   string

            reviewOperatorName?:   string

            reviewTime?:  Timestamp

            status?:   string

        }

        type MemberTransferReq = {

            attachment?:   string

            bizHead?:  HeadReq

            captcha?:   string

            memberId?:   number

            newIdentity?:   string

        }

        type MemberTransferReviewReq = {

            bizHead?:  HeadReq

            formStatus?:   string

            recordIdList?:    Array < number >

            reviewNote?:   string

        }

        type MemberUnLockReq = {

            bizHead?:  HeadReq

            memberId?:   number

            memberNum?:   number

            unlockNote?:   string

        }

        type MemberUpdateReq = {

            ageGroup?:   number

            birthday?:   string

            bizHead?:  HeadReq

            familySize?:   number

            icon?:   string

            incoming?:   number

            job?:   string

            memberId?:   number

            memberName?:   string

            nickName?:   string

            sex?:   string

        }

        type MemberVerifyCancelReq = {

            bizHead?:  HeadReq

            memberId?:   number

        }

        type MemberVerifyReq = {

            bizHead?:  HeadReq

            identityCardNum?:   string

            memberId?:   number

            realName?:   string

        }

        type MemberVisitorAddReq = {

            bizHead:  HeadReq

            phone:   string

            nickName?:   string

            sex?:   string

            birthdate?:   string

            linkedPhone?:   string

            remark?:   string

        }

        type MemberVisitorResp = {

            recordId?:   number

            phone?:   string

            nickName?:   string

            memberFlag?:   number

            sex?:   string

            birthdate?:   string

            linkedPhone?:   string

            remark?:   string

            operatorCode?:   string

            operatorName?:   string

            channel?:   string

            channelValue?:   string

            createTime?:   string

            modifyTime?:   string

        }

        type MutexBatchReq = {

            batchNum?:   string

            mutexId?:   number

        }

        type MutexBatchResp = {

            batchNum?:   string

            beginTime?:   string

            createTime?:   string

            deleteTime?:   string

            description?:   string

            endTime?:   string

            modifyTime?:   string

            mutexId?:   number

            status?:   string

        }

        type OpenAccountBinding = {

            accountType?:   string

            appId?:   string

            bindingTime?:   string

            createTime?:   string

            id?:   number

            memberId?:   number

            openId?:   string

            phone?:   string

            status?:   string

            unbindingTime?:   string

            unionId?:   string

            updateComment?:   string

            updateTime?:   string

        }

        type Pageable = {

            pageNumber?:   number

            pageSize?:   number

            sort?:    Array < string >

        }

        type PageableDTOCouponBatchTemplateReqDTO = {

            dto?:  CouponBatchTemplateReqDTO

            pageable?:  Pageable

        }

        type PageableDTOCouponRespDTO = {

            dto?:  CouponRespDTO

            pageable?:  Pageable

        }

        type PageableDTODenominationManagerDTO = {

            dto?:  DenominationManagerDTO

            pageable?:  Pageable

        }

        type PageableDTOWalletRechargeReq = {

            dto?:  WalletRechargeReq

            pageable?:  Pageable

        }

        type PageBlackWhiteListReq = {

            content?:    Array < BlackWhiteListReq >

            empty?:   boolean

            first?:   boolean

            last?:   boolean

            number?:   number

            numberOfElements?:   number

            pageable?:  Pageable

            size?:   number

            sort?:  Sort

            totalElements?:   number

            totalPages?:   number

        }

        type PageBusinessEarnings = {

            content?:    Array < BusinessEarnings >

            empty?:   boolean

            first?:   boolean

            last?:   boolean

            number?:   number

            numberOfElements?:   number

            pageable?:  Pageable

            size?:   number

            sort?:  Sort

            totalElements?:   number

            totalPages?:   number

        }

        type PageBusinessWithdraw = {

            content?:    Array < BusinessWithdraw >

            empty?:   boolean

            first?:   boolean

            last?:   boolean

            number?:   number

            numberOfElements?:   number

            pageable?:  Pageable

            size?:   number

            sort?:  Sort

            totalElements?:   number

            totalPages?:   number

        }

        type PageCouponBatchTemplate = {

            content?:    Array < CouponBatchTemplate >

            empty?:   boolean

            first?:   boolean

            last?:   boolean

            number?:   number

            numberOfElements?:   number

            pageable?:  Pageable

            size?:   number

            sort?:  Sort

            totalElements?:   number

            totalPages?:   number

        }

        type PageCouponTypeTemplate = {

            content?:    Array < CouponTypeTemplate >

            empty?:   boolean

            first?:   boolean

            last?:   boolean

            number?:   number

            numberOfElements?:   number

            pageable?:  Pageable

            size?:   number

            sort?:  Sort

            totalElements?:   number

            totalPages?:   number

        }

        type PageDenominationManagerDTO = {

            content?:    Array < DenominationManagerDTO >

            empty?:   boolean

            first?:   boolean

            last?:   boolean

            number?:   number

            numberOfElements?:   number

            pageable?:  Pageable

            size?:   number

            sort?:  Sort

            totalElements?:   number

            totalPages?:   number

        }

        type PageMemberBehaviorRecord = {

            content?:    Array < MemberBehaviorRecord >

            empty?:   boolean

            first?:   boolean

            last?:   boolean

            number?:   number

            numberOfElements?:   number

            pageable?:  Pageable

            size?:   number

            sort?:  Sort

            totalElements?:   number

            totalPages?:   number

        }

        type PageMemberEquity = {

            content?:    Array < MemberEquity >

            empty?:   boolean

            first?:   boolean

            last?:   boolean

            number?:   number

            numberOfElements?:   number

            pageable?:  Pageable

            size?:   number

            sort?:  Sort

            totalElements?:   number

            totalPages?:   number

        }

        type PageMemberGrowthModifyRecordQueryResp = {

            content?:    Array < MemberGrowthModifyRecordQueryResp >

            empty?:   boolean

            first?:   boolean

            last?:   boolean

            number?:   number

            numberOfElements?:   number

            pageable?:  Pageable

            size?:   number

            sort?:  Sort

            totalElements?:   number

            totalPages?:   number

        }

        type PageMemberLevelModifyRecordResp = {

            content?:    Array < MemberLevelModifyRecordResp >

            empty?:   boolean

            first?:   boolean

            last?:   boolean

            number?:   number

            numberOfElements?:   number

            pageable?:  Pageable

            size?:   number

            sort?:  Sort

            totalElements?:   number

            totalPages?:   number

        }

        type PageMemberNotifyConfig = {

            content?:    Array < MemberNotifyConfig >

            empty?:   boolean

            first?:   boolean

            last?:   boolean

            number?:   number

            numberOfElements?:   number

            pageable?:  Pageable

            size?:   number

            sort?:  Sort

            totalElements?:   number

            totalPages?:   number

        }

        type PageMemberTransferDetailResp = {

            content?:    Array < MemberTransferDetailResp >

            empty?:   boolean

            first?:   boolean

            last?:   boolean

            number?:   number

            numberOfElements?:   number

            pageable?:  Pageable

            size?:   number

            sort?:  Sort

            totalElements?:   number

            totalPages?:   number

        }

        type PagePayMemberInfoWillExpireResp = {

            content?:    Array < PayMemberInfoWillExpireResp >

            empty?:   boolean

            first?:   boolean

            last?:   boolean

            number?:   number

            numberOfElements?:   number

            pageable?:  Pageable

            size?:   number

            sort?:  Sort

            totalElements?:   number

            totalPages?:   number

        }

        type PagePayMemberQueryRedeemCodeBatchResp = {

            content?:    Array < PayMemberQueryRedeemCodeBatchResp >

            empty?:   boolean

            first?:   boolean

            last?:   boolean

            number?:   number

            numberOfElements?:   number

            pageable?:  Pageable

            size?:   number

            sort?:  Sort

            totalElements?:   number

            totalPages?:   number

        }

        type PagePayMemberQueryRedeemCodeDetailResp = {

            content?:    Array < PayMemberQueryRedeemCodeDetailResp >

            empty?:   boolean

            first?:   boolean

            last?:   boolean

            number?:   number

            numberOfElements?:   number

            pageable?:  Pageable

            size?:   number

            sort?:  Sort

            totalElements?:   number

            totalPages?:   number

        }

        type PagePayMemberSaveMoneyDetail = {

            content?:    Array < PayMemberSaveMoneyDetail >

            empty?:   boolean

            first?:   boolean

            last?:   boolean

            number?:   number

            numberOfElements?:   number

            pageable?:  Pageable

            size?:   number

            sort?:  Sort

            totalElements?:   number

            totalPages?:   number

        }

        type PagePayMemberTypeResp = {

            content?:    Array < PayMemberTypeResp >

            empty?:   boolean

            first?:   boolean

            last?:   boolean

            number?:   number

            numberOfElements?:   number

            pageable?:  Pageable

            size?:   number

            sort?:  Sort

            totalElements?:   number

            totalPages?:   number

        }

        type PageSmsTemplate = {

            content?:    Array < SmsTemplate >

            empty?:   boolean

            first?:   boolean

            last?:   boolean

            number?:   number

            numberOfElements?:   number

            pageable?:  Pageable

            size?:   number

            sort?:  Sort

            totalElements?:   number

            totalPages?:   number

        }

        type PartRefundGoodDTO = {

            refundAmount?:   number

            refundCount?:   number

            refundGoodId?:   string

        }

        type PartRefundReqDTO = {

            bizRefundNo:   string

            refundChannel?:   string

            refundChannelNum:   number

            refundGoodList:    Array < PartRefundGoodDTO >

            tradeNo:   string

            type:   string

        }

        type PartRefundResDTO = {

            refundId?:   number

            refundNo?:   string

        }

        type PayMemberAddRefundReq = {

            assignee?:   string

            bizHead?:  HeadReq

            buyNum?:   string

            complainArea?:   string

            memberId?:   number

            payee?:   string

            refundMoney?:   number

            refundReason?:   string

            refundWay?:   string

            remark?:   string

        }

        type PayMemberBuyOrderReq = {

            bizHead?:  HeadReq

            memberId:   number

        }

        type PayMemberBuyReq = {

            activityCode?:   string

            bizHead?:  HeadReq

            inviteId?:   number

            memberId:   number

            price:   number

            staffNum?:   string

            thirdPartyOrderNum?:   string

            typeCode:   number

        }

        type PayMemberBuyResp = {

            buyNum?:   string

            tradeNum?:   string

        }

        type PayMemberDetailPeriodRespDTO = {

            buyNum?:   string

            memberNum?:   number

            payMemberEndTime?:   string

            payMemberStartTime?:   string

            payMemberType?:  PayMemberType

            price?:   number

            refundMoney?:   number

            refundScenes?:   string

            saveMoney?:   number

            scenes?:   string

        }

        type PayMemberIdentifyResp = {

            createTime?:   string

            description?:   string

            modifyTime?:   string

            statusCode?:   number

            statusName?:   string

        }

        type PayMemberInfoRespDTO = {

            code?:   string

            currentEndTime?:   string

            currentPrice?:   number

            currentStartTime?:   string

            currentTypeCode?:   number

            endTime?:   string

            identifyCode?:   number

            identity?:   string

            inviteTotalCount?:   number

            isExpire?:   boolean

            lastInviteCount?:   number

            memberId?:   number

            memberNum?:   number

            oldIdentifyCode?:   number

            oldTypeTag?:   string

            receiveType?:   string

            saveMoneyCurrentPeriod?:   number

            saveMoneyHistoryTotal?:   number

            setPackageValue?:   string

            startTime?:   string

        }

        type PayMemberInfoWillExpireResp = {

            identifyCode?:   number

            memberId?:   number

        }

        type PayMemberModifyRecord = {

            buyNum?:   string

            channel?:   string

            channelValue?:   string

            createTime?:  Timestamp

            deleteTime?:  Timestamp

            memberId?:   number

            memberLevelId?:   number

            memberNum?:   number

            modifyTime?:  Timestamp

            newTypeCode?:   number

            oldTypeCode?:   number

            recordId?:   number

            statusChange?:   string

        }

        type PayMemberModifyRecordQueryReq = {

            identity?:   string

            pageNum:   number

            pageSize:   number

            statusChange?:   string

        }

        type PayMemberModifyRecordQueryResp = {

            currentPage?:   number

            payMemberModifyRecordList?:    Array < PayMemberModifyRecord >

            totalElements?:   number

            totalPage?:   number

        }

        type PayMemberModifyRefundReq = {

            assignee?:   string

            bizHead?:  HeadReq

            complainArea?:   string

            payee?:   string

            refundMoney?:   number

            refundNum?:   number

            refundReason?:   string

            refundWay?:   string

            remark?:   string

        }

        type PayMemberPeriodInfoResp = {

            automaticRenew?:   boolean

            buyNum?:   string

            differenceRefund?:   boolean

            endTime?:   string

            inviteCount?:   number

            memberId?:   number

            payMemberStatusName?:   string

            payMemberTypeCode?:   string

            payMemberTypeName?:   string

            periodType?:   string

            startTime?:   string

        }

        type PayMemberPeriodResp = {

            createTime?:   string

            description?:   string

            modifyTime?:   string

            periodCode?:   number

            periodDay?:   number

            periodName?:   string

            periodUnit?:   string

        }

        type PayMemberPeriodSetPackageValueReq = {

            memberId?:   number

            startTime?:   string

        }

        type PayMemberPeriodSetPackageValueResp = {

            createTime?:   string

            endTime?:   string

            period?:  PayMemberPeriodResp

            receiveType?:   string

            recordId?:   number

            setPackageValue?:   string

            startTime?:   string

            typeCode?:   number

            typeTag?:   string

        }

        type PayMemberQueryRedeemCodeBatchReq = {

            pageable?:  Pageable

            bizHead?:  HeadReq

            typeCode?:   number

            batchStatus?:   string

            exchangePurpose?:   number

            batchCode?:   string

            validTimeStart?:   string

            validTimeEnd?:   string

            createTimeStart?:   string

            createTimeEnd?:   string

            redeemCode?:   string

            currentPage?:   number

            pageSize?:   number

        }

        type PayMemberQueryRedeemCodeBatchResp = {

            amount?:   number

            applicantName?:   string

            batchCode?:   string

            batchName?:   string

            batchStatus?:   number

            createTime?:   string

            customerFullName?:   string

            exchangePurpose?:   number

            faceValue?:   number

            invalidTime?:   string

            modifyTime?:   string

            operatorCode?:   string

            operatorName?:   string

            remark?:   string

            reviewOperatorCode?:   string

            reviewOperatorName?:   string

            reviewReason?:   string

            reviewTime?:   string

            salesmanName?:   string

            settlementPrice?:   number

            totalCount?:   number

            typeCode?:   number

            typeName?:   string

            usedCount?:   number

            validTime?:   string

        }

        type PayMemberQueryRedeemCodeDetailReq = {

            pageable?:  Pageable

            bizHead?:  HeadReq

            typeCode?:   number

            batchCode?:   string

            useTimeStart?:   string

            useTimeEnd?:   string

            createTimeStart?:   string

            createTimeEnd?:   string

            redeemStatus?:   string

            redeemCode?:   string

            userPhone?:   string

            currentPage?:   number

            pageSize?:   number

        }

        type PayMemberQueryRedeemCodeDetailResp = {

            amount?:   number

            applicantName?:   string

            batchCode?:   string

            batchName?:   string

            batchStatus?:   number

            createTime?:   string

            customerFullName?:   string

            exchangePurpose?:   number

            faceValue?:   number

            invalidTime?:   string

            modifyTime?:   string

            operatorName?:   string

            remark?:   string

            reviewOperatorCode?:   string

            reviewOperatorName?:   string

            reviewReason?:   string

            reviewTime?:   string

            salesmanName?:   string

            settlementPrice?:   number

            typeCode?:   number

            typeName?:   string

            useTime?:   string

            validTime?:   string

            redeemStatus?:   string

            redeemCode?:   string

            memberId?:   number

            userPhone?:   string

        }

        type PayMemberQueryRedeemCodeResp = {

            batchCode?:   string

            createTime?:   string

            memberId?:   number

            modifyTime?:   string

            operatorCode?:   string

            operatorName?:   string

            receiveChannel?:   string

            receiveTime?:   string

            recordId?:   number

            redeemCode?:   string

            redeemStatus?:   number

            remark?:   string

            typeCode?:   number

            useTime?:   string

        }

        type PayMemberQuerySaveMoneyDetailReq = {

            currentPage?:   number

            endTime?:   string

            isEffective?:   boolean

            memberId:   number

            memberNum:   number

            pageSize?:   number

            startTime?:   string

        }

        type PayMemberReViewRedeemCodeBatchReq = {

            bizHead:  HeadReq

            batchCodes:    Array < string >

            reviewPass:   string

            reviewReason?:   string

        }

        type PayMemberRecord = {

            activityCode?:   string

            buyNum?:   string

            buyStatus?:   string

            channel?:   string

            channelValue?:   string

            code?:   string

            createTime?:  Timestamp

            deleteTime?:  Timestamp

            discountMoney?:   number

            inviteId?:   number

            memberId?:   number

            memberNum?:   number

            modifyTime?:  Timestamp

            price?:   number

            recordId?:   number

            scenes?:   string

            staffNum?:   string

            thirdPartyOrderNum?:   string

            tradeNum?:   string

            typeCode?:   number

        }

        type PayMemberRedeemCodeBatchFuzzyResp = {

            batchCode?:   string

            batchName:   string

        }

        type PayMemberRedeemCodeBatchReq = {

            applicantName?:   string

            customerFullName?:   string

            exchangePurpose?:   number

            salesmanName?:   string

            settlementPrice?:   number

            bizHead:  HeadReq

            batchCode?:   string

            batchName:   string

            typeCode:   number

            amount:   number

            validTime:   string

            invalidTime:   string

        }

        type PayMemberRefundRecordInfo = {

            assignee?:   string

            buyNum?:   string

            complainArea?:   string

            createTime?:   string

            identity?:   string

            memberId?:   number

            modifyTime?:   string

            operatorCode?:   string

            operatorName?:   string

            payMemberType?:   number

            payee?:   string

            refundMoney?:   number

            refundNum?:   number

            refundReason?:   string

            refundWay?:   string

            remark?:   string

            reviewOperatorCode?:   string

            reviewOperatorName?:   string

            reviewReason?:   string

            reviewStatus?:   string

            reviewTime?:   string

            scenes?:   string

        }

        type PayMemberRefundRecordInfoResp = {

            currentPage?:   number

            payMemberRefundRecordInfoList?:    Array < PayMemberRefundRecordInfo >

            totalElements?:   number

            totalPage?:   number

        }

        type PayMemberRefundRecordQueryReq = {

            createTimeEnd?:   string

            createTimeStart?:   string

            identity?:   string

            memberId?:   number

            pageNum:   number

            pageSize:   number

            refundReason?:   string

            reviewStatus?:   string

            reviewTimeEnd?:   string

            reviewTimeStart?:   string

        }

        type PayMemberReviewRefundReq = {

            bizHead?:  HeadReq

            refundNum?:   number

            reviewReason?:   string

            reviewStatus?:   boolean

        }

        type PayMemberSaleableReq = {

            bizHead?:  HeadReq

            memberId?:   number

        }

        type PayMemberSaleableResp = {

            identity?:  PayMemberIdentifyResp

            period?:  PayMemberPeriodResp

            typeCode?:   number

            typeName?:   string

        }

        type PayMemberSaveDetailMoneyRespDTO = {

            discountDetail?:  DiscountDetail

            discountMoney?:   number

            discountType?:   string

            memberId?:   number

            scenesType?:   string

        }

        type PayMemberSaveMoneyDetail = {

            businessNum?:   string

            channel?:   string

            channelValue?:   string

            createTime?:  Timestamp

            deleteTime?:  Timestamp

            discountType?:   string

            identifyCode?:   number

            memberId?:   number

            memberNum?:   number

            modifyTime?:  Timestamp

            ordinaryDiscountMoney?:   number

            recordId?:   number

            relateCode?:   string

            saveMoney?:   number

            saveTime?:   string

            scenesType?:   string

            tradeNum?:   string

            tradeTime?:   string

            vipDiscountMoney?:   number

        }

        type PayMemberSaveTotalMoneyRespDTO = {

            currentPayMemberBeginTime?:   string

            currentPayMemberEndTime?:   string

            currentPeriodSaveMoney?:   number

            firstGainPayMemberTime?:   string

            gainPayMemberEndTime?:   string

            identity?:   string

            memberId?:   number

            memberNum?:   number

            payMemberStatus?:   number

            totalSaveMoney?:   number

        }

        type PayMemberSetPackageValueReq = {

            identity?:   string

            memberId?:   number

            receiveType?:   string

            setPackageValue?:   string

            typeTag?:   string

        }

        type PayMemberStopRedeemCodeBatchReq = {

            bizHead:  HeadReq

            batchCodes:    Array < string >

            stopReason?:   string

        }

        type PayMemberStopRedeemCodeReq = {

            redeemCodes:    Array < string >

            bizHead:  HeadReq

            stopReason:   string

        }

        type PayMemberType = {

            automaticRenew?:   number

            callNotifyRenew?:   number

            createTime?:  Timestamp

            deleteTime?:  Timestamp

            differenceRefund?:   number

            identifyCode?:   number

            inviteCount?:   number

            inviteType?:   number

            modifyTime?:  Timestamp

            notifySaveMoneyLimit?:   number

            operatorCode?:   string

            operatorName?:   string

            periodCode?:   number

            price?:   number

            shelvesStatus?:   string

            typeCode?:   number

            typeName?:   string

            typeTag?:   string

        }

        type PayMemberTypeChannelConfig = {

            channel?:   string

            configId?:   number

            createTime?:   string

            deleteTime?:   string

            modifyTime?:   string

            operatorCode?:   string

            operatorName?:   string

            shelvesStatus?:   string

            typeCode?:   number

        }

        type PayMemberTypeReq = {

            automaticRenew?:   number

            bizHead?:  HeadReq

            callNotifyRenew?:   number

            differenceRefund?:   number

            identifyCode?:   number

            inviteCount?:   number

            inviteType?:   number

            notifySaveMoneyLimit?:   number

            periodCode?:   number

            price?:   number

            typeName?:   string

            typeTag?:   string

        }

        type PayMemberTypeResp = {

            automaticRenew?:   number

            callNotifyRenew?:   number

            channelConfigs?:    Array < PayMemberTypeChannelConfig >

            differenceRefund?:   number

            discountMoney?:   number

            inviteCount?:   number

            inviteType?:   number

            notifySaveMoneyLimit?:   number

            payMemberIdentifyCode?:   number

            payMemberPeriodCode?:   number

            payMemberTypeCode?:   number

            payMemberTypeName?:   string

            payMemberTypeTag?:   string

            price?:   number

            shelvesStatus?:   string

        }

        type PayMemberTypeSearchConditionReqDTO = {

            currentPage?:   number

            pageSize?:   number

        }

        type PayMemberTypeShelvesReq = {

            bizHead?:  HeadReq

            channels?:    Array < string >

            shelvesStatus?:   string

            shelvesStatusInRange?:   boolean

            typeCode?:   number

        }

        type PayMemberTypeUpdateReq = {

            automaticRenew?:   number

            bizHead?:  HeadReq

            callNotifyRenew?:   number

            differenceRefund?:   number

            inviteCount?:   number

            inviteType?:   number

            notifySaveMoneyLimit?:   number

            price?:   number

            typeCode?:   number

            typeName?:   string

            typeTag?:   string

        }

        type PayMemberWillExpireReq = {

            expireDay?:   number

            onlyQueryIdentifyCode?:   boolean

            pageable?:  Pageable

            queryWithinExpireDays?:   boolean

            currentPage?:   number

            pageSize?:   number

        }

        type PaymentInfoReq = {

            authCode?:   string

            canUseWechatCoupon?:   boolean

            date?:   string

            installmentNum?:   string

            openID?:   string

            paymentAccount?:   string

            paymentAmount:   number

            paymentChannel:   string

            paymentWay?:   string

            preProduct?:   string

            verifyInfo?:  VerifyInfo

        }

        type PaymentInfoResp = {

            payDiscountAmount?:   number

            paymentChannel?:   string

            paymentPrice?:   number

        }

        type PreActivityInfoReq = {

            activityCode:   string

            goodsList:    Array < string >

        }

        type PreAssetsInfo = {

            couponList?:    Array < string >

            integralDeduction?:   number

        }

        type PreBlackCheckInfo = {

            clientIp?:   string

            customerAddr?:   string

            customerName?:   string

            customerPhone?:   string

            deviceNo?:   string

            latitude?:   string

            longitude?:   string

        }

        type PreCouponInfo = {

            couponId?:   string

            discountAmount?:   number

        }

        type PreCreateTradeOrderReq = {

            assetInfo?:  PreAssetsInfo

            blackCheckInfo?:  PreBlackCheckInfo

            brand:   string

            channelNo:   number

            checkedActivityList?:    Array < PreActivityInfoReq >

            costList?:    Array < CostInfo >

            extensionInfo?:   string

            goodsList:    Array < PreGoodInfoReq >

            memberId:   number

            memberIdentifyType:   string

            merchantId?:   string

            orderAmount:   number

            payAmount:   number

            preCreateTradeNo?:   string

            shopNo:   string

            tradeScene:   string

            tradeType:   string

            uuid?:   string

        }

        type PreGoodInfoReq = {

            goodCode:   string

            quantity:   number

            serviceFeeList?:    Array < PreServiceFee >

            unitPrice:   number

        }

        type PreServiceFee = {

            goodCode:   string

            quantity:   number

            unitPrice:   number

        }

        type PullBlackWhiteListReq = {

            listIds?:    Array < number >

        }

        type QueryBlackWhiteListReq = {

            controlType?:   number

            createTimeEnd?:  Timestamp

            createTimeStart?:  Timestamp

            currentPage?:   number

            identity?:   string

            identityScene?:   string

            invalidTimeEnd?:  Timestamp

            invalidTimeStart?:  Timestamp

            isDelete?:   boolean

            listStatus?:   string

            listType?:   string

            memberId?:   number

            pageSize?:   number

            source?:   string

            validTimeEnd?:  Timestamp

            validTimeStart?:  Timestamp

        }

        type QueryBlackWhiteReq = {

            controlType?:   number

            identity?:   string

            identityScene?:   string

            listType?:   string

            memberId?:   number

        }

        type QueryBusinessAccountResp = {

            balance?:   number

            bizAccState?:   number

            bizType?:   number

            businessAccountId?:   number

            businessInfoId?:   number

            expectBalance?:   number

            freezeBalance?:   number

            relationId?:   string

            withdrawTotalBalance?:   number

        }

        type QueryBusinessEarningsSumMoneyResp = {

            money?:   number

            pageContent?:  PageBusinessEarnings

        }

        type QueryBusinessInfoDmReq = {

            bizType?:   number

            businessAccountId?:   number

        }

        type QueryCouponExistDto = {

            batchNumList?:    Array < string >

            memberId?:   number

        }

        type QueryCouponRuleListReq = {

            batchNumList?:    Array < string >

        }

        type QueryEarningsBatchOrderReq = {

            bizType?:   number

            businessAccountId?:   number

            orderNumList?:    Array < string >

            relationId?:   string

        }

        type QueryEarningsDetailReIdReq = {

            bizType?:   number

            businessAccountId?:   number

            createTimeEnd?:   string

            createTimeStart?:   string

            earningsStatusList?:    Array < number >

            limit?:   number

            modifyTypeList?:    Array < number >

            orderNum?:   string

            pageNum?:   number

            relationId?:   string

        }

        type QueryEarningsDetailReq = {

            businessAccountId?:   number

            createTimeEnd?:   string

            createTimeStart?:   string

            earningsStatus?:   number

            orderNum?:   string

        }

        type QueryMemberBaseInfoByJoinConditionReq = {

            selectCondition?:   string

            selectExternalChannel?:   number

            selectType?:   number

        }

        type QueryMemberEquityListReq = {

            equityType?:   string

            identity?:   string

            memberId?:   number

            receiveType?:   string

            setPackageValue?:   string

            typeCode?:   number

        }

        type QueryMemberEquityListResp = {

            couponAmount?:   number

            couponCategory?:   string

            couponTypeId?:   number

            endTime?:   string

            equityCode?:   number

            equityType?:   string

            identity?:   string

            memberId?:   number

            relateCode?:   string

            setPackageValue?:   string

            startTime?:   string

        }

        type QueryMemberLockListReq = {

            bizHead?:  HeadReq

            lockEndAt?:   string

            lockStartAt?:   string

            lockType?:   string

            memberId?:   number

            memberNum?:   number

            unlockEndAt?:   string

            unlockStartAt?:   string

        }

        type QueryMemberTransferRecordReq = {

            createEndTime?:   string

            createStartTime?:   string

            currentPage?:   number

            formStatus?:   string

            memberId?:   number

            newIdentity?:   string

            oldIdentity?:   string

            pageSize?:   number

        }

        type QueryNotifyConfigReqDTO = {

            channel?:   string

            currentPage?:   number

            enable?:   boolean

            isDeleted?:   boolean

            memberIdentifyCode?:   number

            memberLevelId?:   number

            needContinue?:   number

            notifyTime?:   string

            notifyType?:   string

            operatorCode?:   string

            operatorName?:   string

            pageSize?:   number

            payMemberTypeCode?:   number

            triggerTiming?:   number

            triggerTimingUnit?:   string

            triggerType?:   number

        }

        type QueryOpenIdsByMemberIdsReq = {

            memberIds:    Array < string >

            openIdType:   string

        }

        type QueryOpenIdsByMemberIdsResp = {

            invalidMemberIds?:    Array < string >

            noMappingMemberIds?:    Array < number >

            openIdMappings?:    Array < MemberIdOpenIdMappingDTO >

            openIdType?:   string

        }

        type QueryPayStatusResp = {

            payNo?:   string

            payStatus?:   string

        }

        type QueryRedPackRecordReq = {

            channel?:   string

            mchNo?:   string

            platNo?:   string

            tradeNo?:   string

            type?:   string

        }

        type ReceiveMarketingPayMemberReq = {

            bizHead?:  HeadReq

            memberId?:   number

        }

        type ReceiveMemberEquityReq = {

            bizHead?:  HeadReq

            endTime?:   string

            equityType?:   string

            memberId?:   number

            periodCode?:   number

            setPackageValue?:   string

            startTime?:   string

            updateSetPackageValue?:   boolean

        }

        type RedPackResp = {

            failMsg?:   string

            isSuccess?:   boolean

            mchNo?:   string

            platNo?:   string

            receivedTime?:   string

            refundTime?:   string

            sendTime?:   string

            status?:   string

            statusDes?:   string

            tradeNo?:   string

        }

        type RedeemPayMemberResp = {

            bizHead?:  HeadReq

            code?:   string

            memberId?:   number

            payMemberEndTime?:   string

            payMemberIdentify?:  PayMemberIdentifyResp

            payMemberStartTime?:   string

            payMemberTypeCode?:   number

        }

        type RejectedExecutionHandler = {

        }

        type ReleaseBehaviorLimitReq = {

            identity?:   string

            limitRuleKey?:   string

        }

        type RemoveBlackWhiteListReq = {

            bizHead?:  HeadReq

            listId?:    Array < number >

        }

        type Runnable = {

        }

        type SendCaptchaReq = {

            bizHead?:  HeadReq

            receiveAddr?:   string

            receiveWay?:   string

        }

        type SendHbReq = {

            accountCode?:   string

            channel?:   string

            tradeNo?:   string

            type?:   string

            wxRedPackInfo?:  WxRedPackInfo

        }

        type ShoppingSettlementReq = {

            channelNo?:   string

            checkedActivityList?:    Array < CheckedActivity >

            costList?:    Array < CostInfo >

            goodsList:    Array < GoodsInfoReq >

            memberId:   number

            memberIdentifyType?:   string

            model?:   string

            scene?:   string

            shopNo?:   string

        }

        type ShoppingSettlementResp = {

            availableActivity?:  ActivityInfoResp

            couponInfo?:  CouponInfo

            goodsList?:    Array < GoodsInfoResp >

            integralInfo?:  IntegralInfo

            uuid?:   string

        }

        type SmsTemplate = {

            channel?:   string

            createTime?:   string

            deleteTime?:   string

            modifyTime?:   string

            operatorCode?:   string

            operatorName?:   string

            remark?:   string

            smsToken?:   string

            templateContent?:   string

            templateId?:   number

            templateName?:   string

            templateType?:   number

        }

        type Sort = {

            empty?:   boolean

            sorted?:   boolean

            unsorted?:   boolean

        }

        type TempMemberInfoResp = {

            icon?:   string

            identifyCode?:   number

            identity?:   string

            levelId?:   number

            levelName?:   string

            memberId?:   number

            nickName?:   string

            realName?:   string

            sex?:   string

            status?:   string

        }

        type TerminalInfo = {

            eqptCode?:   string

            ip?:   string

        }

        type ThreadFactory = {

        }

        type ThreadPoolExecutor = {

            activeCount?:   number

            completedTaskCount?:   number

            corePoolSize?:   number

            largestPoolSize?:   number

            maximumPoolSize?:   number

            poolSize?:   number

            queue?:    Array < Runnable >

            rejectedExecutionHandler?:  RejectedExecutionHandler

            shutdown?:   boolean

            taskCount?:   number

            terminated?:   boolean

            terminating?:   boolean

            threadFactory?:  ThreadFactory

        }

        type Timestamp = {

            date?:   number

            day?:   number

            hours?:   number

            minutes?:   number

            month?:   number

            nanos?:   number

            seconds?:   number

            time?:   number

            timezoneOffset?:   number

            year?:   number

        }

        type TradeCancelReq = {

            bizOrderNo:   string

            bizRefundNo:   string

            channelNum:   number

            redisKey?:   string

            tradeNo:   string

            tradeScene:   string

        }

        type TransactionPaymentReq = {

            activityCode?:   string

            businessInfo?:  BusinessInfo

            deductionAmount?:   number

            outPayNo:   string

            paymentInfo:  PaymentInfoReq

            terminalInfo:  TerminalInfo

            tradeNo:   string

        }

        type TransactionPaymentResp = {

            discountAmount?:   number

            jsonObject?:   any

            paidAmount?:   number

            payNo?:   string

            paymentAmount?:   number

            paymentInfo?:    Array < PaymentInfoResp >

            paymentStatus?:   string

            payoffAmount?:   number

        }

        type UnBindRelationReq = {

            bizHead?:  HeadReq

            memberId?:   number

            parentMemberId?:   number

            relationType?:   string

            relationTypeInRange?:   boolean

        }

        type UnbindWechatManagementReq = {

            memberId:   number

            unbindNotes:   string

            bizHead:  HeadReq

        }

        type UpdateBlackWhiteListReq = {

            bizHead?:  HeadReq

            invalidTime?:   string

            listId?:   number

            validTime?:   string

        }

        type UpdateChargeOrderReq = {

            amount?:   number

            bizHead?:  HeadReq

            channelNum?:   number

            memberId?:   number

            memberNum?:   number

            orderNo?:   number

            orderStatus?:   string

            recordId?:   number

            thirdOrderNo?:   string

            tradeNo?:   string

        }

        type UpdateChargeOrderStatusReq = {

            orderStatus?:   string

            tradeNo?:   string

        }

        type UpdateWithdrawStatusReq = {

            bizHead?:  HeadDTO

            businessAccountId?:   number

            orderNum?:   string

            status?:   number

        }

        type UseCouponReq = {

            bizHead?:  HeadReq

            couponCode?:   string

            dealNote?:   string

            memberId?:   number

            payInfo?:    Array < MemberPayInfo >

        }

        type UseCouponResp = {

            dealMoney?:   number

            deposit?:   boolean

        }

        type VerifyInfo = {

            verifyCode?:   string

            verifyType?:   string

        }

        type WalletRechargeReq = {

            activityCode?:   string

            activityName?:   string

            bizHead?:  HeadReq

            channelNum?:   number

            createTime?:   string

            deleteTime?:   string

            memberId?:   number

            memberNum?:   number

            orderNo?:   string

            orderStatus?:   string

            originOrderNo?:   string

            rechargeAmount?:   number

            rechargeCode?:   number

            recordId?:   number

            storeCode?:   string

            terminalInfo?:  TerminalInfo

            thirdOrderNo?:   string

            tradeNo?:   string

            updateTime?:   string

        }

        type WechatMiniProgramDecryptDataReq = {

            encryptedData?:   string

            iv?:   string

            openid?:   string

        }

        type WechatMiniProgramLoginInfoResp = {

            openid?:   string

            unionid?:   string

        }

        type WxRedPackInfo = {

            actName?:   string

            amount?:   number

            remark?:   string

            sceneId?:   string

            wishing?:   string

        }

        type member_relation = {

            channel?:   string

            channelValue?:   string

            createTime?:   string

            deleteTime?:   string

            expireTime?:   string

            memberId?:   number

            memberNum?:   number

            modifyTime?:   string

            parentMemberId?:   number

            parentMemberNum?:   number

            relationId?:   number

            relationType?:   string

        }

        type 创建短信模板入参 = {

            bizHead?:  HeadReq

            remark?:   string

            smsToken?:   string

            templateContent?:   string

            templateName?:   string

            templateType?:   number

        }

        type 失效会员离线会员码 = {

            bizHead?:  HeadReq

            memberId?:   number

        }

        type 更新短信模板入参 = {

            bizHead?:  HeadReq

            remark?:   string

            smsToken?:   string

            templateContent?:   string

            templateId?:   number

            templateName?:   string

            templateType?:   number

        }

        type 查询短信模板入参 = {

            channel?:   string

            createTimeEnd?:   string

            createTimeStart?:   string

            currentPage?:   number

            operatorCode?:   string

            operatorName?:   string

            pageSize?:   number

            smsToken?:   string

            templateContent?:   string

            templateName?:   string

            templateType?:   number

        }

        type 获取离线会员码响应 = {

            expiredTime?:   string

            key?:   string

            systemTime?:   string

        }

        type 获取离线会员码请求参数 = {

            bizHead?:  HeadReq

            memberId?:   number

        }

        type 解析离线会员码入参 = {

            bizHead?:  HeadReq

            memberCode?:   string

        }

        type 解析离线会员码响应 = {

            expiredTime?:   string

            memberId?:   number

        }



      /**description
       *
       */

        /**
        * checkOriginPassword
        * @method
        * @name checkOriginPassword
        * @param  body - accountPasswordReq * @param  x-pagoda-envoy-route-project -
        */
        function postAccountV1AccountPasswordCheckOriginPassword (

            parameters : {
              'body'  : AccountPasswordReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * cleanTodayAccountPasswordRecord
        * @method
        * @name cleanTodayAccountPasswordRecord
        * @param  body - accountPasswordReq * @param  x-pagoda-envoy-route-project -
        */
        function postAccountV1AccountPasswordCleanTodayAccountPasswordRecord (

            parameters : {
              'body'  : AccountPasswordReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * createAccountPassword
        * @method
        * @name createAccountPassword
        * @param  body - accountPasswordReq * @param  x-pagoda-envoy-route-project -
        */
        function postAccountV1AccountPasswordCreateAccountPassword (

            parameters : {
              'body'  : AccountPasswordReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryAccountPasswordRecordSingle
        * @method
        * @name queryAccountPasswordRecordSingle
        * @param  body - accountPasswordReq * @param  x-pagoda-envoy-route-project -
        */
        function postAccountV1AccountPasswordQueryAccountPasswordRecordSingle (

            parameters : {
              'body'  : AccountPasswordReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryAccountPasswordSingle
        * @method
        * @name queryAccountPasswordSingle
        * @param  body - accountPasswordReq * @param  x-pagoda-envoy-route-project -
        */
        function postAccountV1AccountPasswordQueryAccountPasswordSingle (

            parameters : {
              'body'  : AccountPasswordReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * updateAccountPassword
        * @method
        * @name updateAccountPassword
        * @param  body - accountPasswordReq * @param  x-pagoda-envoy-route-project -
        */
        function postAccountV1AccountPasswordUpdateAccountPassword (

            parameters : {
              'body'  : AccountPasswordReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * applyWithdraw
        * @method
        * @name applyWithdraw
        * @param  body - businessWithdrawReq * @param  x-pagoda-envoy-route-project -
        */
        function postAccountV1BizAccountApplyWithdraw (

            parameters : {
              'body'  : BusinessWithdraw,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getEarningsDetail
        * @method
        * @name getEarningsDetail
        * @param integer businessEarningsId - businessEarningsId * @param  x-pagoda-envoy-route-project -
        */
        function getAccountV1BizAccountGetEarningsDetailByBusinessEarningsId (

            parameters : {
              'businessEarningsId'  : number,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * openBizAccount
        * @method
        * @name openBizAccount
        * @param  body - businessInfoReq * @param  x-pagoda-envoy-route-project -
        */
        function postAccountV1BizAccountOpenBizAccount (

            parameters : {
              'body'  : BusinessInfoReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryBizAccountInfo
        * @method
        * @name queryBizAccountInfo
        * @param  body - queryBusinessInfoReq * @param  x-pagoda-envoy-route-project -
        */
        function postAccountV1BizAccountQueryBizAccountInfo (

            parameters : {
              'body'  : QueryBusinessInfoDmReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryBizAccountRecords
        * @method
        * @name queryBizAccountRecords
        * @param  body - businessAccountRecordReq * @param  x-pagoda-envoy-route-project -
        */
        function postAccountV1BizAccountQueryBizAccountRecords (

            parameters : {
              'body'  : BusinessAccountRecordReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryEarningsByBatchOrderNum
        * @method
        * @name queryEarningsByBatchOrderNum
        * @param  body - queryEarningsBatchOrderReq * @param  x-pagoda-envoy-route-project -
        */
        function postAccountV1BizAccountQueryEarningsByBatchOrderNum (

            parameters : {
              'body'  : QueryEarningsBatchOrderReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryEarningsDeal
        * @method
        * @name queryEarningsDeal
        * @param  body - queryEarningsDetailReq * @param  x-pagoda-envoy-route-project -
        */
        function postAccountV1BizAccountQueryEarningsDeal (

            parameters : {
              'body'  : QueryEarningsDetailReIdReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryEarningsDetail
        * @method
        * @name queryEarningsDetail
        * @param  body - queryEarningsDetailReq * @param  x-pagoda-envoy-route-project -
        */
        function postAccountV1BizAccountQueryEarningsDetail (

            parameters : {
              'body'  : QueryEarningsDetailReIdReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryEarningsDetailGroupCode
        * @method
        * @name queryEarningsDetailGroupCode
        * @param  body - queryEarningsDetailReq * @param  x-pagoda-envoy-route-project -
        */
        function postAccountV1BizAccountQueryEarningsDetailGroupCode (

            parameters : {
              'body'  : QueryEarningsDetailReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryWithdrawRecords
        * @method
        * @name queryWithdrawRecords
        * @param  body - businessWithdrawRecordReq * @param  x-pagoda-envoy-route-project -
        */
        function postAccountV1BizAccountQueryWithdrawRecords (

            parameters : {
              'body'  : BusinessWithdrawRecordReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryWithdrawRecordsByPage
        * @method
        * @name queryWithdrawRecordsByPage
        * @param  body - businessWithdrawRecordReq * @param  x-pagoda-envoy-route-project -
        */
        function postAccountV1BizAccountQueryWithdrawRecordsByPage (

            parameters : {
              'body'  : BusinessWithdrawRecordPageReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * updateWithdrawStatus
        * @method
        * @name updateWithdrawStatus
        * @param  body - updateWithdrawStatusReq * @param  x-pagoda-envoy-route-project -
        */
        function postAccountV1BizAccountUpdateWithdrawStatus (

            parameters : {
              'body'  : UpdateWithdrawStatusReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * createDenomination
        * @method
        * @name createDenomination
        * @param  body - denominationReq * @param  x-pagoda-envoy-route-project -
        */
        function postAccountV1DenominationCreateDenomination (

            parameters : {
              'body'  : DenominationReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getDenominationDiff
        * @method
        * @name getDenominationDiff
        * @param  body - denominationMatchReq * @param  x-pagoda-envoy-route-project -
        */
        function postAccountV1DenominationGetDenominationDiff (

            parameters : {
              'body'  : DenominationReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getDenominationListPage
        * @method
        * @name getDenominationListPage
        * @param  body - denominationManagerPageableDTO * @param  x-pagoda-envoy-route-project -
        */
        function postAccountV1DenominationGetDenominationListPage (

            parameters : {
              'body'  : PageableDTODenominationManagerDTO,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getDenominations
        * @method
        * @name getDenominations
        * @param  body - denominationReq * @param  x-pagoda-envoy-route-project -
        */
        function postAccountV1DenominationGetDenominations (

            parameters : {
              'body'  : DenominationReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * updateDenominationDeleteStatus
        * @method
        * @name updateDenominationDeleteStatus
        * @param  body - denominationReq * @param  x-pagoda-envoy-route-project -
        */
        function postAccountV1DenominationUpdateDenominationDeleteStatus (

            parameters : {
              'body'  : DenominationReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * updateDenominationStatus
        * @method
        * @name updateDenominationStatus
        * @param  body - denominationReq * @param  x-pagoda-envoy-route-project -
        */
        function postAccountV1DenominationUpdateDenominationStatus (

            parameters : {
              'body'  : DenominationReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getAccountBalances
        * @method
        * @name getAccountBalances
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postAccountV1MemberAccountGetAccountBalances (

            parameters : {
              'body'  : AccountMemberReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询钱包充值单
        * @method
        * @name 查询钱包充值单
        * @param  body - queryWalletRechargeOrderReq * @param  x-pagoda-envoy-route-project -
        */
        function postAccountV1WalletGetRechargeOrder (

            parameters : {
              'body'  : WalletRechargeReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询钱包充值单列表
        * @method
        * @name 查询钱包充值单列表
        * @param  body - queryWalletRechargeOrderListReqPageableDTO * @param  x-pagoda-envoy-route-project -
        */
        function postAccountV1WalletGetRechargeOrderList (

            parameters : {
              'body'  : PageableDTOWalletRechargeReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 创建钱包充值单
        * @method
        * @name 创建钱包充值单
        * @param  body - walletRechargeOrderReq * @param  x-pagoda-envoy-route-project -
        */
        function postAccountV1WalletRechargeCreateRechargeOrder (

            parameters : {
              'body'  : WalletRechargeReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 修改钱包充值单列表
        * @method
        * @name 修改钱包充值单列表
        * @param  body - updateChargeOrderReq * @param  x-pagoda-envoy-route-project -
        */
        function postAccountV1WalletUpdateChargeOrder (

            parameters : {
              'body'  : UpdateChargeOrderReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据第三方单号修改账户充值单
        * @method
        * @name 根据第三方单号修改账户充值单
        * @param  body - updateChargeOrderStatusReq * @param  x-pagoda-envoy-route-project -
        */
        function postAccountV1WalletUpdateChargeOrderStatus (

            parameters : {
              'body'  : UpdateChargeOrderStatusReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * aliPayAppNotify
        * @method
        * @name aliPayAppNotify
        * @param  body - requestJson * @param  x-pagoda-envoy-route-project -
        */
        function postCallbackV1AlipayNotifyApp (

            parameters : {
              'body'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * aliPayH5Notify
        * @method
        * @name aliPayH5Notify
        * @param  body - requestJson * @param  x-pagoda-envoy-route-project -
        */
        function postCallbackV1AlipayNotifyH5 (

            parameters : {
              'body'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * JDPayNotify
        * @method
        * @name JDPayNotify
        * @param  body - request * @param  x-pagoda-envoy-route-project -
        */
        function postCallbackV1JdpayNotify (

            parameters : {
              'body'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * jdRefundNotify
        * @method
        * @name jdRefundNotify
        * @param  body - request * @param  x-pagoda-envoy-route-project -
        */
        function postCallbackV1JdrefundNotify (

            parameters : {
              'body'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * PAPayNotify
        * @method
        * @name PAPayNotify
        * @param  body - request * @param  x-pagoda-envoy-route-project -
        */
        function postCallbackV1PapayNotify (

            parameters : {
              'body'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * PARefundNotify
        * @method
        * @name PARefundNotify
        * @param  body - request * @param  x-pagoda-envoy-route-project -
        */
        function postCallbackV1ParefundNotify (

            parameters : {
              'body'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * unionPayAppNotify
        * @method
        * @name unionPayAppNotify
        * @param  body - requestParams * @param  x-pagoda-envoy-route-project -
        */
        function postCallbackV1UnionpayNotifyApp (

            parameters : {
              'body'  : any,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * unionPayH5Notify
        * @method
        * @name unionPayH5Notify
        * @param  body - requestParams * @param  x-pagoda-envoy-route-project -
        */
        function postCallbackV1UnionpayNotifyH5 (

            parameters : {
              'body'  : any,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * wxPayAppNotify
        * @method
        * @name wxPayAppNotify
        * @param  body - requestJson * @param  x-pagoda-envoy-route-project -
        */
        function postCallbackV1WxpayNotifyApp (

            parameters : {
              'body'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * wxPayH5Notify
        * @method
        * @name wxPayH5Notify
        * @param  body - requestJson * @param  x-pagoda-envoy-route-project -
        */
        function postCallbackV1WxpayNotifyH5 (

            parameters : {
              'body'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * wxPayMiniNotify
        * @method
        * @name wxPayMiniNotify
        * @param  body - requestJson * @param  x-pagoda-envoy-route-project -
        */
        function postCallbackV1WxpayNotifyMini (

            parameters : {
              'body'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * wxPayPubNotify
        * @method
        * @name wxPayPubNotify
        * @param  body - requestJson * @param  x-pagoda-envoy-route-project -
        */
        function postCallbackV1WxpayNotifyPub (

            parameters : {
              'body'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * wxPaySvipNotify
        * @method
        * @name wxPaySvipNotify
        * @param  body - requestJson * @param  x-pagoda-envoy-route-project -
        */
        function postCallbackV1WxpayNotifySvip (

            parameters : {
              'body'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * wxRefundAppNotify
        * @method
        * @name wxRefundAppNotify
        * @param  body - requestJson * @param  x-pagoda-envoy-route-project -
        */
        function postCallbackV1WxrefundNotifyApp (

            parameters : {
              'body'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * wxRefundH5Notify
        * @method
        * @name wxRefundH5Notify
        * @param  body - requestJson * @param  x-pagoda-envoy-route-project -
        */
        function postCallbackV1WxrefundNotifyH5 (

            parameters : {
              'body'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * wxRefundMiniNotify
        * @method
        * @name wxRefundMiniNotify
        * @param  body - requestJson * @param  x-pagoda-envoy-route-project -
        */
        function postCallbackV1WxrefundNotifyMini (

            parameters : {
              'body'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * wxRefundPubNotify
        * @method
        * @name wxRefundPubNotify
        * @param  body - requestJson * @param  x-pagoda-envoy-route-project -
        */
        function postCallbackV1WxrefundNotifyPub (

            parameters : {
              'body'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * wxRefundSvipNotify
        * @method
        * @name wxRefundSvipNotify
        * @param  body - requestJson * @param  x-pagoda-envoy-route-project -
        */
        function postCallbackV1WxrefundNotifySvip (

            parameters : {
              'body'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * zhPayAppNotify
        * @method
        * @name zhPayAppNotify
        * @param  body - request * @param  x-pagoda-envoy-route-project -
        */
        function postCallbackV1ZhpayNotifyApp (

            parameters : {
              'body'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * zhPayH5Notify
        * @method
        * @name zhPayH5Notify
        * @param  body - request * @param  x-pagoda-envoy-route-project -
        */
        function postCallbackV1ZhpayNotifyH5 (

            parameters : {
              'body'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * sendCaptcha
        * @method
        * @name sendCaptcha
        * @param  body - sendCaptchaReq * @param  x-pagoda-envoy-route-project -
        */
        function postCaptchaSend (

            parameters : {
              'body'  : SendCaptchaReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * check
        * @method
        * @name check
        * @param  x-pagoda-envoy-route-project -
        */
        function getCheck (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * checkRpc
        * @method
        * @name checkRpc
        * @param  x-pagoda-envoy-route-project -
        */
        function getCheckRpc (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * receiveBugGiftCouponCallback
        * @method
        * @name receiveBugGiftCouponCallback
        * @param string couponCode - couponCode * @param  x-pagoda-envoy-route-project -
        */
        function getCouponV1BuyGiftCouponCallbackByCouponCode (

            parameters : {
              'couponCode'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * receiveBugGiftCouponCallback
        * @method
        * @name receiveBugGiftCouponCallback
        * @param string couponCode - couponCode
        */
        function headCouponV1BuyGiftCouponCallbackByCouponCode (

            parameters : {
              'couponCode'  : string,

            }


        ): Promise < AxiosResponse >

        /**
        * receiveBugGiftCouponCallback
        * @method
        * @name receiveBugGiftCouponCallback
        * @param string couponCode - couponCode
        */
        function postCouponV1BuyGiftCouponCallbackByCouponCode (

            parameters : {
              'couponCode'  : string,

            }


        ): Promise < AxiosResponse >

        /**
        * receiveBugGiftCouponCallback
        * @method
        * @name receiveBugGiftCouponCallback
        * @param string couponCode - couponCode
        */
        function putCouponV1BuyGiftCouponCallbackByCouponCode (

            parameters : {
              'couponCode'  : string,

            }


        ): Promise < AxiosResponse >

        /**
        * receiveBugGiftCouponCallback
        * @method
        * @name receiveBugGiftCouponCallback
        * @param string couponCode - couponCode
        */
        function deleteCouponV1BuyGiftCouponCallbackByCouponCode (

            parameters : {
              'couponCode'  : string,

            }


        ): Promise < AxiosResponse >

        /**
        * receiveBugGiftCouponCallback
        * @method
        * @name receiveBugGiftCouponCallback
        * @param string couponCode - couponCode
        */
        function optionsCouponV1BuyGiftCouponCallbackByCouponCode (

            parameters : {
              'couponCode'  : string,

            }


        ): Promise < AxiosResponse >

        /**
        * receiveBugGiftCouponCallback
        * @method
        * @name receiveBugGiftCouponCallback
        * @param string couponCode - couponCode
        */
        function patchCouponV1BuyGiftCouponCallbackByCouponCode (

            parameters : {
              'couponCode'  : string,

            }


        ): Promise < AxiosResponse >

        /**
        * queryCouponAndBatchList
        * @method
        * @name queryCouponAndBatchList
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postCouponV1CouponQueryCouponAndBatchList (

            parameters : {
              'body'  : CouponSearchReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * createCouponBatch
        * @method
        * @name createCouponBatch
        * @param  body - couponBatchReq * @param  x-pagoda-envoy-route-project -
        */
        function postCouponV1CouponBatchCreateCouponBatch (

            parameters : {
              'body'  : CouponTemplateReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * deactivateCouponBatch
        * @method
        * @name deactivateCouponBatch
        * @param  body - couponBatchStatusChangeReq * @param  x-pagoda-envoy-route-project -
        */
        function postCouponV1CouponBatchDeactivateCouponBatch (

            parameters : {
              'body'  : CouponBatchStatusVaryReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryCouponBatchByBatchNum
        * @method
        * @name queryCouponBatchByBatchNum
        * @param string batchNum - batchNum * @param  x-pagoda-envoy-route-project -
        */
        function getCouponV1CouponBatchQueryCouponBatchByBatchNumByBatchNum (

            parameters : {
              'batchNum'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getCouponBatchPage
        * @method
        * @name getCouponBatchPage
        * @param  body - pageableDTO * @param  x-pagoda-envoy-route-project -
        */
        function postCouponV1CouponBatchQueryCouponBatchInfo (

            parameters : {
              'body'  : PageableDTOCouponBatchTemplateReqDTO,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryCouponBatchWithActivityType
        * @method
        * @name queryCouponBatchWithActivityType
        * @param  body - couponFeedBackReqDTO * @param  x-pagoda-envoy-route-project -
        */
        function postCouponV1CouponBatchQueryCouponBatchWithActivityType (

            parameters : {
              'body'  : CouponrFeedBackReqDTO,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryCouponExistByBatchNumAndId
        * @method
        * @name queryCouponExistByBatchNumAndId
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postCouponV1CouponBatchQueryCouponExistByBatchNumAndId (

            parameters : {
              'body'  : QueryCouponExistDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryCouponTypeAndBatchByBatchNum
        * @method
        * @name queryCouponTypeAndBatchByBatchNum
        * @param string batchNum - batchNum * @param  x-pagoda-envoy-route-project -
        */
        function getCouponV1CouponBatchQueryCouponTypeAndBatchByBatchNumByBatchNum (

            parameters : {
              'batchNum'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * verifyCouponBatch
        * @method
        * @name verifyCouponBatch
        * @param  body - couponBatchStatusVaryReq * @param  x-pagoda-envoy-route-project -
        */
        function postCouponV1CouponBatchVerifyCouponBatch (

            parameters : {
              'body'  : CouponBatchStatusVaryReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryCouponStoreLimitByBatchNum
        * @method
        * @name queryCouponStoreLimitByBatchNum
        * @param  body - couponRuleReq * @param  x-pagoda-envoy-route-project -
        */
        function postCouponV1CouponRuleStoreLimitQueryByBatchNum (

            parameters : {
              'body'  : CouponRuleReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryCouponUseRuleByBatchNum
        * @method
        * @name queryCouponUseRuleByBatchNum
        * @param  body - couponRuleReq * @param  x-pagoda-envoy-route-project -
        */
        function postCouponV1CouponRuleUseRuleQueryByBatchNum (

            parameters : {
              'body'  : CouponRuleReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryCouponUseRuleByBatchNumList
        * @method
        * @name queryCouponUseRuleByBatchNumList
        * @param  body - queryCouponRuleListReq * @param  x-pagoda-envoy-route-project -
        */
        function postCouponV1CouponRuleUseRuleQueryCouponUseRuleByBatchNumList (

            parameters : {
              'body'  : QueryCouponRuleListReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * createCouponType
        * @method
        * @name createCouponType
        * @param  body - couponTemplateReq * @param  x-pagoda-envoy-route-project -
        */
        function postCouponV1CouponTypeCreateCouponType (

            parameters : {
              'body'  : CouponTemplateReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getCouponTypeListPage
        * @method
        * @name getCouponTypeListPage
        * @param  body - couponTemplateReq * @param  x-pagoda-envoy-route-project -
        */
        function postCouponV1CouponTypeGetCouponTypeListPage (

            parameters : {
              'body'  : PageableDTOCouponRespDTO,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getCouponGoods
        * @method
        * @name getCouponGoods
        * @param integer couponTypeId - couponTypeId * @param  x-pagoda-envoy-route-project -
        */
        function getCouponV1CouponTypeGoodsByCouponTypeId (

            parameters : {
              'couponTypeId'  : number,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryCouponTypeDetail
        * @method
        * @name queryCouponTypeDetail
        * @param  body - couponTemplateReq * @param  x-pagoda-envoy-route-project -
        */
        function postCouponV1CouponTypeQueryCouponTypeDetail (

            parameters : {
              'body'  : CouponTemplateReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * statusChangeCouponType
        * @method
        * @name statusChangeCouponType
        * @param  body - couponTemplateReq * @param  x-pagoda-envoy-route-project -
        */
        function postCouponV1CouponTypeStatusChangeCouponType (

            parameters : {
              'body'  : CouponTemplateReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * updateCouponType
        * @method
        * @name updateCouponType
        * @param  body - updateReq * @param  x-pagoda-envoy-route-project -
        */
        function postCouponV1CouponTypeUpdateCouponType (

            parameters : {
              'body'  : CouponTypeUpdateReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * couponBindingByCode
        * @method
        * @name couponBindingByCode
        * @param  body - couponReq * @param  x-pagoda-envoy-route-project -
        */
        function postCouponV1MemberCouponBindCouponByCode (

            parameters : {
              'body'  : BindingCouponReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 创建卡券购买单
        * @method
        * @name 创建卡券购买单
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postCouponV1MemberCouponCreateBuyOrder (

            parameters : {
              'body'  : CouponBuyOrderReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据购买单查询购买单详情
        * @method
        * @name 根据购买单查询购买单详情
        * @param string orderNo - orderNo * @param  x-pagoda-envoy-route-project -
        */
        function getCouponV1MemberCouponGetBuyOrderByOrderNo (

            parameters : {
              'orderNo'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据会员Id查询卡券购买单
        * @method
        * @name 根据会员Id查询卡券购买单
        * @param integer memberId - memberId * @param  x-pagoda-envoy-route-project -
        */
        function getCouponV1MemberCouponGetBuyOrderListByMemberId (

            parameters : {
              'memberId'  : number,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getCouponByBatchNum
        * @method
        * @name getCouponByBatchNum
        * @param  body - couponReq * @param  x-pagoda-envoy-route-project -
        */
        function postCouponV1MemberCouponGetCouponByBatchNum (

            parameters : {
              'body'  : GetCouponReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * isRechargeCoupon
        * @method
        * @name isRechargeCoupon
        * @param  body - isRechargeReq * @param  x-pagoda-envoy-route-project -
        */
        function postCouponV1MemberCouponIsRechargeCoupon (

            parameters : {
              'body'  : IsRechargeReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryMemberCoupons
        * @method
        * @name queryMemberCoupons
        * @param  body - memberCoupon * @param  x-pagoda-envoy-route-project -
        */
        function postCouponV1MemberCouponQuery (

            parameters : {
              'body'  : MemberCouponDTO,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryMemberCoupon
        * @method
        * @name queryMemberCoupon
        * @param  body - memberCouponReq * @param  x-pagoda-envoy-route-project -
        */
        function postCouponV1MemberCouponQueryByGoods (

            parameters : {
              'body'  : MemberCouponReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryMemberCouponInfo
        * @method
        * @name queryMemberCouponInfo
        * @param  body - memberCouponReq * @param  x-pagoda-envoy-route-project -
        */
        function postCouponV1MemberCouponQueryMemberCouponInfo (

            parameters : {
              'body'  : MemberCouponReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryMemberInfoByCouponCode
        * @method
        * @name queryMemberInfoByCouponCode
        * @param string couponCode - couponCode * @param  x-pagoda-envoy-route-project -
        */
        function getCouponV1MemberCouponQueryMemberInfoByCouponCodeByCouponCode (

            parameters : {
              'couponCode'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * useCoupon
        * @method
        * @name useCoupon
        * @param  body - useCouponReq * @param  x-pagoda-envoy-route-project -
        */
        function postCouponV1MemberCouponUseCoupon (

            parameters : {
              'body'  : UseCouponReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据条件模糊查询erp大客户数据
        * @method
        * @name 根据条件模糊查询erp大客户数据
        * @param  body - jsonObject * @param  x-pagoda-envoy-route-project -
        */
        function postCouponV1OtherQueryBoaCodeByErp (

            parameters : {
              'body'  : any,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据优惠券码或者批次号查询优惠券信息【提供给POS】
        * @method
        * @name 根据优惠券码或者批次号查询优惠券信息【提供给POS】
        * @param string couponInfo - couponInfo * @param  x-pagoda-envoy-route-project -
        */
        function getCouponV1PosQueryCouponInfoByPosByCouponInfo (

            parameters : {
              'couponInfo'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * checkServiceRpc
        * @method
        * @name checkServiceRpc
        * @param  x-pagoda-envoy-route-project -
        */
        function getHealthCheckRpc (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询会员登录记录（分页）
        * @method
        * @name 查询会员登录记录（分页）
        * @param  body - queryReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1BehaviorRecords (

            parameters : {
              'body'  : MemberBehaviorRecordQueryReqDTO,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * add
        * @method
        * @name add
        * @param  body - blackWhiteListReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1BlackwhitelistAdd (

            parameters : {
              'body'  : AddBlackWhiteListReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 【批量】以会员ID过滤黑名单
        * @method
        * @name 【批量】以会员ID过滤黑名单
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1BlackwhitelistBatchQueryInBlackWhiteMemberIds (

            parameters : {
              'body'  : BatchQueryInBlackWhiteReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * countBlackWhiteList
        * @method
        * @name countBlackWhiteList
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1BlackwhitelistCount (

            parameters : {
              'body'  : QueryBlackWhiteListReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * pullBlack
        * @method
        * @name pullBlack
        * @param  body - pullBlackWhiteListReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1BlackwhitelistPullBlack (

            parameters : {
              'body'  : PullBlackWhiteListReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryLastRecord
        * @method
        * @name queryLastRecord
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1BlackwhitelistQueryLastRecord (

            parameters : {
              'body'  : QueryBlackWhiteListReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryMemberIsBlackList
        * @method
        * @name queryMemberIsBlackList
        * @param  body - blackWhiteReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1BlackwhitelistQueryMemberIsBlackWhite (

            parameters : {
              'body'  : QueryBlackWhiteReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * remove
        * @method
        * @name remove
        * @param  body - removeBlackWhiteListReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1BlackwhitelistRemove (

            parameters : {
              'body'  : RemoveBlackWhiteListReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * update
        * @method
        * @name update
        * @param  body - updateBlackWhiteListReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1BlackwhitelistUpdate (

            parameters : {
              'body'  : UpdateBlackWhiteListReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * get
        * @method
        * @name get
        * @param integer listId - listId * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1BlackwhitelistByListId (

            parameters : {
              'listId'  : number,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getList
        * @method
        * @name getList
        * @param  body - queryDTO * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1Blackwhitelists (

            parameters : {
              'body'  : QueryBlackWhiteListReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * cancel
        * @method
        * @name cancel
        * @param  body - memberCancelReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1Cancel (

            parameters : {
              'body'  : MemberCancelReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 通过识别号-查询已注销信息
        * @method
        * @name 通过识别号-查询已注销信息
        * @param string identity - identity * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1CancelQueryCancelMemberInfoByIdentity (

            parameters : {
              'identity'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getRecord
        * @method
        * @name getRecord
        * @param string recordId - recordId * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1CancelRecordByRecordId (

            parameters : {
              'recordId'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getRecords
        * @method
        * @name getRecords
        * @param  body - request * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1CancelRecords (

            parameters : {
              'body'  : MemberCancelRecordSearchConditionReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * verifyCode
        * @method
        * @name verifyCode
        * @param string code - code * @param string identity - identity * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1CaptchaVerifyCodeTempByIdentityByCode (

            parameters : {
              'code'  : string,
              'identity'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询可领取权益（客户端）
        * @method
        * @name 查询可领取权益（客户端）
        * @param  body - memberEquityReceiveReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1Equity (

            parameters : {
              'body'  : MemberEquityReviewReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 新增会员权益
        * @method
        * @name 新增会员权益
        * @param  body - memberEquityReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1EquityAdd (

            parameters : {
              'body'  : MemberEquityReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询会员权益列表
        * @method
        * @name 查询会员权益列表
        * @param  body - memberEquityListReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1EquityList (

            parameters : {
              'body'  : MemberEquityListReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询权益类型列表
        * @method
        * @name 查询权益类型列表
        * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1EquityQueryEquityTypeList (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询已领取月度礼包记录
        * @method
        * @name 查询已领取月度礼包记录
        * @param  body - memberEquityReceiveReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1EquityQueryMemberEquityReceive (

            parameters : {
              'body'  : MemberEquityReviewReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询付费会员月度礼包套餐权益(用于页面上显示年卡剩余月份的套餐券包)
        * @method
        * @name 查询付费会员月度礼包套餐权益(用于页面上显示年卡剩余月份的套餐券包)
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1EquityQueryMonthEquityList (

            parameters : {
              'body'  : QueryMemberEquityListReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据付费会员类型-查询月度礼包套餐
        * @method
        * @name 根据付费会员类型-查询月度礼包套餐
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1EquityQueryMonthListByTypeCode (

            parameters : {
              'body'  : MemberEquityMonthListByIdentifyReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询互斥批次
        * @method
        * @name 查询互斥批次
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1EquityQueryMutexBatch (

            parameters : {
              'body'  : MutexBatchReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据是否首次开通查询开卡礼包对应的付费会员类型列表
        * @method
        * @name 根据是否首次开通查询开卡礼包对应的付费会员类型列表
        * @param boolean isFirstOpen - isFirstOpen * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1EquityQueryTypeCodeListByIsFirstOpenByIsFirstOpen (

            parameters : {
              'isFirstOpen'  : boolean,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 领取会员权益（客户端）
        * @method
        * @name 领取会员权益（客户端）
        * @param  body - receiveMemberEquityReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1EquityReceive (

            parameters : {
              'body'  : ReceiveMemberEquityReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询月度礼包记录（客户端）
        * @method
        * @name 查询月度礼包记录（客户端）
        * @param  body - memberEquityReceiveReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1EquityRecord (

            parameters : {
              'body'  : MemberEquityReviewReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 审核会员权益
        * @method
        * @name 审核会员权益
        * @param  body - memberEquityReviewBatchReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1EquityReview (

            parameters : {
              'body'  : MemberEquityReviewBatchReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 修改会员权益
        * @method
        * @name 修改会员权益
        * @param  body - memberEquityReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1EquityUpdate (

            parameters : {
              'body'  : MemberEquityReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * memberIsBeingByIdentity
        * @method
        * @name memberIsBeingByIdentity
        * @param string identity - identity * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1ExistsIdentityByIdentity (

            parameters : {
              'identity'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * decryptWechatMiniProgramEncryptedData
        * @method
        * @name decryptWechatMiniProgramEncryptedData
        * @param string programType - programType * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1ExternalMemberDecryptWechatMiniProgramEncryptedDataByProgramType (

            parameters : {
              'programType'  : string,
              'body'  : WechatMiniProgramDecryptDataReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getWechatMiniProgramOpenid
        * @method
        * @name getWechatMiniProgramOpenid
        * @param string jsCode - jsCode * @param string programType - programType * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1ExternalMemberGetWechatMiniProgramOpenidByProgramTypeByJsCode (

            parameters : {
              'jsCode'  : string,
              'programType'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 微信解绑
        * @method
        * @name 微信解绑
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1ExternalMemberUnbindWechatManagement (

            parameters : {
              'body'  : UnbindWechatManagementReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * firstConsumeStore
        * @method
        * @name firstConsumeStore
        * @param  body - consumeStoreReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1FirstConsumeStore (

            parameters : {
              'body'  : ConsumeStoreReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * memberVerify
        * @method
        * @name memberVerify
        * @param  body - memberVerifyReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1Identify (

            parameters : {
              'body'  : MemberVerifyReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * cancelMemberVerify
        * @method
        * @name cancelMemberVerify
        * @param  body - memberCancelReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1IdentifyCancelmember (

            parameters : {
              'body'  : MemberVerifyCancelReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryMemberVerifyRecord
        * @method
        * @name queryMemberVerifyRecord
        * @param integer recordId - recordId * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1IdentifyRecordByRecordId (

            parameters : {
              'recordId'  : number,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryMemberVerifyByIdCardNum
        * @method
        * @name queryMemberVerifyByIdCardNum
        * @param string idCardNum - idCardNum * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1IdentifyVerifyIdcardnum (

            parameters : {
              'idCardNum'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryMemberVerifyByMemberId
        * @method
        * @name queryMemberVerifyByMemberId
        * @param integer memberId - memberId * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1IdentifyVerifyMemberidByMemberId (

            parameters : {
              'memberId'  : number,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * memberVerifyWithOutCertify
        * @method
        * @name memberVerifyWithOutCertify
        * @param  body - memberVerifyReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1IdentifyWithOutCertify (

            parameters : {
              'body'  : MemberVerifyReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryMemberVerify
        * @method
        * @name queryMemberVerify
        * @param integer memberId - memberId * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1IdentifyByMemberId (

            parameters : {
              'memberId'  : number,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * change
        * @method
        * @name change
        * @param  body - memberTransferReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1IdentityChange (

            parameters : {
              'body'  : MemberTransferReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryChangeMemberDetailByOperateId
        * @method
        * @name queryChangeMemberDetailByOperateId
        * @param integer recordId - recordId * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1IdentityChangeRecordByRecordId (

            parameters : {
              'recordId'  : number,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryTransferRecords
        * @method
        * @name queryTransferRecords
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1IdentityChangeRecords (

            parameters : {
              'body'  : QueryMemberTransferRecordReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * review
        * @method
        * @name review
        * @param  body - memberTransferReviewReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1IdentityChangeReview (

            parameters : {
              'body'  : MemberTransferReviewReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * loginVerify
        * @method
        * @name loginVerify
        * @param  body - loginVerifyReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1Login (

            parameters : {
              'body'  : LoginVerifyReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getDetailList
        * @method
        * @name getDetailList
        * @param  body - queryDTO * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1ManagerBlackwhitelists (

            parameters : {
              'body'  : QueryBlackWhiteListReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * modifyMemberConfig
        * @method
        * @name modifyMemberConfig
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1MemberConfigModifyMemberConfig (

            parameters : {
              'body'  : MemberConfigReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 6.4.查询成长值余额
        * @method
        * @name 6.4.查询成长值余额
        * @param integer memberId - memberId * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1MemberGrowthBalanceByMemberId (

            parameters : {
              'memberId'  : number,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 成长值变动
        * @method
        * @name 成长值变动
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1MemberGrowthChange (

            parameters : {
              'body'  : MemberGrowthReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 6.3.查询会员成长值变更记录 分页
        * @method
        * @name 6.3.查询会员成长值变更记录 分页
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1MemberGrowthModifyRecordList (

            parameters : {
              'body'  : MemberGrowthModifyRecordQueryReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询会员基本结构
        * @method
        * @name 查询会员基本结构
        * @param integer memberId - memberId * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1MemberInfoQueryMemberBaseInfoByMemberId (

            parameters : {
              'memberId'  : number,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 通过识别号-查询会员基本信息
        * @method
        * @name 通过识别号-查询会员基本信息
        * @param string identity - identity * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1MemberInfoQueryMemberBaseInfoByIdentityByIdentity (

            parameters : {
              'identity'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 通过组合条件-查询会员基本信息
        * @method
        * @name 通过组合条件-查询会员基本信息
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1MemberInfoQueryMemberBaseInfoByJoinCondition (

            parameters : {
              'body'  : QueryMemberBaseInfoByJoinConditionReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 通过会员Id列表查询微信会员信息
        * @method
        * @name 通过会员Id列表查询微信会员信息
        * @param  body - unionIds * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1MemberInfoQueryMemberIdsByUniondIds (

            parameters : {
              'body'  : Array<string>,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryMemberInfoMultiExternal
        * @method
        * @name queryMemberInfoMultiExternal
        * @param integer memberId - memberId * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1MemberInfoQueryMemberInfoMultiExternalByMemberId (

            parameters : {
              'memberId'  : number,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 通过会员Id列表查询一体化会员会员基础信息
        * @method
        * @name 通过会员Id列表查询一体化会员会员基础信息
        * @param  body - memberIds * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1MemberInfoQueryMembersByMemberIds (

            parameters : {
              'body'  : Array<string>,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 通过会员Id列表查询会员基础信息
        * @method
        * @name 通过会员Id列表查询会员基础信息
        * @param  body - memberIds * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1MemberInfoQueryMembersByMemberIdsWithPayMemberInfo (

            parameters : {
              'body'  : Array<number>,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 通过手机号列表查询一体化会员会员基础信息
        * @method
        * @name 通过手机号列表查询一体化会员会员基础信息
        * @param  body - phones * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1MemberInfoQueryMembersByPhones (

            parameters : {
              'body'  : Array<string>,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据会员ID批量查询openID
        * @method
        * @name 根据会员ID批量查询openID
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1MemberInfoQueryOpenIdsByMemberIds (

            parameters : {
              'body'  : QueryOpenIdsByMemberIdsReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 通过识别号-区分是否为付费会员
        * @method
        * @name 通过识别号-区分是否为付费会员
        * @param string identity - identity * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1MemberInfoQueryPayMemberByIdentityByIdentity (

            parameters : {
              'identity'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 通过会员ID-查询会员基本信息
        * @method
        * @name 通过会员ID-查询会员基本信息
        * @param integer memberId - memberId * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1MemberInfoQueryPayMemberByMemberIdByMemberId (

            parameters : {
              'memberId'  : number,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 通过openID-查询会员基本信息
        * @method
        * @name 通过openID-查询会员基本信息
        * @param string openId - openId * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1MemberInfoQueryPayMemberByOpenIdByOpenId (

            parameters : {
              'openId'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * updateMultiExternalQueryMember
        * @method
        * @name updateMultiExternalQueryMember
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1MemberInfoUpdateMultiExternalQueryMember (

            parameters : {
              'body'  : ExternalMemberQueryRulesUpdateReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 5.1.新增等级
        * @method
        * @name 5.1.新增等级
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1MemberLevelAdd (

            parameters : {
              'body'  : MemberLevelReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询所有会员 的等级
        * @method
        * @name 查询所有会员 的等级
        * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1MemberLevelAllMemberLevelInfos (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 仅查询会员 的等级
        * @method
        * @name 仅查询会员 的等级
        * @param integer memberId - memberId * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1MemberLevelMemberInfoByMemberId (

            parameters : {
              'memberId'  : number,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 分页查询变动记录
        * @method
        * @name 分页查询变动记录
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1MemberLevelModifyRecordList (

            parameters : {
              'body'  : MemberLevelModifyRecordReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询基础等级相关信息
        * @method
        * @name 查询基础等级相关信息
        * @param integer levelId - levelId * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1MemberLevelQueryByLevelId (

            parameters : {
              'levelId'  : number,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 5.2.修改等级
        * @method
        * @name 5.2.修改等级
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1MemberLevelUpdate (

            parameters : {
              'body'  : MemberLevelReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * memberLock
        * @method
        * @name memberLock
        * @param  body - memberLockReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1MemberLockLock (

            parameters : {
              'body'  : MemberLockReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryMemberLockDetail
        * @method
        * @name queryMemberLockDetail
        * @param integer recordId - recordId * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1MemberLockRecordByRecordId (

            parameters : {
              'recordId'  : number,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryMemberLockList
        * @method
        * @name queryMemberLockList
        * @param  body - queryMemberLockListReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1MemberLockRecords (

            parameters : {
              'body'  : QueryMemberLockListReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * memberUnLock
        * @method
        * @name memberUnLock
        * @param  body - memberUnLockReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1MemberLockUnLock (

            parameters : {
              'body'  : MemberUnLockReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 新增或更新记录
        * @method
        * @name 新增或更新记录
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1MemberVisitorAddOrUpdate (

            parameters : {
              'body'  : MemberVisitorAddReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据 联系方式 查询 信息
        * @method
        * @name 根据 联系方式 查询 信息
        * @param string phone - phone * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1MemberVisitorQueryByPhone (

            parameters : {
              'phone'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 解析离线会员码
        * @method
        * @name 解析离线会员码
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1MembercodeDistinguish (

            parameters : {
              'body'  : 解析离线会员码入参,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 使会员当前会员码失效
        * @method
        * @name 使会员当前会员码失效
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1MembercodeInvalid (

            parameters : {
              'body'  : 失效会员离线会员码,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 获取生成离线会员码的key
        * @method
        * @name 获取生成离线会员码的key
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1MembercodeReceivekey (

            parameters : {
              'body'  : 获取离线会员码请求参数,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 校验会员码是否可用
        * @method
        * @name 校验会员码是否可用
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1MembercodeValidate (

            parameters : {
              'body'  : MemberCodeValidReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * updateMemberInfo
        * @method
        * @name updateMemberInfo
        * @param  body - memberUpdateReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1Memberinfo (

            parameters : {
              'body'  : MemberUpdateReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getMemberByIdentity
        * @method
        * @name getMemberByIdentity
        * @param string identity - identity * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1MemberinfoIdentityByIdentity (

            parameters : {
              'identity'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getMemberByMemberId
        * @method
        * @name getMemberByMemberId
        * @param integer memberId - memberId * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1MemberinfoMemberIdByMemberId (

            parameters : {
              'memberId'  : number,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getBasicMemberBymemberNum
        * @method
        * @name getBasicMemberBymemberNum
        * @param integer memberNum - memberNum * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1MemberinfoMemberNumByMemberNum (

            parameters : {
              'memberNum'  : number,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * createConfig
        * @method
        * @name createConfig
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1NotifyConfigCreate (

            parameters : {
              'body'  : CreateNotifyConfigReqDTO,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryPageable
        * @method
        * @name queryPageable
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1NotifyConfigQueryPageable (

            parameters : {
              'body'  : QueryNotifyConfigReqDTO,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * updateConfig
        * @method
        * @name updateConfig
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1NotifyConfigUpdate (

            parameters : {
              'body'  : CreateNotifyConfigReqDTO,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * addPayMemberRefund
        * @method
        * @name addPayMemberRefund
        * @param  body - payMemberAddRefundReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberAddRefund (

            parameters : {
              'body'  : PayMemberAddRefundReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量邀请试用会员
        * @method
        * @name 批量邀请试用会员
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberBatchInvited (

            parameters : {
              'body'  : InvitePayMemberReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * batchSelectMemberInfo
        * @method
        * @name batchSelectMemberInfo
        * @param  body - memberIdList * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberBatchSelectMemberInfo (

            parameters : {
              'body'  : Array<number>,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 购买付费会员
        * @method
        * @name 购买付费会员
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberBuy (

            parameters : {
              'body'  : PayMemberBuyReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * buyPayMemberMigrate
        * @method
        * @name buyPayMemberMigrate
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberBuyPayMemberMigrate (

            parameters : {
              'body'  : PayMemberBuyReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * calculateMaxRefundMoney
        * @method
        * @name calculateMaxRefundMoney
        * @param string buyNum - buyNum * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1PayMemberCalculateMaxRefundMoneyByBuyNum (

            parameters : {
              'buyNum'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * canRefundOriginWay
        * @method
        * @name canRefundOriginWay
        * @param string buyNum - buyNum * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1PayMemberCanRefundOriginWayByBuyNum (

            parameters : {
              'buyNum'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 兑换码兑换付费会员
        * @method
        * @name 兑换码兑换付费会员
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberConvert (

            parameters : {
              'body'  : RedeemPayMemberResp,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * doBuyPayMemberCallBack
        * @method
        * @name doBuyPayMemberCallBack
        * @param string thirdPartyOrderNum - thirdPartyOrderNum * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1PayMemberDoBuyPayMemberCallBackByThirdPartyOrderNum (

            parameters : {
              'thirdPartyOrderNum'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 付费会员邀请试用会员
        * @method
        * @name 付费会员邀请试用会员
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberInvited (

            parameters : {
              'body'  : RedeemPayMemberResp,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * modifyPayMemberRefund
        * @method
        * @name modifyPayMemberRefund
        * @param  body - payMemberModifyRefundReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberModifyRefund (

            parameters : {
              'body'  : PayMemberModifyRefundReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryPayMemberModifyRecords
        * @method
        * @name queryPayMemberModifyRecords
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberQueryPayMemberModifyRecords (

            parameters : {
              'body'  : PayMemberModifyRecordQueryReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询付费会员周期套餐值
        * @method
        * @name 查询付费会员周期套餐值
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberQueryPayMemberPeriodSetPackageValue (

            parameters : {
              'body'  : PayMemberPeriodSetPackageValueReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryPayMemberRecordByThirdPartyOrderNum
        * @method
        * @name queryPayMemberRecordByThirdPartyOrderNum
        * @param string thirdPartyOrderNum - thirdPartyOrderNum * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1PayMemberQueryPayMemberRecordByThirdPartyOrderNumByThirdPartyOrderNum (

            parameters : {
              'thirdPartyOrderNum'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 分页查询付费会员节省金额明细
        * @method
        * @name 分页查询付费会员节省金额明细
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberQueryPayMemberSaveDetailMoney (

            parameters : {
              'body'  : PayMemberQuerySaveMoneyDetailReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryPayMemberSaveDetailMoneyOld
        * @method
        * @name queryPayMemberSaveDetailMoneyOld
        * @param integer memberId - memberId * @param string tradeTime - tradeTime * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1PayMemberQueryPayMemberSaveDetailMoneyByMemberIdByTradeTime (

            parameters : {
              'memberId'  : number,
              'tradeTime'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryPayMemberSaveDetailMoney
        * @method
        * @name queryPayMemberSaveDetailMoney
        * @param integer memberId - memberId * @param string tradeTime - tradeTime * @param string tradeTimeEnd - tradeTimeEnd * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1PayMemberQueryPayMemberSaveDetailMoneyByMemberIdByTradeTimeByTradeTimeEnd (

            parameters : {
              'memberId'  : number,
              'tradeTime'  : string,
              'tradeTimeEnd'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 分页查询付费会员节省金额明细
        * @method
        * @name 分页查询付费会员节省金额明细
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberQueryPayMemberSaveMoneyGroupByType (

            parameters : {
              'body'  : PayMemberQuerySaveMoneyDetailReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 分页查询付费会员节省金额明细
        * @method
        * @name 分页查询付费会员节省金额明细
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberQueryPayMemberSaveMoneyPeriodRecord (

            parameters : {
              'body'  : PayMemberQuerySaveMoneyDetailReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryPayMemberSaveTotalMoney
        * @method
        * @name queryPayMemberSaveTotalMoney
        * @param integer memberId - memberId * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1PayMemberQueryPayMemberSaveTotalMoneyByMemberId (

            parameters : {
              'memberId'  : number,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryMemberRefund
        * @method
        * @name queryMemberRefund
        * @param  body - payMemberQueryRefundReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberQueryRefund (

            parameters : {
              'body'  : PayMemberRefundRecordQueryReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryEffectiveBuyPeriodList
        * @method
        * @name queryEffectiveBuyPeriodList
        * @param integer memberId - memberId * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1PayMemberQueryRefundEffectiveBuyPeriodListByMemberId (

            parameters : {
              'memberId'  : number,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 接收营销活动邀请
        * @method
        * @name 接收营销活动邀请
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberReceiveMarketingActivityInvite (

            parameters : {
              'body'  : ReceiveMarketingPayMemberReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 右模糊查询所有兑换码
        * @method
        * @name 右模糊查询所有兑换码
        * @param string redeemCodeKey - redeemCodeKey * @param integer size - size * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberRedeemCodeFuzzyQueryList (

            parameters : {
              'redeemCodeKey'  ?: string,
              'size'  ?: number,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 文档 4.8
        * @method
        * @name 文档 4.8
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberRedeemCodeStop (

            parameters : {
              'body'  : PayMemberStopRedeemCodeReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 文档 4.7
        * @method
        * @name 文档 4.7
        * @param string redeemCode - redeemCode * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberRedeemCodeByRedeemCode (

            parameters : {
              'redeemCode'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 文档 4.1
        * @method
        * @name 文档 4.1
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberRedeemCodeBatchAdd (

            parameters : {
              'body'  : PayMemberRedeemCodeBatchReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 取消完成新增付费会员兑换码批次
        * @method
        * @name 取消完成新增付费会员兑换码批次
        * @param  body - batchCodes * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberRedeemCodeBatchCancelCompleteAdd (

            parameters : {
              'body'  : Array<string>,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 完成新增付费会员兑换码批次
        * @method
        * @name 完成新增付费会员兑换码批次
        * @param  body - batchCodes * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberRedeemCodeBatchCompleteAdd (

            parameters : {
              'body'  : Array<string>,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 删除付费会员兑换码批次
        * @method
        * @name 删除付费会员兑换码批次
        * @param  body - batchCodes * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberRedeemCodeBatchDelete (

            parameters : {
              'body'  : Array<string>,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 右模糊查询批次号或批次名称
        * @method
        * @name 右模糊查询批次号或批次名称
        * @param string batchCodeKey - batchCodeKey * @param integer size - size * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberRedeemCodeBatchFuzzyQueryList (

            parameters : {
              'batchCodeKey'  ?: string,
              'size'  ?: number,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询付费会员兑换码批次详情
        * @method
        * @name 查询付费会员兑换码批次详情
        * @param string batchCode - batchCode * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberRedeemCodeBatchInfoByBatchCode (

            parameters : {
              'batchCode'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 文档 4.5 分页
        * @method
        * @name 文档 4.5 分页
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberRedeemCodeBatchList (

            parameters : {
              'body'  : PayMemberQueryRedeemCodeBatchReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 文档 4.3
        * @method
        * @name 文档 4.3
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberRedeemCodeBatchReview (

            parameters : {
              'body'  : PayMemberReViewRedeemCodeBatchReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 文档 4.4
        * @method
        * @name 文档 4.4
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberRedeemCodeBatchStop (

            parameters : {
              'body'  : PayMemberStopRedeemCodeBatchReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 文档 4.2
        * @method
        * @name 文档 4.2
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberRedeemCodeBatchUpdate (

            parameters : {
              'body'  : PayMemberRedeemCodeBatchReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询兑换码详情批次列表
        * @method
        * @name 查询兑换码详情批次列表
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberRedeemCodeDetailList (

            parameters : {
              'body'  : PayMemberQueryRedeemCodeDetailReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * reviewPayMemberRefund
        * @method
        * @name reviewPayMemberRefund
        * @param  body - payMemberReviewRefundReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberReviewRefund (

            parameters : {
              'body'  : PayMemberReviewRefundReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询可销售
        * @method
        * @name 查询可销售
        * @param  body - saleableReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberSelectCanSales (

            parameters : {
              'body'  : PayMemberSaleableReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询付费会员购买单
        * @method
        * @name 查询付费会员购买单
        * @param string buyNum - buyNum * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1PayMemberSelectPayMemberBuyOrderDetailByBuyNum (

            parameters : {
              'buyNum'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询付费会员购买单列表
        * @method
        * @name 查询付费会员购买单列表
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberSelectPayMemberBuyOrderList (

            parameters : {
              'body'  : PayMemberBuyOrderReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * selectPayMemberInfo
        * @method
        * @name selectPayMemberInfo
        * @param integer memberId - memberId * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1PayMemberSelectPayMemberInfoByMemberId (

            parameters : {
              'memberId'  : number,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询会员邀请已兑换记录
        * @method
        * @name 查询会员邀请已兑换记录
        * @param integer memberId - memberId * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1PayMemberSelectPayMemberRedeemRecordByMemberId (

            parameters : {
              'memberId'  : number,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * selectPayMemberWillExpire
        * @method
        * @name selectPayMemberWillExpire
        * @param  body - payMemberWillExpireReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberSelectPayMemberWillExpireTemp (

            parameters : {
              'body'  : PayMemberWillExpireReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 更新付费会员月度礼包套餐值
        * @method
        * @name 更新付费会员月度礼包套餐值
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberUpdatePayMemberSetPackageValue (

            parameters : {
              'body'  : PayMemberSetPackageValueReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 新增付费会员类型
        * @method
        * @name 新增付费会员类型
        * @param  body - addReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberTypeAdd (

            parameters : {
              'body'  : PayMemberTypeReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 新增付费会员类型时，需要用到此接口
        * @method
        * @name 新增付费会员类型时，需要用到此接口
        * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1PayMemberTypeSelectAllPayMemberIdentify (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 新增付费会员类型时，需要用到此接口
        * @method
        * @name 新增付费会员类型时，需要用到此接口
        * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1PayMemberTypeSelectAllPayMemberPeriod (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询不同身份的付费会员类型,列表，默认[上架]
        * @method
        * @name 查询不同身份的付费会员类型,列表，默认[上架]
        * @param integer identifyCode - identifyCode * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1PayMemberTypeSelectAllPayMemberTypeDefaultUpByIdentifyCode (

            parameters : {
              'identifyCode'  : number,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询付费会员类型详情
        * @method
        * @name 查询付费会员类型详情
        * @param integer typeCode - typeCode * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1PayMemberTypeSelectPayMemberTypeByTypeCode (

            parameters : {
              'typeCode'  : number,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据条件查询所有付费会员类型
        * @method
        * @name 根据条件查询所有付费会员类型
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberTypeSelectPayMemberTypes (

            parameters : {
              'body'  : PayMemberTypeSearchConditionReqDTO,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 上下架付费会员类型
        * @method
        * @name 上下架付费会员类型
        * @param  body - shelvesReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberTypeShelves (

            parameters : {
              'body'  : PayMemberTypeShelvesReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 更新付费会员类型
        * @method
        * @name 更新付费会员类型
        * @param  body - updateReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1PayMemberTypeUpdate (

            parameters : {
              'body'  : PayMemberTypeUpdateReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * registerMember
        * @method
        * @name registerMember
        * @param  body - memberNoCaptchaRegisterReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1Register (

            parameters : {
              'body'  : MemberNoCaptchaRegisterReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * registerMemberBatch
        * @method
        * @name registerMemberBatch
        * @param  body - memberNoCaptchaRegisterBatchReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1RegisterBatch (

            parameters : {
              'body'  : MemberNoCaptchaRegisterBatchReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * registerMemberSimple
        * @method
        * @name registerMemberSimple
        * @param  body - memberCaptchaRegisterReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1RegisterSimple (

            parameters : {
              'body'  : MemberCaptchaRegisterReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * registerTemp
        * @method
        * @name registerTemp
        * @param  body - memberCaptchaRegisterReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1RegisterTemp (

            parameters : {
              'body'  : MemberCaptchaRegisterReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * countByCondition
        * @method
        * @name countByCondition
        * @param  body - reqDTO * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1RelationCountByCondition (

            parameters : {
              'body'  : CountRelationMemberIdsReqDTO,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * createRelation
        * @method
        * @name createRelation
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1RelationCreateRelation (

            parameters : {
              'body'  : CreateRelationReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryByCondition
        * @method
        * @name queryByCondition
        * @param  body - reqDTO * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1RelationQueryByCondition (

            parameters : {
              'body'  : CountRelationMemberIdsReqDTO,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryEffectiveMemberIdsByParentMemberId
        * @method
        * @name queryEffectiveMemberIdsByParentMemberId
        * @param integer currentPage - 页号 * @param integer pageSize - 页容量 * @param integer parentMemberId - 父会员ID * @param string relationType - 关系类型 * @param boolean relationTypeInRange -  * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1RelationQueryEffectiveMemberIdsByParentMemberId (

            parameters : {
              'currentPage'  ?: number,
              'pageSize'  ?: number,
              'parentMemberId'  ?: number,
              'relationType'  ?: string,
              'relationTypeInRange'  ?: boolean,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryEffectiveParentMemberIdByMemberId
        * @method
        * @name queryEffectiveParentMemberIdByMemberId
        * @param integer memberId - 会员ID * @param string relationType - 关系类型 * @param boolean relationTypeInRange -  * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV1RelationQueryEffectiveParentMemberIdByMemberId (

            parameters : {
              'memberId'  ?: number,
              'relationType'  ?: string,
              'relationTypeInRange'  ?: boolean,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * unBindRelation
        * @method
        * @name unBindRelation
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1RelationUnBindRelation (

            parameters : {
              'body'  : UnBindRelationReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * releaseBehaviorLimit
        * @method
        * @name releaseBehaviorLimit
        * @param  body - releaseBehaviorLimitReq * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1ReleaseBehaviorLimit (

            parameters : {
              'body'  : ReleaseBehaviorLimitReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * createTemplate
        * @method
        * @name createTemplate
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1SmstemplateCreateTemplate (

            parameters : {
              'body'  : 创建短信模板入参,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryList
        * @method
        * @name queryList
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1SmstemplateQueryList (

            parameters : {
              'body'  : 查询短信模板入参,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryPageable
        * @method
        * @name queryPageable
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1SmstemplateQueryPageable (

            parameters : {
              'body'  : 查询短信模板入参,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * updateTemplate
        * @method
        * @name updateTemplate
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postMemberV1SmstemplateUpdateTemplate (

            parameters : {
              'body'  : 更新短信模板入参,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * selectPayMemberInfo
        * @method
        * @name selectPayMemberInfo
        * @param integer memberId - memberId * @param  x-pagoda-envoy-route-project -
        */
        function getMemberV2PayMemberSelectPayMemberInfoByMemberId (

            parameters : {
              'memberId'  : number,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 交行支付
        * @method
        * @name 交行支付
        * @param string billType -  * @param string consoleNum -  * @param string date -  * @param  x-pagoda-envoy-route-project -
        */
        function postPayV1BocmBill (

            parameters : {
              'billType'  ?: string,
              'consoleNum'  ?: string,
              'date'  ?: string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 交行支付
        * @method
        * @name 交行支付
        * @param  body - bocmPayDTO * @param  x-pagoda-envoy-route-project -
        */
        function postPayV1BocmPay (

            parameters : {
              'body'  : BocmPayDTO,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 交行支付
        * @method
        * @name 交行支付
        * @param  body - bocmPayBaseDTO * @param  x-pagoda-envoy-route-project -
        */
        function postPayV1BocmPayQuery (

            parameters : {
              'body'  : BocmPayBaseDTO,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 交行支付
        * @method
        * @name 交行支付
        * @param  body - bocmRefundDTO * @param  x-pagoda-envoy-route-project -
        */
        function postPayV1BocmRefund (

            parameters : {
              'body'  : BocmRefundDTO,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 交行支付
        * @method
        * @name 交行支付
        * @param  body - bocmRefundBaseDTO * @param  x-pagoda-envoy-route-project -
        */
        function postPayV1BocmRefundQuery (

            parameters : {
              'body'  : BocmRefundBaseDTO,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryRedPack
        * @method
        * @name queryRedPack
        * @param  body - info * @param  x-pagoda-envoy-route-project -
        */
        function postRedpackV1Query (

            parameters : {
              'body'  : QueryRedPackRecordReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * sendRedPack
        * @method
        * @name sendRedPack
        * @param  body - info * @param  x-pagoda-envoy-route-project -
        */
        function postRedpackV1Send (

            parameters : {
              'body'  : SendHbReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 关闭交易
        * @method
        * @name 关闭交易
        * @param  body - closeTradeOrderReq * @param  x-pagoda-envoy-route-project -
        */
        function postTradeV1OrderClose (

            parameters : {
              'body'  : CloseTradeOrderReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 创建交易
        * @method
        * @name 创建交易
        * @param  body - createTradeOrderReq * @param  x-pagoda-envoy-route-project -
        */
        function postTradeV1OrderCreate (

            parameters : {
              'body'  : CreateTradeOrderReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 预创建交易
        * @method
        * @name 预创建交易
        * @param  body - preCreateTradeOrderReq * @param  x-pagoda-envoy-route-project -
        */
        function postTradeV1OrderPrecreate (

            parameters : {
              'body'  : PreCreateTradeOrderReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * partRefund
        * @method
        * @name partRefund
        * @param  body - partRefundReqDTO * @param  x-pagoda-envoy-route-project -
        */
        function postTradeV1PartRefund (

            parameters : {
              'body'  : PartRefundReqDTO,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 交易支付
        * @method
        * @name 交易支付
        * @param  body - transactionPaymentReq * @param  x-pagoda-envoy-route-project -
        */
        function postTradeV1Pay (

            parameters : {
              'body'  : TransactionPaymentReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 撤销支付
        * @method
        * @name 撤销支付
        * @param  body - request * @param  x-pagoda-envoy-route-project -
        */
        function postTradeV1PayCancel (

            parameters : {
              'body'  : CancelPaymentReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getPayStatusByPayNo
        * @method
        * @name getPayStatusByPayNo
        * @param string payNo - payNo * @param  x-pagoda-envoy-route-project -
        */
        function getTradeV1PayStatusByPayNo (

            parameters : {
              'payNo'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 购物结算
        * @method
        * @name 购物结算
        * @param  body - shoppingSettlementReq * @param  x-pagoda-envoy-route-project -
        */
        function postTradeV1SettleShopping (

            parameters : {
              'body'  : ShoppingSettlementReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * cancellationTrade
        * @method
        * @name cancellationTrade
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postTradeV1TradeCancel (

            parameters : {
              'body'  : TradeCancelReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

      }
  }
}
export {}

