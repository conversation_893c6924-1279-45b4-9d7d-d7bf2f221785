const {axios} = require('../../../common');

describe('POST /coupon/fruit/v1/redeemCoupon', () => {
  /*
  it('should redeem a coupon with valid data', async () => {
    const requestBody = {
      customerID: 123456,
      couponDetailCode: '123456789012345678',
      cityID: 1,
    };

    const response = await axios.post('/coupon/fruit/v1/redeemCoupon', requestBody);

    expect(response.status).toBe(200);
    expect(response.data).toBeDefined();
  });
  */

  it('should return an error with invalid data', async () => {
    const requestBody = {
      customerID: 'invalid',
      couponDetailCode: 'invalid',
      cityID: 'invalid',
    };

    try {
      await axios.post('/coupon/fruit/v1/redeemCoupon', requestBody);
    } catch (error) {
      // console.log(error);
      expect(error.response.status).toBe(500);
      expect(error.response.data).toBeDefined();
    }
  });
});
