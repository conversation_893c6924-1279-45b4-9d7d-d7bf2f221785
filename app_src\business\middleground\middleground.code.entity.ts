import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm'

@Entity('t_gen_code')
export class MGCode {
  @PrimaryGeneratedColumn()
  id: number

  @Column('varchar', { length: 75, comment: 'host+basePath' })
  mgservice: string

  @Column('int', { width: 11 })
  version: number

  @Column('mediumtext', { comment: 'declare namespace API{}' })
  dts_content: string

  @Column('mediumtext')
  js_content: string

  @Column('tinyint', { default: 0, width: 2, comment: '0:未删除 1:已删除' })
  is_delete: number

  @Column('varchar', { length: 50, default: 'prod', comment: 'draft dev test uat drill prod' })
  env: string
}
