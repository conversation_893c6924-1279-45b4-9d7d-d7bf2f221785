//将代码业务代码编译包起来 并传到 loader文件夹里面
const fs = require('fs-extra')
const path = require('path')
// @ts-ignore
var project_config = require('../src_business/serverless.config')
console.log(project_config)

var project_name = project_config.project_name

var from_dir = path.resolve(__dirname, '../src_business', project_config.project_code_dir)

// @ts-ignore
var to_dist_business = path.resolve(__dirname, '../dist/loaded_function_code', process.env.RUNTIME_ENV, project_name)
fs.ensureDirSync(to_dist_business)

//把所有文件复制进去 并将js文件改写包起来
fs.copySync(from_dir, to_dist_business)
overWriteFileContent('.', to_dist_business)

/**
 * @param {string} filename
 * @param {string} dir */
function overWriteFileContent(filename, dir) {
  var location = path.resolve(dir, filename)
  if (fs.statSync(location).isDirectory()) {
    var items = fs.readdirSync(location)
    for (let index = 0; index < items.length; index++) {
      const element = items[index]
      overWriteFileContent(element, path.resolve(dir, filename))
    }
  } else {
    //如果是文件，修改文件内容
    if (!filename.endsWith('.js')) {
      return
    }
    var file_content = fs.readFileSync(location)
    fs.writeFileSync(
      location,
      `var __serverless_require=require;require=function(url){  if(url.indexOf('.') === 0){__serverless_require(url).__serverless_runtime_export_function(m_req,m_res,m_API,m_RUNTIME_ENV,m_xlog,m_redisCache,m_next,m_getAPI,m_middlewareCallback);return __serverless_require(url)}else{    return __serverless_require(url)}};var m_req; var m_res; var m_API; var m_RUNTIME_ENV; var m_xlog; var m_redisCache; var m_next; var m_getAPI; var m_middlewareCallback;exports.__serverless_runtime_export_function=function(req,res,API,RUNTIME_ENV,xlog,redisCache,next,getAPI,middlewareCallback){m_req = req; m_res = res; m_API = API; m_RUNTIME_ENV = RUNTIME_ENV; m_xlog = xlog; m_redisCache = redisCache; m_next = next; m_getAPI = getAPI; m_middlewareCallback = middlewareCallback;` +
        file_content +
        '\n}'
    )
  }
}
