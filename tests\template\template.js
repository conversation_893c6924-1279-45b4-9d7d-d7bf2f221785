import express from "express";
import cors from 'cors'
// console.log('/gupiao/list')
const app = express();
app.use(cors({
  origin: true,
  methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS,CONNECT,TRACE',
  preflightContinue: false,
  optionsSuccessStatus: 204,
  credentials: true,
  exposedHeaders: '*'
}))
app.use(express.static('static'))
const port = process.env.port || 4000;
app.set("trust proxy", 1);
app.listen(port, () => {
  console.log(`Example app listening on port ${port}`);
});

const middleware = function (req, res, next) {
  const axios = require("axios").default;
  const REQUEST_TIMEOUT = 10000 // 超时时间
  // 记录请求次数
  if (!API.requestCount) {
    API.requestCount = 0
  }
  class RequestTask {
    constructor () {

      // this.requestId = new Date().getTime().toString(16) + Math.random().toString(16).substring(2,10)
      // this.requestCount =  API.requestCount
    }
    add() {
      // this.requestTasks.add(this.requestId)
      API.requestCount++
      xlog.info('request count', req.url, API.requestCount)
    }
    delete() {
      // this.requestTasks.delete(this.requestId)
      API.requestCount--
    }
  }
  const requestTask = new RequestTask()
  requestTask.add()
  const http = axios.create({
    timeout: REQUEST_TIMEOUT,
    // 开发环境在这里设置代理
    /*
    proxy: {
      host: '127.0.0.1',
      port: 8888
    }
    */
  });
  http.interceptors.request.use( config => {
    config.requestTime = new Date()
    return config
  })

  http.interceptors.response.use( response => {
    response.config.responseTime = new Date()
    return response
  })
  const Redis = require("ioredis");
  const _ = require("lodash");
  const Qs = require('querystring')
  const {
    appDownloadCouponConfig,
    couponChannelMap,
    prdPicDomain,
    wxminAppid,
  } = require("../common/const/other").otherConst;
  const {
    errorEnum,
    redisConfig,
    apiResKeyMap,
    dominConfig,
    decryptPath,
    decryptByTokenPath
  } = require("../common/const/request").requestConst;
  const { h5DomainConfig } = require("../common/const/h5");
  const {
    encrypt,
    getDecryptData,
    getEncryptKey
  } = require("../common/services/crypto");
  const { resDataFormat } = require("../common/services/resDataFormat");
  const { checkInterfaceAuth } = require("../common/services/checkInterfaceAuth");
  const { verifyJWTAuthorization } = require('../common/services/jwt')
  const { externalConfig, interfaceAuthWhiteList, ipWhiteList } = require('../common/const/externalConfig')
  const {getKeyPrefix} = require("../common/utils/redisHelper");
  const codeKey = "errorCode";
  const messageKey = "description";
  const dataKey = "data";
  const pageKey = "page";
  const successCode = 0;

  // APP下载引导券批次号
  req.$appDownloadCouponBatchNum =
    appDownloadCouponConfig[RUNTIME_ENV] || appDownloadCouponConfig.test_exc;

  // 优惠券渠道配置
  req.$couponChannelMap = couponChannelMap;

  // 错误类型枚举
  req.$ERROR_CODE = errorEnum;

  req.$http = function (method, url, data = {}, config, type) {
    const axiosConfig = {
      ...config,
      method,
      url
    };
    if (method.toLowerCase() === "get") {
      axiosConfig.params = data;
    } else {
      axiosConfig.data = data;
    }
    const headers = axiosConfig.headers || {};
    axiosConfig.headers = {
      ...req.requestHeaders,
      ...headers,
      ...res.locals.logtubeHeaders
    };

    const isEshop = type === 'eshop'
    const isAPIPath = url.includes('api')
    const headersNotToken = !axiosConfig.headers.userToken

    if (isEshop && isAPIPath && headersNotToken) {
      //  增加此参数后，无需传递token鉴权参数
      axiosConfig.headers.eshopRequestKey = 'a2r1201f-3245-1q3d-1a30-a3e1g123q319'
    }

    return http(axiosConfig);
  };

  // H5业务域名配置
  req.$h5DomainConfig = h5DomainConfig;

  req.$prdPicDomain = prdPicDomain;

  // 检测值是否为空
  req.$isEmpty = function (target) {
    if (_.isUndefined(target) || _.isNull(target)) {
      return true;
    } else if (_.isString(target)) {
      return target.trim().length === 0;
    } else if (_.isObject(target)) {
      return Object.keys(target).length === 0;
    } else if (_.isArray(target)) {
      return target.length === 0;
    }
    return false;
  };

  // 检测对象中的某些属性是否为空 example：req.$checkParamsEmpty({ a: null }, ["a"])
  req.$checkParamsEmpty = function (object, props) {
    props.forEach(prop => {
      const value = object[prop];

      if (req.$isEmpty(value)) {
        throw new req.$ApiException(
          `缺少参数${prop}字段`,
          req.$ERROR_CODE.PARAMS_INVALID
        );
      }
    });
  };

  req.$ApiException = class ApiException extends Error {
    constructor (msg, code, error) {
      super();

      this.message = msg;
      this.code = code;
      this.error = {
        name: "ApiException",
        ...error
      };
    }
  };

  req.$TokenException = class TokenException extends req.$ApiException {
    constructor (msg, desc) {
      super(msg, req.$ERROR_CODE.LOGIN_INVALID, {
        origin: "middleware checkToken",
        message: desc
      });
    }
  };

  req.$ServiceException = class ServiceException extends Error {
    constructor (error) {
      super();
      this.error = error;
    }
  };

  // 解构 api 返回数据并捕获 api 异常
  req.$execute = async function (
    apiPromise,
    origin = "未知",
    resCodeKey = codeKey,
    resMsgKey = messageKey,
    resDataKey = dataKey,
    resSuccessCode = successCode,
    isRuturnError = false, // http请求正常的情况下，是否返回中台接口逻辑异常错误
    isRuturnServiceError = false, // 是否返回http请求异常
    type = ''
  ) {
    try {
      const res = await apiPromise;
      collectReqData(res.config);
      const data = res.data;
      const domainKey = _.findKey(req.dominConfig, domain =>
        _.includes(res.config.url, domain)
      );
      if (domainKey) {
        // 格式化接口返回的数据（某些接口缺少errorCode等信息）
        const resDataHandle = resDataFormat[domainKey];
        resDataHandle &&
          typeof resDataHandle === "function" &&
          Object.assign(data, resDataHandle(data));

        //支付宝接口返回格式处理
        if(type === 'alipay'){
          const alipayParams = Qs.parse(origin.split("?")[1]) || {}
          const { method = 'alipay.system.oauth.token' } = alipayParams
          resDataKey = method.split(".").join("_") + "_response"
          const apipayData = data[resDataKey] || data.error_response;
          const alipayCode = apipayData.code || "10000";
          const isFail = alipayCode !== "10000";
          _.assign(data,{
            code: isFail ? alipayCode : 0,
            message:isFail ? `${apipayData.sub_code}: ${apipayData.sub_msg}` : "请求成功",
          })
        }
      }
      const {
        [resCodeKey]: code,
        [resMsgKey]: msg,
        [resDataKey]: resData,
        [pageKey]: pageData = null,
        error
      } = data;
      if (code === resSuccessCode) {
        if (
          !req.$isEmpty(pageData) &&
          !req.$isEmpty(resData) &&
          _.isObject(resData)
        ) {
          resData[pageKey] = pageData;
        }
        return resData;
      } else if (isRuturnError) {
        return {
          [resCodeKey]: code,
          [resDataKey]: resData,
          [resMsgKey]: msg
        };
      } else {
        throw new req.$ApiException(msg, code, error);
      }
    } catch (e) {
      xlog.info("errorOrigin", e)
      if (type === 'customerCoupon') {
        // 针对卡券2.0，把中台返回400/500状态码的信息返回
        const status = _.get(e, 'response.status')
        if (status === 400 || status === 500) {
          const { code, msg } = _.get(e, 'response.data')
          if (isRuturnError) {
            return {
              [resCodeKey]: code,
              [resMsgKey]: msg
            };
          } else {
            const originUrl = _.get(e, 'config.url', '')
            throw new req.$ApiException(msg, code, { origin: originUrl })
          }
        }
      }
      const errorOrigin = e.error && e.error.origin ? e.error.origin : [];
      // 小于默认超时时间则不抛出异常自定义处理
      if (e.config && (e.config.timeout < REQUEST_TIMEOUT)) {
        return {
          [resCodeKey]: 408,
          [resDataKey]: null,
        }
      }
      errorOrigin.push(origin);
      const status = _.get(e, "response.status", 0);
      if (status && (status >= 500 && status < 600)) {
        throw new req.$ApiException(`${status}`, 999, {
          origin: errorOrigin
        });
      } else if (e.constructor === req.$ApiException) {
        throw new req.$ApiException(e.message, e.code, {
          origin: errorOrigin
        });
      } else {
        const msg = _.get(e, "response.data.error", undefined) || e.message
        const serviceError = {
          origin: errorOrigin,
          message: msg
        }
        if (!isRuturnServiceError) { // 不需要返回Service异常错误
          throw new req.$ServiceException(serviceError);
        }
        if (status && status < 400) { // 服务器超时，没有response响应，status会不存在（为0时），
          throw new req.$ServiceException(serviceError);
        }
        return _.assign(serviceError, {
          status
        })
      }
    }
  };

  // http 调用 API
  req.$executeHttp = async function (opt) {
    const type = opt.type || "middle"
    const {
      method,
      url,
      data,
      config,
      isRuturnError,
      isRuturnServiceError,
      apiPromise = req.$http(method, url, data, config, type),
      apiDesc
    } = opt;
    if (!apiResKeyMap[type]) {
      throw new req.$ServiceException({
        origin: "middleware $executeHttp函数",
        message: `${type}接口类型不存在`
      });
    }
    // 解析返回的数据结构并统一
    const { successCode, codeKey, messageKey, dataKey, desc } = apiResKeyMap[
      type
    ];

    return await req.$execute(
      apiPromise,
      `${desc}API：${apiDesc || url}`,
      codeKey,
      messageKey,
      dataKey,
      successCode,
      isRuturnError,
      isRuturnServiceError,
      type
    );
  };

  // post调用方式
  req.$executeHttp.post = async function (opt) {
    return await req.$executeHttp({
      ...opt,
      method: "post"
    });
  };

  // get调用方式
  req.$executeHttp.get = async function (opt) {
    return await req.$executeHttp({
      ...opt,
      method: "get"
    });
  };

  // 注入方式调用中台 API
  req.$executeApi = async function (apiPromise, apiDesc = "未知") {
    return await req.$executeHttp({
      apiPromise,
      apiDesc
    });
  };

  req.$executeMain = async function (main, returnRes = true) {
    try {
      const result = await main();
      if (returnRes) {
        logInfo();
        const {
          data,
          description = "请求成功",
          errorCode = 0,
          isNeedEncrypt = false
        } = result;
        const { isEncryptRes = false, resData } = await getEncryptResult({
          data,
          isNeedEncrypt
        });
        requestTask.delete()
        return res.json({
          isEncryptRes,
          systemTime: Date.now(),
          [codeKey]: errorCode,
          [dataKey]: resData,
          [messageKey]: description,
          crid: _.get(res, "locals.crid", "")
        });
      }
    } catch (e) {
      requestTask.delete()
      const { code, message, error } = e;
      logInfo(message || "异常响应");
      if (
        e.constructor === req.$ApiException ||
        e.constructor === req.$TokenException
      ) {
        return res.json({
          crid: _.get(res, "locals.crid", ""),
          systemTime: Date.now(),
          [codeKey]: Number(code) || req.$ERROR_CODE.COMMON,
          [messageKey]: message || "请求异常，请稍后重试",
          error
        });
      } else {
        const err =
          e.constructor === req.$ServiceException
            ? error
            : {
              name: e.name,
              message: e.message || "请求异常，请稍后重试",
              stack: e.stack
            };
        xlog.error(`${req.protocol}://${req.headers.host}${req.path}`, typeof err === 'object' ? JSON.stringify(err) : err);
        return res.json({
          crid: _.get(res, "locals.crid", ""),
          systemTime: Date.now(),
          [codeKey]: req.$ERROR_CODE.SERVICE_INTERNAL_ERROR,
          [messageKey]: "服务异常",
          error: err
        });
      }
    }
  };

  req.$validateParamsByJoi = function ({ schema, data }) {
    const { error } = schema.validate(data);

    if (error) {
      const [errorDetail] = error.details;
      const prop = errorDetail.context && errorDetail.context.label;

      if (prop) {
        throw new req.$ApiException(
          `接口${prop}参数异常`,
          req.$ERROR_CODE.PARAMS_INVALID,
          errorDetail
        );
      } else {
        throw new req.$ApiException(
          "接口参数异常",
          req.$ERROR_CODE.PARAMS_INVALID,
          errorDetail
        );
      }
    }
  };
  next()
};

app.use(middleware)

app.get("/coupon/fruit/v1/redeemCoupon", (req, res) => {
  /**
   * @desc 兑换优惠券
   *  历史逻辑：
   *  如果输入的是数字，并且长度是4位或者6位，则兑换门店推荐码
      如果输入的是数字，并且长度是18位，则直接调用会员兑换优惠券
      如果输入是字符串，就兑换优惠口令
  */
  const { $executeMain, $validateParamsByJoi, $executeHttp } = req
  const Joi = require('joi')
  const _ = require('lodash')
  const { couponApi2 } = require('../../../common/services/coupon-v3')

  const schema = Joi.object({
    customerID: Joi.alternatives() //用户ID
      .try(Joi.number(), Joi.string())
      .required(),
    couponDetailCode: Joi.alternatives() //券明细编码
      .try(Joi.number(), Joi.string())
      .required(),
    cityID: Joi.alternatives() //城市ID
      .try(Joi.number(), Joi.string())
  })
    .unknown()
    .required()

  async function main() {
    $validateParamsByJoi({ schema, data: req.body })

    const { customerID, couponDetailCode } = req.body || {}

    const length = (String(couponDetailCode) || '').length
    let result
    if (length === 18 || length === 21) {
      result = await couponApi2.redeemCoupon({ customerID, couponDetailCode })
      const { code = '', msg, description } = result || {}
      if (code !== '') {
        throw new req.$ApiException(msg || description, req.$ERROR_CODE.SERVICE_INTERNAL_ERROR, {})
      } else {
        xlog.info(`用户${customerID}兑换优惠券${couponDetailCode}，卡券接口返回成功，返回信息：`, JSON.stringify(result))
      }
    } else {
      result = await redeemOther()
      const { errorCode = 0, description } = result || {}
      if (errorCode !== 0) {
        throw new req.$ApiException(description, req.$ERROR_CODE.SERVICE_INTERNAL_ERROR, {})
      } else {
        xlog.info(`用户${customerID}兑换优惠券${couponDetailCode}，电商接口返回成功，返回信息：`, JSON.stringify(result))
      }
    }

    return {
      data: result
    }
  }

  async function redeemOther() {
    const { cityID, customerID, couponDetailCode } = req.body || {}
    const url = `${req.dominConfig.ms_customer}/coupon/redeem`
    const params = {
      cityID,
      customerID,
      privilegeCode: couponDetailCode
    }

    const data = await $executeHttp.post({
      url,
      data: params,
      type: 'eshop',
      isRuturnError: true,
      isReturnError: true
    })

    return data
  }

  $executeMain(main)

})
