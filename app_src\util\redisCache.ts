import config from '../config'
import Redis, { KeyType, ValueType } from 'ioredis'
import Log from './Log'

const redisClient = new Redis({ ...config.redis, keyPrefix: 'runtime_cache:' })
redisClient.connect(() => Log.log('redis runtime cache connected'))

class RedisCache {
  keyPrefix: string
  expireTime: number
  maxExpire: number
  env: string
  // maxExpire seconds
  constructor(keyPrefix: string, env?: string) {
    if (!keyPrefix) {
      throw new Error('keyPrefix must be provide ')
    }

    // default one day in seconds
    this.expireTime = 24 * 60 * 60
    this.keyPrefix = keyPrefix
    this.env = env ?? ''
  }
  getKey(name: KeyType, projectName?: string) {
    return `${projectName ? projectName : this.keyPrefix}${this.env ? ':' + this.env : ''}:${name}`
  }
  async get(name: KeyType, otherProjectName?: string) {
    const key = this.getKey(name, otherProjectName)
    const result = await redisClient.get(key)
    if (result) {
      try {
        return JSON.parse(result)
      } catch (error) {
        Log.error(error)
      }
    }
    return result
  }
  async set(name: KeyType, value: ValueType | object, seconds = this.expireTime, projectName?: string) {
    const key = this.getKey(name, projectName)
    const result = JSON.stringify(value)
    return await redisClient.set(key, result, 'EX', seconds)
  }
  async del(name: KeyType, projectName?: string) {
    const key = this.getKey(name, projectName)
    return await redisClient.del(key)
  }
  async expire(name: KeyType, seconds = this.expireTime, projectName?: string) {
    const key = this.getKey(name, projectName)

    return await redisClient.expire(key, seconds)
  }
}

export default redisClient
export { RedisCache, redisClient }
