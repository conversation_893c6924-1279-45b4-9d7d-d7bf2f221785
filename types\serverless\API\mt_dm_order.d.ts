/*this is generated code */
import  { AxiosResponse } from 'axios'
declare global{
  namespace API {
    /** mt-dm-order
      * 文档链接：http://op.pagoda.com.cn/?#/technical-doc
      * {"description":"order domain rest api","version":"1.0-SNAPSHOT","title":"1.0-SNAPSHOT"}
      * dm-order.kd1.pagoda.com.cn
    */
    namespace mt_dm_order {

        type Activity = {

            activityCode?:   string

            activityName?:   string

            activityType?:   number

            couponCode?:   string

            discount?:   number

            goodsList?:    Array < string >

            id?:   number

            mainOrderNo?:   string

            type?:   string

        }

        type AfterSaleOrderAuditReq = {

            afterSaleOrderNo?:   string

            auditRemark?:   string

            auditor?:   string

            status?:   number

            subOrderNo?:   string

        }

        type AfterSaleOrderItemReq = {

            afterSaleProof?:    Array < string >

            applyRefundAmount?:   number

            damageNum?:   number

            goodsId?:   number

            itemSnapshotId?:   number

            lackNum?:   number

            refundProof?:    Array < string >

            swapNum?:   number

        }

        type AfterSaleOrderReq = {

            afterSaleFrom?:   number

            afterSaleType?:   number

            applicant?:   string

            applyTime?:   string

            customerType?:   number

            description?:   string

            items?:    Array < AfterSaleOrderItemReq >

            memberCode?:   string

            mobile?:   string

            orgCode?:   string

            reason?:   string

            subOrderNo?:   string

        }

        type AfterSaleRefundChangeReq = {

            afterSaleOrderNo?:   string

            realAmount?:   number

        }

        type AntiFraudRequest = {

            payloads?:    Array < RequestField >

        }

        type ApiResult = {

            data?:   any

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultEnums = {

            data?:  Enums

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultMapstringobject = {

            data?:   any

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPagodaOrderRes = {

            data?:  PagodaOrderRes

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPeriodOrderRes = {

            data?:  PeriodOrderRes

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultRefundCreateRes = {

            data?:  RefundCreateRes

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultRefundOrderRes = {

            data?:  RefundOrderRes

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultSetstring = {

            data?:    Array < string >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultThirdOrderRes = {

            data?:  ThirdOrderRes

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultValidationThirdOrder = {

            data?:  ValidationThirdOrder

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultint = {

            data?:   number

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultobject = {

            data?:   any

            errorMsg?:   string

            resultCode:   number

        }

        type AssetInfo = {

            couponList?:    Array < CouponInfo >

            integralDeduction?:   number

        }

        type BasicItem = {

            goodsId?:   number

            id?:   number

            itemCode?:   string

            itemSnapshotId?:   number

            quantity?:   string

            salesPrice?:   number

            skuId?:   string

            subOrderNo?:   string

            weight?:   string

        }

        type BasicMember = {

            memberCode?:   string

            mobile?:   string

        }

        type BatchOrderOperateSyncReq = {

            changeTime?:   string

            channelNum?:   number

            operate?:   number

            subOrderNoList?:    Array < string >

        }

        type BlackCheckInfo = {

            clientIp?:   string

            customerAddr?:   string

            customerName?:   string

            customerPhone?:   string

            deviceNo?:   string

            latitude?:   string

            longitude?:   string

        }

        type ChangeOrderReq = {

            action?:   string

            dispatchEndDateTime?:   string

            dispatchStartDateTime?:   string

            leaderNote?:   string

            orderNo?:   string

        }

        type ChildrenBean = {

            desc?:   string

            value?:   number

        }

        type CommunityGroup = {

            cityCode?:   string

            customerCode?:   string

            distributionCenter?:   string

            id?:   number

            leaderCode?:   string

            leaderNote?:   string

            mainOrderNo?:   string

            mobileShortCode?:   string

            submittedTime?:   string

            v?:   string

            warehouseId?:   string

        }

        type Cost = {

            costAmount?:   number

            costCode?:   string

            standardAmount?:   number

        }

        type CouponInfo = {

            couponId?:   string

            discountAmount?:   number

        }

        type CreateOrderRequest = {

            orderNo?:   string

            payloads?:    Array < RequestField >

        }

        type Customer = {

            createdTime?:   string

            customerCode?:   string

            customerType?:   number

            id?:   number

            mainOrderNo?:   string

            mobile?:   string

            v?:   number

        }

        type DeferredResultApiResultEnums = {

            result?:   any

            setOrExpired?:   boolean

        }

        type Detail = {

            type?:   string

            value?:   string

        }

        type Enums = {

            enums?:    Array < EnumsBean >

        }

        type EnumsBean = {

            children?:    Array < ChildrenBean >

            type?:   string

        }

        type FixSnapShotIdReq = {

            subOrderNoList?:    Array < string >

        }

        type GroupActivity = {

            activityCode?:   string

            cancelTime?:   string

            changeTime?:   string

            endTime?:   string

            groupCode?:   string

            groupCount?:   number

            groupModel?:   number

            groupStatus?:   number

            id?:   number

            joinCount?:   number

            leaderOrderNo?:   string

            openTime?:   string

            successTime?:   string

            v?:   number

        }

        type Invoice = {

            email?:   string

            identifyNo?:   string

            money?:   number

            note?:   string

            remark?:   string

            status?:   number

            title?:   string

            titleType?:   string

            type?:   number

        }

        type Order = {

            activity?:    Array < Activity >

            attachChannelNo?:   string

            attachOrderNo?:   string

            brand?:   number

            channelNo?:   string

            channelNum?:   number

            communityGroup?:  CommunityGroup

            createDateTime?:   string

            customer?:  Customer

            customerCode?:   string

            discounts?:    Array < Detail >

            dispatchEndDateTime?:   string

            dispatchStartDateTime?:   string

            dispatchType?:   number

            employeeId?:   string

            groupActivity?:  GroupActivity

            id?:   number

            invoice?:  Invoice

            items?:    Array < BasicItem >

            mainOrderNo?:   string

            member?:  BasicMember

            orderChannel?:   number

            orderEntry?:   number

            orderModel?:   number

            orderNo?:   string

            orderStatus?:   number

            orderType?:   number

            orgCode?:   string

            payment?:  Payment

            paymentStatus?:   number

            periodOrders?:  PeriodOrder

            pickUpCode?:   string

            posDeviceNo?:   string

            preCreateTradeNo?:   string

            preTradeInfo?:  PrePayRequest

            processedMap?:   any

            receiver?:  Receiver

            refundNo?:   string

            resource?:   string

            saleModel?:   number

            source?:   string

            streamCode?:   string

            subOrders?:    Array < SubOrder >

            submittedTime?:   string

            syncEShopInfo?:  SyncEShopInfoReq

            thirdCustomers?:  ThirdCustomer

            tickNo?:   string

            tickType?:   number

            tradeNo?:   string

            turn?:   number

            v?:   number

            waybillSet?:    Array < Waybill >

        }

        type OrderDelayReq = {

            mainOrder?:   string

            subOrder?:   string

        }

        type OrderOperateSignReq = {

            customerCode?:   string

            customerType?:   number

            endTime?:   string

            orderStatus?:    Array < number >

            startTime?:   string

        }

        type OrderOperateSyncReq = {

            changeTime?:   string

            channelNum?:   number

            operate?:   number

            paramMap?:   any

            subOrderNo?:   string

        }

        type PagodaItem = {

            channelId?:   string

            goodsId?:   number

            goodsSn?:   string

            goodsTradeType?:   string

            goodsType?:   string

            id?:   number

            itemCode?:   string

            itemSnapshotId?:   number

            orgCode?:   string

            quantity?:   string

            salesPrice?:   number

            serviceList?:    Array < PagodaItem >

            skuId?:   string

            subOrderNo?:   string

            unitPrice?:   number

            weight?:   string

        }

        type PagodaMember = {

            memberCode?:   string

            memberId?:   number

            mobile?:   string

            xxvip?:   boolean

        }

        type PagodaOrderReq = {

            activity?:    Array < Activity >

            brand?:   number

            businessInfo?:   any

            consumptions?:    Array < Detail >

            dispatchEndDateTime?:   string

            dispatchStartDateTime?:   string

            dispatchType?:   number

            employeeId?:   string

            groupActivity?:  GroupActivity

            invoice?:  Invoice

            items?:    Array < PagodaItem >

            member?:  PagodaMember

            orderChannel?:   number

            orderEntry?:   number

            orderModel?:   number

            orderNote?:   string

            orderType?:   number

            orgCode?:   string

            orgName?:   string

            payment?:  Payment

            periodOrders?:  PeriodOrder

            pickUpCode?:   string

            posDeviceNo?:   string

            preCreateTradeNo?:   string

            preTradeInfo?:  PrePayRequest

            receiver?:  Receiver

            saleModel?:   number

            submittedTime?:   string

            syncEShopInfo?:  SyncEShopInfoReq

            tickNo?:   string

            tickType?:   number

            turn?:   number

            verification?:   string

        }

        type PagodaOrderRes = {

            mainOrderNo?:   string

            resMap?:   any

            subOrders?:    Array < SubOrder >

        }

        type PagodaRefundCreateReq = {

            additionReason?:   string

            customerComplaintType?:   number

            desc?:   string

            launchContent?:   string

            launchType?:   number

            picUrls?:    Array < string >

            reason?:   string

            refundItems?:    Array < PagodaRefundItem >

            refundModel?:   number

            refundMoney?:   number

            refundOperateAction?:   string

            refundOrderNo?:   string

            refundStatus?:   number

            refundTradeChannelNum?:   number

            refundTransNo?:   string

            refundType?:   number

            subOrderNo?:   string

            transNo?:   string

        }

        type PagodaRefundItem = {

            goodsId?:   number

            goodsSn?:   string

            itemName?:   string

            platformUndertake?:   number

            quantity?:   string

            refundAmount?:   number

            storeUndertake?:   number

            unit?:   string

            unitName?:   string

        }

        type PayedRes = {

            mainOrderNo?:   string

            paySuccess?:   boolean

            paymentNo?:   string

            paymentStatus?:   number

        }

        type Payment = {

            details?:    Array < Detail >

            payDateTime?:   string

            paymentType?:   string

            status?:   string

            tradeNo?:   string

        }

        type PeriodOrder = {

            content?:   string

            cronExpress?:   string

            cutTime?:   string

            endTime?:   string

            id?:   number

            limitCounter?:   number

            mainOrderNo?:   string

            nextTime?:   string

            orderIndex?:   number

            startTime?:   string

            type?:   number

        }

        type PeriodOrderReq = {

            delayTime?:   number

            mainOrderNo?:   string

            orderIndex?:   number

            subOrderNo?:   string

        }

        type PeriodOrderRes = {

        }

        type PrePayRequest = {

            assetInfo?:  AssetInfo

            blackCheckInfo?:  BlackCheckInfo

            brand?:   string

            channelNo?:   string

            checkedActivityList?:    Array < SimpleActivity >

            costList?:    Array < Cost >

            extensionInfo?:   string

            goodsList?:    Array < SimpleGoods >

            memberId?:   number

            memberIdentifyType?:   string

            merchantId?:   string

            orderAmount?:   number

            payAmount?:   number

            shopNo?:   string

            tradeScene?:   string

            tradeType?:   string

            uuid?:   string

        }

        type Receiver = {

            address?:   string

            addressNote?:   string

            city?:   string

            gender?:   number

            hometown?:   string

            id?:   number

            latitude?:   string

            longitude?:   string

            mobile?:   string

            name?:   string

            orderNote?:   string

            province?:   string

            secondPhone?:   string

            street?:   string

        }

        type RefundCreateRes = {

            order?:  Order

            orderType?:   number

            refundNo?:   string

        }

        type RefundOrderReq = {

            reason?:   string

            refundOperate?:   number

            refundOrderNo?:   string

            reviewContent?:   string

            reviewPlatform?:   number

            reviewTime?:   string

            subOrderNo?:   string

        }

        type RefundOrderRes = {

        }

        type RequestField = {

            fieldName?:   string

            value?:    Array < any >

        }

        type SimpleActivity = {

            activityCode?:   string

            goodsList?:    Array < string >

        }

        type SimpleGoods = {

            goodCode?:   string

            quantity?:   number

            serviceFeeList?:    Array < SimpleGoods >

            unitPrice?:   number

        }

        type StoreInfo = {

            address?:   string

            brandID?:   number

            deliveryTime?:   string

            distance?:   string

            isOpen?:   string

            lat?:   string

            lon?:   string

            name?:   string

            number?:   string

            openingTime?:   string

            phone?:   string

            shortName?:   string

            storeID?:   number

        }

        type SubOrder = {

            cancelTime?:   string

            changeTime?:   string

            customerCode?:   string

            defaultDispatchType?:   number

            endDispatchTime?:   string

            expCode?:   string

            finishTime?:   string

            id?:   number

            insteadOrgCode?:   string

            items?:    Array < BasicItem >

            mainOrderNo?:   string

            orderType?:   number

            orgCode?:   string

            paymentStatus?:   number

            pickUpCode?:   string

            prepareStockTime?:   string

            printBillTime?:   string

            realDispatchType?:   number

            receiver?:  Receiver

            receiverId?:   number

            startDispatchTime?:   string

            status?:   number

            streamCode?:   string

            subOrderNo?:   string

            submittedTime?:   string

            tickType?:   number

            v?:   number

        }

        type SyncEShopInfoReq = {

            chanID?:   string

            customerID?:   number

            distance?:   string

            fromSmartRetail?:   string

            preDepId?:   number

            receiverAddrID?:   string

            receiverCityID?:   string

            store?:  StoreInfo

            tradeNo?:   string

            tripDeliveryDistance?:   number

        }

        type ThirdCustomer = {

            customerCode?:   string

            customerName?:   string

            gender?:   number

            id?:   number

        }

        type ThirdItem = {

            costPrice?:   number

            desc?:   string

            externalCode?:   string

            gift?:   number

            goodsId?:   number

            id?:   number

            itemCode?:   string

            itemSnapshotId?:   number

            name?:   string

            quantity?:   string

            salesPrice?:   number

            sellType?:   string

            skuId?:   string

            subOrderNo?:   string

            unit?:   string

            unitDesc?:   string

            unitPrice?:   number

            volume?:   string

            weight?:   string

        }

        type ThirdMember = {

            anonymity?:   string

            gender?:   number

            level?:   number

            memberCode?:   string

            mobile?:   string

            name?:   string

            newCustomer?:   string

            storedShop?:   string

            thirdMemberCode?:   string

        }

        type ThirdOrderReq = {

            activity?:    Array < Activity >

            attachOrderNo?:   string

            brand?:   number

            channelNo?:   string

            consumptions?:    Array < Detail >

            discounts?:    Array < Detail >

            dispatchEndDateTime?:   string

            dispatchStartDateTime?:   string

            dispatchType?:   number

            employeeId?:   string

            invoice?:  Invoice

            items?:    Array < ThirdItem >

            member?:  ThirdMember

            orderChannel?:   number

            orderEntry?:   number

            orderModel?:   number

            orderNote?:   string

            orderType?:   number

            orgCode?:   string

            orgName?:   string

            payment?:  Payment

            periodOrders?:  PeriodOrder

            pickUpCode?:   string

            receiver?:  Receiver

            saleModel?:   number

            source?:   string

            streamCode?:   string

            submittedTime?:   string

            tickNo?:   string

            tickType?:   number

            verification?:   string

            waybill?:  Waybill

        }

        type ThirdOrderRes = {

            orderNo?:   string

        }

        type ThirdRefundCreateReq = {

            additionReason?:   string

            desc?:   string

            launchContent?:   string

            launchType?:   number

            picUrls?:    Array < string >

            reason?:   string

            refundItems?:    Array < ThirdRefundItem >

            refundModel?:   number

            refundMoney?:   number

            refundOperateAction?:   string

            refundOrderNo?:   string

            refundStatus?:   number

            refundTime?:   string

            refundType?:   number

            subOrderNo?:   string

            thirdRefundNo?:   string

            transNo?:   string

        }

        type ThirdRefundItem = {

            externalCode?:   string

            goodsId?:   number

            goodsSn?:   string

            itemName?:   string

            platformUndertake?:   number

            quantity?:   string

            refundAmount?:   number

            storeUndertake?:   number

            unit?:   string

            unitName?:   string

        }

        type ValidationThirdOrder = {

            msg?:   string

            status?:   string

        }

        type Waybill = {

            active?:   number

            arrivalTime?:   string

            assignRidersTime?:   string

            atStoreTime?:   string

            cancelTime?:   string

            changeTime?:   string

            createdTime?:   string

            deliveryCompany?:   string

            deliveryStaffMobile?:   string

            deliveryStaffName?:   string

            deliveryTime?:   string

            endStatus?:   boolean

            id?:   number

            receivingTime?:   string

            subOrderNo?:   string

            waybillNo?:   string

            waybillStatus?:   number

        }

        type WaybillStatusSyncReq = {

            changeTime?:   string

            deliveryCompany?:   string

            deliveryStaffMobile?:   string

            deliveryStaffName?:   string

            status?:   number

            statusDesc?:   string

            subOrderNo?:   string

            waybillNo?:   string

        }



      /**description
       * order domain rest api
       */

        /**
        * antiFraudCancelBlackList
        * @method
        * @name antiFraudCancelBlackList
        * @param  body - request * @param  x-pagoda-envoy-route-project -
        */
        function postAntifraudV1EshopCancelBlacklist (

            parameters : {
              'body'  : AntiFraudRequest,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * antiFraudCreateOrder
        * @method
        * @name antiFraudCreateOrder
        * @param  body - createRequests * @param  x-pagoda-envoy-route-project -
        */
        function postAntifraudV1EshopOrderCreate (

            parameters : {
              'body'  : Array<CreateOrderRequest>,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * antiFraudOrderSync
        * @method
        * @name antiFraudOrderSync
        * @param  body - request * @param  x-pagoda-envoy-route-project -
        */
        function postAntifraudV1EshopOrderSync (

            parameters : {
              'body'  : AntiFraudRequest,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * antiFraudCreateOrder
        * @method
        * @name antiFraudCreateOrder
        * @param  body - request * @param  x-pagoda-envoy-route-project -
        */
        function postAntifraudV1EshopRefundCreate (

            parameters : {
              'body'  : AntiFraudRequest,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * antiFraudOrderSync
        * @method
        * @name antiFraudOrderSync
        * @param  body - request * @param  x-pagoda-envoy-route-project -
        */
        function postAntifraudV1EshopRefundSync (

            parameters : {
              'body'  : AntiFraudRequest,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * check
        * @method
        * @name check
        * @param  x-pagoda-envoy-route-project -
        */
        function getCheck (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * dubboMock
        * @method
        * @name dubboMock
        * @param  x-pagoda-envoy-route-project -
        */
        function getDubboMock (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * fixOrderItemSnapshot
        * @method
        * @name fixOrderItemSnapshot
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postHotfixItemSnapShotId (

            parameters : {
              'body'  : FixSnapShotIdReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * auditAftersaleOrder
        * @method
        * @name auditAftersaleOrder
        * @param  body - aftersaleOrderAuditReqs * @param  x-pagoda-envoy-route-project -
        */
        function postOrderDomainV1AftersaleAudit (

            parameters : {
              'body'  : Array<AfterSaleOrderAuditReq>,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * afterSaleOrderCallback
        * @method
        * @name afterSaleOrderCallback
        * @param  body - aftersaleOrderAuditReqs * @param  x-pagoda-envoy-route-project -
        */
        function postOrderDomainV1AftersaleInvalidCallback (

            parameters : {
              'body'  : Array<AfterSaleOrderAuditReq>,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * modifyRefundAmount
        * @method
        * @name modifyRefundAmount
        * @param  body - aftersaleRefundChangeReqs * @param  x-pagoda-envoy-route-project -
        */
        function postOrderDomainV1AftersaleModifyAmount (

            parameters : {
              'body'  : Array<AfterSaleRefundChangeReq>,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * createAftersaleOrder
        * @method
        * @name createAftersaleOrder
        * @param  body - aftersaleOrdersReq * @param  x-pagoda-envoy-route-project -
        */
        function postOrderDomainV1AftersaleOrderCreate (

            parameters : {
              'body'  : Array<AfterSaleOrderReq>,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * createPagodaOrder
        * @method
        * @name createPagodaOrder
        * @param  body - pagodaOrderReq * @param  x-pagoda-envoy-route-project -
        */
        function postOrderDomainV1CreatePagodaOrder (

            parameters : {
              'body'  : PagodaOrderReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * creatPeriodOrder
        * @method
        * @name creatPeriodOrder
        * @param  body - periodOrderReq * @param  x-pagoda-envoy-route-project -
        */
        function postOrderDomainV1CreatePeriodOrder (

            parameters : {
              'body'  : PeriodOrderReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * createThirdOrder
        * @method
        * @name createThirdOrder
        * @param  body - thirdOrderReq * @param  x-pagoda-envoy-route-project -
        */
        function postOrderDomainV1CreateThirdOrder (

            parameters : {
              'body'  : ThirdOrderReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * changeOrderInfo
        * @method
        * @name changeOrderInfo
        * @param  body - changeOrderReq * @param  x-pagoda-envoy-route-project -
        */
        function postOrderDomainV1OrderChange (

            parameters : {
              'body'  : ChangeOrderReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * orderOperateBatchSign
        * @method
        * @name orderOperateBatchSign
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderDomainV1OrderOperateSignBatch (

            parameters : {
              'body'  : OrderOperateSignReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * orderStatusSync
        * @method
        * @name orderStatusSync
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderDomainV1OrderOperateSync (

            parameters : {
              'body'  : OrderOperateSyncReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * batchOrderStatusSync
        * @method
        * @name batchOrderStatusSync
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderDomainV1OrderOperateSyncBatch (

            parameters : {
              'body'  : BatchOrderOperateSyncReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * confirmPayment
        * @method
        * @name confirmPayment
        * @param  body - paymentReq * @param  x-pagoda-envoy-route-project -
        */
        function postOrderDomainV1PaymentOrder (

            parameters : {
              'body'  : PayedRes,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * checkPaymentTimeout
        * @method
        * @name checkPaymentTimeout
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderDomainV1PaymentTimeout (

            parameters : {
              'body'  : OrderDelayReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * generatePickUpCode
        * @method
        * @name generatePickUpCode
        * @param string subOrderNo - subOrderNo * @param  x-pagoda-envoy-route-project -
        */
        function getOrderDomainV1PickUpCodeGetBySubOrderNo (

            parameters : {
              'subOrderNo'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * createRefundOrderPagoda
        * @method
        * @name createRefundOrderPagoda
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderDomainV1RefundCreatePagoda (

            parameters : {
              'body'  : PagodaRefundCreateReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * createRefundOrderThird
        * @method
        * @name createRefundOrderThird
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderDomainV1RefundCreateThird (

            parameters : {
              'body'  : ThirdRefundCreateReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * refundStatusSynchronize
        * @method
        * @name refundStatusSynchronize
        * @param  body - refundOrderReq * @param  x-pagoda-envoy-route-project -
        */
        function postOrderDomainV1RefundSynchronize (

            parameters : {
              'body'  : RefundOrderReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * validateSupportedOrder
        * @method
        * @name validateSupportedOrder
        * @param  body - thirdOrderReq * @param  x-pagoda-envoy-route-project -
        */
        function postOrderDomainV1ValidationSupportedType (

            parameters : {
              'body'  : ThirdOrderReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * waybillStatusSync
        * @method
        * @name waybillStatusSync
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderDomainV1WaybillStatusSync (

            parameters : {
              'body'  : WaybillStatusSyncReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listConstants
        * @method
        * @name listConstants
        * @param  x-pagoda-envoy-route-project -
        */
        function getV1Enumeration (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

      }
  }
}
export {}

