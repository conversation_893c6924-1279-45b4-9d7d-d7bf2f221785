//由于构建机器的node版本为10 此库需要动态安装依赖 所以导致服务器的node版本也只能为10 但其他地方代码又用到了10以上的api
// 所以先注释掉 等部署到云服务器上的时候再开启
// require('xprofiler').start()

import hackhook, { tracingMiddleware } from './tracing/async_hook'
hackhook()

import { join, resolve } from 'path'
import express, { urlencoded, json } from 'express'

// middleware
import xmlParser from 'express-xml-bodyparser'
import cookieParser from 'cookie-parser'
import cors from 'cors'
import fileUpload from 'express-fileupload'

// custom middleware
import xLog from './util/logtube'
import session, { redisClient } from './middlewares/session.middleware'
import domainError from './middlewares/domain.middleware'
import genericErrorHandler from './middlewares/genericErrorHandler.middleware'
import notFoundHandler from './middlewares/notFoundHandler.middleware'
import timeout from './middlewares/timeout.middleware'
import conditionMiddleware from './middlewares/filter.middleware'

// routers
import user from './business/user/user.control'
import exc from './business/exc/exc.control'
import health from './business/health/health.control'

// services
import { MiddleGroundService } from './business/middleground/middleground.service'
import Log, { getProjectName } from './util/Log'
import { GlobalCopyCacheCodeAndCleanRequireCache } from './business/exc/exc.service'
import { createConnection } from 'typeorm'
import config from './config'
import { readdirSync, statSync, existsSync } from 'fs-extra'
console.log('process.env', process.env)

async function main() {
  // createConnection().then(async ormConnection => {
    global['GlobalCopyCacheCodeAndCleanRequireCache'] = GlobalCopyCacheCodeAndCleanRequireCache

    //0 下载中台代码
    // await require('../cli/download_api').main()
    // 1加载中台api jscode 加载到内存里
    // const mgService = new MiddleGroundService()
    // await mgService.loadMiddleGroundJSCode()
    // kd1 环境 草稿箱需要热更新代码
    // if (process.env.API_HOT_RELOAD == '1') {
    //   await mgService.onLoadMiddleGroundJScode()
    // }

    // 2加载业务代码
    //  npm run build

    // const projectService = new ProjectService()
    // await projectService.getProjectCasLogin()

    const app = express()

    app.get(['/', '/favicon.ico'], (req, res, next) => res.end('success'))
    app.use('/health', health)

    // view engine setup
    app.set('views', join(__dirname, 'views'))
    app.set('view engine', 'ejs')
    app.use(timeout(60))

    app.use(xLog.express())

    app.use('/', function(req, res, next) {
      // 这一行会让所有该 Logger 生成的日志事件追加 x_module  = "my_module" 字段
      res.locals.log.extras.module = getProjectName(req.originalUrl)
      next()
    })
    app.use(xLog.expressAccess())
    app.use(tracingMiddleware)
    app.use(
      cors({
        // 可设置白名单 https://github.com/expressjs/cors#configuration-options
        origin: true,
        methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS,CONNECT,TRACE',
        preflightContinue: false,
        optionsSuccessStatus: 204,
        credentials: true,
        exposedHeaders: '*'
      })
    )
    const ignoreCondition = 'raw_stream'
    // app.use(logger('dev'))
    app.use(conditionMiddleware(ignoreCondition, json({ limit: '50mb' })))

    app.use(conditionMiddleware(ignoreCondition, urlencoded({ extended: false, limit: '50mb' })))
    app.use(conditionMiddleware(ignoreCondition, fileUpload()))
    app.use(conditionMiddleware(ignoreCondition, cookieParser()))
    app.use(conditionMiddleware(ignoreCondition, xmlParser()))
    //@ts-ignore
    app.use(session(app))
    app.use(domainError)
    app.use(exc)
    app.use(user)
    // error handler middleware
    app.use(genericErrorHandler)

    // response middleware for 404 not found
    app.use(notFoundHandler)

    //启动钩子

    const dirs = readdirSync(resolve(__dirname, `../${config.CODE_PATH}`))

    for (let index = 0; index < dirs.length; index++) {
      const dir = dirs[index]
      //判断是不是文件夹
      var isExist = existsSync(resolve(__dirname, `../${config.CODE_PATH}/${dir}/serverless.config.js`))
      if (isExist) {
        var project_config = require(`../${config.CODE_PATH}/${dir}/serverless.config`)
        //如果钩子存在就加载钩子
        if (project_config.start) {
          await require(resolve(__dirname, `../${config.CODE_PATH}/${dir}`, project_config.start)).main(app)
          console.log(`${dir}启动钩子执行完成`)
        }
      }
    }

    const port = process.env.TENCENTCLOUD_SERVER_PORT || '3001'
    const server = app.listen(port, function() {
      Log.log('启动成功...端口：', port)
    })
    process.on('unhandledRejection', function(error) {
      Log.error(error)
    })

    process.on('SIGTERM', shutDown)
    process.on('SIGINT', shutDown)

    async function shutDown() {
      Log.error('shutting down...')
      server.close(async () => {
        Log.log('server close success')
        // await ormConnection.close()
        await redisClient.quit()
        process.exit(0)
      })
    }
  // })
}

main()

process.on('uncaughtException', function(err) {
  Log.error('Caught exception: ' + err)
})
