/*this is generated code */
import  { AxiosResponse } from 'axios'
declare global{
  namespace API {
    /** mt-dm-order-info
      * 文档链接：http://op.pagoda.com.cn/?#/technical-doc
      * {"description":"order domain info rest api","version":"1.0","title":"1.0"}
      * *********
    */
    namespace mt_dm_order_info {

        type Activity = {

            activityCode?:   string

            activityName?:   string

            activityType?:   number

            couponCode?:   string

            discount?:   number

            goodsList?:    Array < string >

            id?:   number

            mainOrderNo?:   string

            type?:   string

        }

        type AfterSaleDetailManageReq = {

            optionalInfo?:    Array < string >

            subOrderNo?:   string

        }

        type AfterSaleDetailReq = {

            afterSaleOrderNo?:   string

            optionalInfo?:    Array < string >

        }

        type AfterSaleListManageReq = {

            afterSaleOrderNo?:   string

            applyTimeEnd?:   string

            applyTimeStart?:   string

            goodCode?:   string

            goodName?:   string

            leaderMobile?:   string

            leaderName?:   string

            leaderStoreCode?:   string

            leaderStoreName?:   string

            pageNo?:   number

            pageSize?:   number

            status?:   number

            subOrderNo?:   string

        }

        type AfterSaleOrderInfo = {

            afterSaleFrom?:   number

            afterSaleOrderItems?:    Array < AfterSaleOrderItem >

            afterSaleOrderNo?:   string

            afterSaleType?:   number

            applicant?:   string

            applyRefundAmount?:   number

            applyTime?:   string

            auditRemark?:   string

            auditTime?:   string

            auditor?:   string

            customerType?:   number

            description?:   string

            gmtCreate?:   string

            gmtModified?:   string

            id?:   number

            mainOrderNo?:   string

            memberCode?:   string

            mobile?:   string

            orgCode?:   string

            realRefundAmount?:   number

            reason?:   string

            refundOrderNo?:   string

            status?:   number

            subOrderNo?:   string

            universalOrderAttachments?:    Array < UniversalOrderAttachment >

            v?:   number

        }

        type AfterSaleOrderItem = {

            afterSaleOrderNo?:   string

            applyRefundAmount?:   number

            damageNum?:   number

            gmtCreate?:   string

            gmtModified?:   string

            goodsId?:   number

            id?:   number

            itemSnapshotId?:   number

            lackNum?:   number

            realRefundAmount?:   number

            swapNum?:   number

            v?:   number

        }

        type AfterSaleOrderReq = {

            action?:   string

            applyTimeEnd?:   string

            applyTimeStart?:   string

            code?:   string

            codeType?:   number

            count?:   number

            extraFilter?:   any

            optionalInfo?:    Array < string >

            recordTime?:   string

            status?:   number

        }

        type AntiFraudRequest = {

            payloads?:    Array < RequestField >

        }

        type ApiResult = {

            data?:   any

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultWrapper = {

            dataList?:    Array < Mapstringobject >

            dataMap?:   any

            errorMsg?:   string

            resultCode?:   number

        }

        type ApiResultListOrderGoodsRes = {

            data?:    Array < OrderGoodsRes >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListOrderInfo = {

            data?:    Array < OrderInfo >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListOrder = {

            data?:    Array < Order >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListRefundOrderDetailRes = {

            data?:    Array < RefundOrderDetailRes >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultMapstringint = {

            data?:   any

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultOrderCountRes = {

            data?:  OrderCountRes

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultRefundOrderDetailRes = {

            data?:  RefundOrderDetailRes

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultRefundRes = {

            data?:  RefundRes

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultStatisticsStoreRes = {

            data?:  StatisticsStoreRes

            errorMsg?:   string

            resultCode:   number

        }

        type AssetInfo = {

            couponList?:    Array < CouponInfo >

            integralDeduction?:   number

        }

        type BasicItem = {

            goodsId?:   number

            id?:   number

            itemCode?:   string

            itemSnapshotId?:   number

            quantity?:   string

            salesPrice?:   number

            skuId?:   string

            subOrderNo?:   string

            weight?:   string

        }

        type BasicMember = {

            memberCode?:   string

            mobile?:   string

        }

        type BatchOrderDetail = {

            optionalInfo?:    Array < string >

            orderNos?:    Array < string >

            timeout?:   number

        }

        type BlackCheckInfo = {

            clientIp?:   string

            customerAddr?:   string

            customerName?:   string

            customerPhone?:   string

            deviceNo?:   string

            latitude?:   string

            longitude?:   string

        }

        type CommunityGroup = {

            cityCode?:   string

            customerCode?:   string

            distributionCenter?:   string

            id?:   number

            leaderCode?:   string

            leaderNote?:   string

            mainOrderNo?:   string

            mobileShortCode?:   string

            submittedTime?:   string

            v?:   string

            warehouseId?:   string

        }

        type Cost = {

            costAmount?:   number

            costCode?:   string

            standardAmount?:   number

        }

        type CouponInfo = {

            couponId?:   string

            discountAmount?:   number

        }

        type Customer = {

            createdTime?:   string

            customerCode?:   string

            customerType?:   number

            id?:   number

            mainOrderNo?:   string

            mobile?:   string

            v?:   number

        }

        type DataBeanX = {

            data?:    Array < RefundItem >

            total?:   number

        }

        type Detail = {

            type?:   string

            value?:   string

        }

        type GoodsListBean = {

            goods_code?:   string

            goods_name?:   string

            goods_price?:   string

            goods_spec?:   string

            quantity?:   string

        }

        type GoodsOrderReq = {

            action?:   string

            count?:   number

            customerCode?:   string

            customerType?:   number

            endTime?:   string

            extraFilter?:   any

            goodsDetail?:   boolean

            initValue?:   number

            optionalInfo?:    Array < string >

            orderChannel?:   string

            orderModel?:   number

            orderStatus?:    Array < number >

            orderType?:    Array < number >

            pickUpCode?:   string

            saleModel?:   number

            searchOrderNextTime?:   string

            searchOrderPrevTime?:   string

            searchOrderTime?:   string

            startTime?:   string

        }

        type GoodsShipmentReq = {

            cityCodes?:    Array < string >

            itemCodes?:    Array < string >

            periods?:    Array < string >

        }

        type GroupActivity = {

            activityCode?:   string

            cancelTime?:   string

            changeTime?:   string

            endTime?:   string

            groupCode?:   string

            groupCount?:   number

            groupModel?:   number

            groupStatus?:   number

            id?:   number

            joinCount?:   number

            leaderOrderNo?:   string

            openTime?:   string

            successTime?:   string

            v?:   number

        }

        type Invoice = {

            email?:   string

            identifyNo?:   string

            money?:   number

            note?:   string

            remark?:   string

            status?:   number

            title?:   string

            titleType?:   string

            type?:   number

        }

        type Mapstringint = {

        }

        type Mapstringobject = {

        }

        type Order = {

            activity?:    Array < Activity >

            attachChannelNo?:   string

            attachOrderNo?:   string

            brand?:   number

            channelNo?:   string

            channelNum?:   number

            communityGroup?:  CommunityGroup

            createDateTime?:   string

            customer?:  Customer

            customerCode?:   string

            discounts?:    Array < Detail >

            dispatchEndDateTime?:   string

            dispatchStartDateTime?:   string

            dispatchType?:   number

            employeeId?:   string

            groupActivity?:  GroupActivity

            id?:   number

            invoice?:  Invoice

            items?:    Array < BasicItem >

            mainOrderNo?:   string

            member?:  BasicMember

            orderChannel?:   number

            orderEntry?:   number

            orderModel?:   number

            orderNo?:   string

            orderStatus?:   number

            orderType?:   number

            orgCode?:   string

            payment?:  Payment

            paymentStatus?:   number

            periodOrders?:  PeriodOrder

            pickUpCode?:   string

            posDeviceNo?:   string

            preCreateTradeNo?:   string

            preTradeInfo?:  PrePayRequest

            processedMap?:   any

            receiver?:  Receiver

            refundNo?:   string

            resource?:   string

            saleModel?:   number

            source?:   string

            streamCode?:   string

            subOrders?:    Array < SubOrder >

            submittedTime?:   string

            syncEShopInfo?:  SyncEShopInfoReq

            thirdCustomers?:  ThirdCustomer

            tickNo?:   string

            tickType?:   number

            tradeNo?:   string

            turn?:   number

            v?:   number

            waybillSet?:    Array < Waybill >

        }

        type OrderCountReq = {

            customerCode?:   string

            customerType?:   number

            endTime?:   string

            exclude?:   any

            joinMain?:   string

            orderChannel?:   number

            orderModel?:   number

            orderStatus?:    Array < number >

            orderType?:    Array < number >

            saleModel?:   number

            startTime?:   string

        }

        type OrderCountRes = {

            count?:   number

        }

        type OrderDetailBatchReq = {

            orderNo?:    Array < string >

        }

        type OrderGoodsReq = {

            channel?:   number

            externalOrderNo?:   string

            orderNo?:   string

        }

        type OrderGoodsRes = {

            actualAmount?:   number

            channelOrderSn?:   string

            itemList?:    Array < OrderItem >

        }

        type OrderInfo = {

            afterSaleOrderInfos?:    Array < AfterSaleOrderInfo >

            attachOrderNo?:   string

            communityGroup?:   any

            createTime?:   string

            customer?:  Customer

            dispatchType?:   number

            endDispatchTime?:   string

            finishTime?:   string

            items?:    Array < Mapstringobject >

            mainOrderNo?:   string

            member?:   any

            orderChannel?:   number

            orderEntry?:   number

            orderModel?:   number

            orderNo?:   string

            orderStatus?:   number

            orderType?:   number

            orgCode?:   string

            orgName?:   string

            payment?:   any

            paymentStatus?:   number

            pickUpCode?:   string

            pickupTime?:   string

            posDeviceNo?:   string

            receiver?:  ReceiverInfo

            refundAmount?:   string

            saleModel?:   number

            startDispatchTime?:   string

            store?:   any

            streamCode?:   string

        }

        type OrderItem = {

            goodsName?:   string

            goodsSn?:   string

            heartPrice?:   number

            refundedAmount?:   number

            retailPrice?:   number

            sellType?:   string

            skuId?:   string

            totalPrice?:   number

        }

        type OrderListQueryReq = {

            arriveTimeEnd?:   string

            arriveTimeStart?:   string

            channel?:   number

            cityCode?:   string

            customerCode?:   string

            deliveryCenterCode?:   string

            mobile?:   string

            orderCode?:   string

            orderModel?:   number

            orderStatus?:    Array < number >

            orderType?:   number

            orgCode?:   string

            orgName?:   string

            pageNo?:   number

            pageSize?:   number

            pickUpCode?:   string

            receiverMobile?:   string

            saleModel?:   number

            submittedTimeEnd?:   string

            submittedTimeStart?:   string

        }

        type OrderListReq = {

            customerCode?:   string

            endTime?:   string

            orderChannel?:   number

            orderModel?:   number

            orderType?:    Array < number >

            saleModel?:   number

            startTime?:   string

        }

        type PagodaOrderDetailReq = {

            customerCode?:   string

            optionalInfo?:    Array < string >

            orderNo?:   string

            saleModel?:   number

        }

        type Param = {

            apply_refund_time_begin?:   string

            apply_refund_time_end?:   string

            channel_key?:   string

            channel_order_sn?:   string

            order_type?:   string

            page_no?:   string

            page_size?:   string

            refund_status?:   string

            store_code?:   string

        }

        type ParamsBean = {

            cut_off_time?:   string

            store_code?:   string

        }

        type Payment = {

            details?:    Array < Detail >

            payDateTime?:   string

            paymentType?:   string

            status?:   string

            tradeNo?:   string

        }

        type PeriodOrder = {

            content?:   string

            cronExpress?:   string

            cutTime?:   string

            endTime?:   string

            id?:   number

            limitCounter?:   number

            mainOrderNo?:   string

            nextTime?:   string

            orderIndex?:   number

            startTime?:   string

            type?:   number

        }

        type PrePayRequest = {

            assetInfo?:  AssetInfo

            blackCheckInfo?:  BlackCheckInfo

            brand?:   string

            channelNo?:   string

            checkedActivityList?:    Array < SimpleActivity >

            costList?:    Array < Cost >

            extensionInfo?:   string

            goodsList?:    Array < SimpleGoods >

            memberId?:   number

            memberIdentifyType?:   string

            merchantId?:   string

            orderAmount?:   number

            payAmount?:   number

            shopNo?:   string

            tradeScene?:   string

            tradeType?:   string

            uuid?:   string

        }

        type Receiver = {

            address?:   string

            addressNote?:   string

            city?:   string

            gender?:   number

            hometown?:   string

            id?:   number

            latitude?:   string

            longitude?:   string

            mobile?:   string

            name?:   string

            orderNote?:   string

            province?:   string

            secondPhone?:   string

            street?:   string

        }

        type ReceiverInfo = {

            address?:   string

            addressNote?:   string

            city?:   string

            hometown?:   string

            latitude?:   string

            longitude?:   string

            mobile?:   string

            name?:   string

            orderNote?:   string

            province?:   string

            secondPhone?:   string

            street?:   string

        }

        type RefundGoodsItem = {

            goodsName?:   string

            goodsSn?:   string

            goodsType?:   string

            itemSnapshotId?:   number

            num?:   number

            refundAmount?:   number

            specificationDesc?:   string

            unitType?:   string

            unitTypeCode?:   string

            volume?:   number

            weight?:   number

        }

        type RefundItem = {

            actual_amount?:   string

            address?:   string

            channel_key?:   string

            channel_order_sn?:   string

            complaint_refund_no?:   string

            created_at?:   string

            delivery_city?:   string

            delivery_company?:   string

            delivery_no?:   string

            delivery_status?:   string

            goods_list?:    Array < GoodsListBean >

            goods_total_amount?:   string

            operator_status?:   string

            order_refund_id?:   number

            pic_urls?:    Array < string >

            reason?:   string

            refund_apply_time?:   string

            refund_status?:   string

            refund_time?:   string

            refund_total_amount?:   string

            refund_type?:   string

            refuse_reason?:   string

            store_code?:   string

            store_name?:   string

            waybill_number?:   string

        }

        type RefundOrderDetailReq = {

            refundOrderNo?:   string

        }

        type RefundOrderDetailRes = {

            additionReason?:   string

            attachOrderNo?:   string

            brand?:   number

            channel?:   number

            createTime?:   string

            dispatchType?:   number

            endDispatchTime?:   string

            externalCreateTime?:   string

            images?:    Array < string >

            launchType?:   number

            orderEntry?:   number

            orderModel?:   number

            orderType?:   number

            orgCode?:   string

            orgName?:   string

            paymentStatus?:   number

            reason?:   string

            refundFinishTime?:   string

            refundItemList?:    Array < RefundGoodsItem >

            refundModel?:   number

            refundMoney?:   number

            refundOrderId?:   number

            refundOrderNo?:   string

            refundStatus?:   number

            refundTime?:   string

            reviewComment?:   string

            reviewTime?:   string

            saleModel?:   number

            source?:   string

            startDispatchTime?:   string

            streamCode?:   string

            tickType?:   number

        }

        type RefundOrderListReq = {

            endTime?:   string

            offSetValue?:   number

            orgCode?:   string

            pageNumber?:   number

            pageSize?:   number

            refundModel?:   number

            refundOrderNo?:   string

            startTime?:   string

            status?:   number

            subOrderNo?:   string

        }

        type RefundReq = {

            app_key?:   string

            format?:   string

            params?:  Param

            sign?:   string

            timestamp?:   string

            token?:   string

            version?:   string

        }

        type RefundRes = {

            code?:   number

            data?:  DataBeanX

            message?:   string

            request_id?:   string

        }

        type RequestField = {

            fieldName?:   string

            value?:    Array < any >

        }

        type Result = {

            cut_off_time?:   string

            total_amount?:   number

            total_quantity?:   number

        }

        type SalesData = {

            list?:    Array < Result >

        }

        type SimpleActivity = {

            activityCode?:   string

            goodsList?:    Array < string >

        }

        type SimpleGoods = {

            goodCode?:   string

            quantity?:   number

            serviceFeeList?:    Array < SimpleGoods >

            unitPrice?:   number

        }

        type StatisticsStoreReq = {

            app_key?:   string

            format?:   string

            params?:  ParamsBean

            sign?:   string

            timestamp?:   string

            token?:   string

            version?:   string

        }

        type StatisticsStoreRes = {

            code?:   number

            data?:  SalesData

            message?:   string

            request_id?:   string

        }

        type StoreInfo = {

            address?:   string

            brandID?:   number

            deliveryTime?:   string

            distance?:   string

            isOpen?:   string

            lat?:   string

            lon?:   string

            name?:   string

            number?:   string

            openingTime?:   string

            phone?:   string

            shortName?:   string

            storeID?:   number

        }

        type SubOrder = {

            cancelTime?:   string

            changeTime?:   string

            customerCode?:   string

            defaultDispatchType?:   number

            endDispatchTime?:   string

            expCode?:   string

            finishTime?:   string

            id?:   number

            insteadOrgCode?:   string

            items?:    Array < BasicItem >

            mainOrderNo?:   string

            orderType?:   number

            orgCode?:   string

            paymentStatus?:   number

            pickUpCode?:   string

            prepareStockTime?:   string

            printBillTime?:   string

            realDispatchType?:   number

            receiver?:  Receiver

            receiverId?:   number

            startDispatchTime?:   string

            status?:   number

            streamCode?:   string

            subOrderNo?:   string

            submittedTime?:   string

            tickType?:   number

            v?:   number

        }

        type SyncEShopInfoReq = {

            chanID?:   string

            customerID?:   number

            distance?:   string

            fromSmartRetail?:   string

            preDepId?:   number

            receiverAddrID?:   string

            receiverCityID?:   string

            store?:  StoreInfo

            tradeNo?:   string

            tripDeliveryDistance?:   number

        }

        type ThirdCustomer = {

            customerCode?:   string

            customerName?:   string

            gender?:   number

            id?:   number

        }

        type UniversalOrderAttachment = {

            businessType?:   number

            gmtCreate?:   string

            gmtModified?:   string

            id?:   number

            orderType?:   number

            resourceType?:   number

            universalOrderNo?:   string

            url?:   string

        }

        type Waybill = {

            active?:   number

            arrivalTime?:   string

            assignRidersTime?:   string

            atStoreTime?:   string

            cancelTime?:   string

            changeTime?:   string

            createdTime?:   string

            deliveryCompany?:   string

            deliveryStaffMobile?:   string

            deliveryStaffName?:   string

            deliveryTime?:   string

            endStatus?:   boolean

            id?:   number

            receivingTime?:   string

            subOrderNo?:   string

            waybillNo?:   string

            waybillStatus?:   number

        }



      /**description
       * order domain info rest api
       */

        /**
        * antiFraudStat
        * @method
        * @name antiFraudStat
        * @param  body - request * @param  x-pagoda-envoy-route-project -
        */
        function postAntifraudV1EshopOrderStat (

            parameters : {
              'body'  : AntiFraudRequest,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * bathOrderDetail
        * @method
        * @name bathOrderDetail
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postBatchOrderDetails (

            parameters : {
              'body'  : BatchOrderDetail,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * bathOrderLastTimeline
        * @method
        * @name bathOrderLastTimeline
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postBatchOrderLastTimeline (

            parameters : {
              'body'  : BatchOrderDetail,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * bathOrderTimelines
        * @method
        * @name bathOrderTimelines
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postBatchOrderTimelines (

            parameters : {
              'body'  : BatchOrderDetail,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * check
        * @method
        * @name check
        * @param  x-pagoda-envoy-route-project -
        */
        function getCheck (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * antiFraudStat
        * @method
        * @name antiFraudStat
        * @param  body - body * @param  x-pagoda-envoy-route-project -
        */
        function postEsV1CreateOrderSave (

            parameters : {
              'body'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * block
        * @method
        * @name block
        * @param  x-pagoda-envoy-route-project -
        */
        function getOrderInfoBlock (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * cpuInfo
        * @method
        * @name cpuInfo
        * @param  x-pagoda-envoy-route-project -
        */
        function getOrderInfoCpuInfo (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * mockPost
        * @method
        * @name mockPost
        * @param  body - body * @param  x-pagoda-envoy-route-project -
        */
        function postOrderInfoMockPost (

            parameters : {
              'body'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * proxy
        * @method
        * @name proxy
        * @param  x-pagoda-envoy-route-project -
        */
        function getOrderInfoProxy (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * proxy
        * @method
        * @name proxy

        */
        function headOrderInfoProxy (





        ): Promise < AxiosResponse >

        /**
        * proxy
        * @method
        * @name proxy

        */
        function postOrderInfoProxy (





        ): Promise < AxiosResponse >

        /**
        * proxy
        * @method
        * @name proxy

        */
        function putOrderInfoProxy (





        ): Promise < AxiosResponse >

        /**
        * proxy
        * @method
        * @name proxy

        */
        function deleteOrderInfoProxy (





        ): Promise < AxiosResponse >

        /**
        * proxy
        * @method
        * @name proxy

        */
        function optionsOrderInfoProxy (





        ): Promise < AxiosResponse >

        /**
        * proxy
        * @method
        * @name proxy

        */
        function patchOrderInfoProxy (





        ): Promise < AxiosResponse >

        /**
        * afterSaleDetail
        * @method
        * @name afterSaleDetail
        * @param  body - request * @param  x-pagoda-envoy-route-project -
        */
        function postOrderInfoV1AftersaleDetail (

            parameters : {
              'body'  : AfterSaleDetailReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getManageAfterSaleDetail
        * @method
        * @name getManageAfterSaleDetail
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderInfoV1AftersaleDetailManage (

            parameters : {
              'body'  : AfterSaleDetailManageReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listAfterSaleOrder
        * @method
        * @name listAfterSaleOrder
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderInfoV1AftersaleList (

            parameters : {
              'body'  : AfterSaleOrderReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listManageAfterSaleOrder
        * @method
        * @name listManageAfterSaleOrder
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderInfoV1AftersaleListManage (

            parameters : {
              'body'  : AfterSaleListManageReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * orderCount
        * @method
        * @name orderCount
        * @param  body - orderCountReq * @param  x-pagoda-envoy-route-project -
        */
        function postOrderInfoV1OrderCount (

            parameters : {
              'body'  : OrderCountReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 自营订单详情
        * @method
        * @name 自营订单详情
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderInfoV1OrderDetail (

            parameters : {
              'body'  : PagodaOrderDetailReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量订单号查询
        * @method
        * @name 批量订单号查询
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderInfoV1OrderDetailBatch (

            parameters : {
              'body'  : OrderDetailBatchReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * orderGoodsDetail
        * @method
        * @name orderGoodsDetail
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderInfoV1OrderGoodsDetail (

            parameters : {
              'body'  : OrderGoodsReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getOrderList
        * @method
        * @name getOrderList
        * @param  body - goodsOrderReq * @param  x-pagoda-envoy-route-project -
        */
        function postOrderInfoV1OrderList (

            parameters : {
              'body'  : GoodsOrderReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getManageAfterSaleDetail
        * @method
        * @name getManageAfterSaleDetail
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderInfoV1OrderListManage (

            parameters : {
              'body'  : OrderListQueryReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * orderPaymentCount
        * @method
        * @name orderPaymentCount
        * @param  body - orderCountReq * @param  x-pagoda-envoy-route-project -
        */
        function postOrderInfoV1OrderPaymentCount (

            parameters : {
              'body'  : OrderCountReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * refundList
        * @method
        * @name refundList
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderInfoV1OrderRefundList (

            parameters : {
              'body'  : RefundReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getOrderStatList
        * @method
        * @name getOrderStatList
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderInfoV1OrderStatList (

            parameters : {
              'body'  : OrderListReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * storeSalesStatistics
        * @method
        * @name storeSalesStatistics
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderInfoV1OrderStatisticsStoreSales (

            parameters : {
              'body'  : StatisticsStoreReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryRefundDetail
        * @method
        * @name queryRefundDetail
        * @param  body - refundOrderDetailReq * @param  x-pagoda-envoy-route-project -
        */
        function postOrderInfoV1RefundDetail (

            parameters : {
              'body'  : RefundOrderDetailReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryRefundList
        * @method
        * @name queryRefundList
        * @param  body - refundOrderListReq * @param  x-pagoda-envoy-route-project -
        */
        function postOrderInfoV1RefundList (

            parameters : {
              'body'  : RefundOrderListReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * statistics
        * @method
        * @name statistics
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderInfoV1StatisticsGoodsShipment (

            parameters : {
              'body'  : GoodsShipmentReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * xlog
        * @method
        * @name xlog
        * @param  body - body * @param  x-pagoda-envoy-route-project -
        */
        function postOrderInfoXlog (

            parameters : {
              'body'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

      }
  }
}
export {}

