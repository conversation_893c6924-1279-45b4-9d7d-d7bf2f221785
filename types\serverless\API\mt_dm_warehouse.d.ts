/*this is generated code */
import  { AxiosResponse } from 'axios'
declare global{
  namespace API {
    /** mt-dm-warehouse
      * 文档链接：http://op.pagoda.com.cn/?#/technical-doc
      * {"contact":{},"description":"仓配域","title":"1.0.0","version":"1.0.0"}
      * dm-warehouse.kd1.pagoda.com.cn
    */
    namespace mt_dm_warehouse {

        type FindInvCheckStockInput = {

        }

        type PageableDTOFindInvDepotByConditionInput = {

            pageable?:  Pageable

            dto?:  FindInvDepotByConditionInput

        }

        type UpdateParentCatClassInput = {

            id?:   number

            parentCatClassCode?:   string

            version?:   number

            parentCatClassName?:   string

            parentCatClassId?:   number

        }

        type PageableDTOFindInvDepotByCatClassInput = {

            pageable?:  Pageable

            dto?:  FindInvDepotByCatClassInput

        }

        type PageableDTOFindInvDepotByCodeOrNameInput = {

            pageable?:  Pageable

            dto?:  FindInvDepotByCodeOrNameInput

        }

        type Pageable = {

            pageNumber?:   number

            pageSize?:   number

            sort?:   string

        }

        type FindInvDepotByCodeOrNameInput = {

            routeCode?:    Array < string >

            codeOrName?:   string

            idList?:    Array < number >

        }

        type FindInvDepotClassTreeInput = {

            catId?:   number

            levelNum?:   number

            distance?:   number

            pidList?:    Array < number >

            cidList?:    Array < number >

            isEnabled?:   number

            id?:   number

        }

        type FindDepotPeriodListInput = {

            depotClassCodeList?:    Array < string >

            depotCodeList?:    Array < string >

            prePeriodFlag?:   number

            currentPeriod?:   string

            orgCodeList?:    Array < string >

        }

        type PageableDTOFindLimitGoodsInput = {

            pageable?:  Pageable

            dto?:  FindLimitGoodsInput

        }

        type FindInvDepotBaseSettingInput = {

            depotClassCodeList?:    Array < string >

            isCheckPermitDecimal?:   number

            isStoreVisible?:   number

            depotBelongTo?:   number

            isAllowInvOut?:   number

            isExportSales?:   number

            codeList?:    Array < string >

            idList?:    Array < number >

            goodsTypeIdList?:    Array < number >

            orgCodeList?:    Array < string >

            checkMode?:   number

            orgIdList?:    Array < number >

            isEnabled?:   number

            isShowNegInv?:   number

        }

        type FindInvDepotCatClassInput = {

            catId?:   number

            maxLevelNum?:   number

            levelNum?:   number

            isEnabled?:   number

            id?:   number

            idList?:    Array < number >

            isLeaf?:   number

            parentCatClassId?:   number

        }

        type InvDepotGoodsType对象 = {

            creatorCode?:   string

            lastModifiedAt?:   string

            entId?:   number

            goodsTypeCode?:   string

            goodsTypeName?:   string

            creatorName?:   string

            depotId?:   number

            version?:   number

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            deleted?:   number

            goodsTypeId?:   number

            id?:   number

            modifierName?:   string

            modifierOrgCode?:   string

        }

        type PageFindInvDepotOutput = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < FindInvDepotOutput >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type Sort = {

            unsorted?:   boolean

            sorted?:   boolean

            empty?:   boolean

        }

        type DelInvDepotInput = {

            id?:   number

            orgId?:   number

        }

        type UpdateInvDepotCatClassInput = {

            catId?:   number

            code?:   string

            isEnabled?:   number

            name?:   string

            remark?:   string

            showOrder?:   number

            id?:   number

            parentCatClassId?:   number

        }

        type PageFindInvDepotOutput_1 = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < FindInvDepotOutput_1 >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type InvLimitTransGoods对象 = {

            creatorCode?:   string

            lastModifiedAt?:   string

            entId?:   number

            goodsId?:   number

            creatorName?:   string

            goodsClassId?:   number

            remark?:   string

            goodsClassName?:   string

            limitType?:   number

            version?:   number

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            deleted?:   number

            goodsClassCode?:   string

            transTypeId?:   number

            goodsCode?:   string

            id?:   number

            modifierName?:   string

            transTypeName?:   string

            modifierOrgCode?:   string

            goodsName?:   string

            transTypeCode?:   string

        }

        type PageFindInvDepotListOutput = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < FindInvDepotListOutput >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type PageableDTOFindInvDepotBaseSettingInput = {

            pageable?:  Pageable

            dto?:  FindInvDepotBaseSettingInput

        }

        type InvDepot对象 = {

            isPurRetOut?:   number

            isCheckByAdd?:   number

            turnUserName?:   string

            adLng?:   number

            defCatName?:   string

            depotPhone?:   string

            isCheckInputNeedCheck?:   number

            prePeriodFlag?:   number

            depotGroupId?:   number

            isPostDailyDetail?:   number

            modifierCode?:   string

            wmsCode?:   string

            createdAt?:   string

            manageType?:   number

            wmsDepotType?:   string

            adCode?:   string

            isSalConIn?:   number

            id?:   number

            modifierOrgCode?:   string

            defCatClassName?:   string

            creatorCode?:   string

            orgName?:   string

            isPickAllocation?:   number

            isStoreVisible?:   number

            isTurnExistNoPost?:   number

            entId?:   number

            isOrg?:   number

            isSimpleProcess?:   number

            isStoEnabled?:   number

            isSalRetIn?:   number

            orgClassId?:   number

            defCatCode?:   string

            version?:   number

            checkInvRange?:   number

            isEnabled?:   number

            name?:   string

            defCatId?:   number

            depotGroupCode?:   string

            isPurRecIn?:   number

            isGoodsTypeAll?:   number

            orgClassCode?:   string

            isCheckPermitDecimal?:   number

            isCheckShowFinQty?:   number

            isSalRetOut?:   number

            code?:   string

            lastModifiedAt?:   string

            isCheckQtyZero?:   number

            creatorName?:   string

            isAllowInvOut?:   number

            isExportSales?:   number

            autoInboundCostType?:   number

            isCheckByPost?:   number

            defCatClassId?:   number

            orgId?:   number

            creatorOrgCode?:   string

            isDefDualDepot?:   number

            isEnableLot?:   string

            previousPeriod?:   string

            isPostDaily?:   number

            orgCode?:   string

            isDualDepot?:   number

            orgClassName?:   string

            defCatClassCode?:   string

            balOrgId?:   number

            currentPeriod?:   string

            depotGroupName?:   string

            isShowNegInv?:   number

            measureType?:   number

            autoInboundCostPrice?:   number

            turnUserCode?:   string

            depotAddress?:   string

            autoInboundDepotId?:   number

            isAllowPostReturn?:   number

            stoPeriod?:   string

            checkDay?:   number

            turnDateTime?:   string

            isSalConOut?:   number

            deleted?:   number

            checkMode?:   number

            isPostCheckAll?:   number

            isCheckByNegative?:   number

            adLat?:   number

            searchWord?:   string

            modifierName?:   string

        }

        type InvDepotPeriod对象 = {

            depotName?:   string

            creatorCode?:   string

            lastModifiedAt?:   string

            orgName?:   string

            entId?:   number

            creatorName?:   string

            depotId?:   number

            orgId?:   number

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            deleted?:   number

            orgCode?:   string

            depotCode?:   string

            id?:   number

            modifierName?:   string

            modifierOrgCode?:   string

            prePeriodNo?:   string

            periodNo?:   string

        }

        type FindInvDepotOutput = {

            isPurRetOut?:   number

            isCheckByAdd?:   number

            turnUserName?:   string

            adLng?:   number

            defCatName?:   string

            depotPhone?:   string

            isCheckInputNeedCheck?:   number

            prePeriodFlag?:   number

            depotGroupId?:   number

            isPostDailyDetail?:   number

            modifierCode?:   string

            wmsCode?:   string

            createdAt?:   string

            manageType?:   number

            wmsDepotType?:   string

            adCode?:   string

            isSalConIn?:   number

            id?:   number

            modifierOrgCode?:   string

            defCatClassName?:   string

            isCusAutoSale?:   number

            creatorCode?:   string

            orgName?:   string

            isPickAllocation?:   number

            isStoreVisible?:   number

            isTurnExistNoPost?:   number

            entId?:   number

            isOrg?:   number

            isSimpleProcess?:   number

            isStoEnabled?:   number

            isSalRetIn?:   number

            orgClassId?:   number

            defCatCode?:   string

            version?:   number

            checkInvRange?:   number

            cusOrgName?:   string

            isEnabled?:   number

            name?:   string

            defCatId?:   number

            depotGroupCode?:   string

            isPurRecIn?:   number

            isGoodsTypeAll?:   number

            orgClassCode?:   string

            isCheckPermitDecimal?:   number

            isCheckShowFinQty?:   number

            isSalRetOut?:   number

            code?:   string

            lastModifiedAt?:   string

            cusOrgCode?:   string

            cusOrgId?:   number

            isCheckQtyZero?:   number

            creatorName?:   string

            isAllowInvOut?:   number

            isExportSales?:   number

            autoInboundCostType?:   number

            isCheckByPost?:   number

            defCatClassId?:   number

            orgId?:   number

            creatorOrgCode?:   string

            isDefDualDepot?:   number

            previousPeriod?:   string

            isPostDaily?:   number

            orgCode?:   string

            isDualDepot?:   number

            orgClassName?:   string

            defCatClassCode?:   string

            balOrgId?:   number

            currentPeriod?:   string

            depotGroupName?:   string

            isShowNegInv?:   number

            measureType?:   number

            autoInboundCostPrice?:   number

            turnUserCode?:   string

            depotAddress?:   string

            autoInboundDepotId?:   number

            isAllowPostReturn?:   number

            stoPeriod?:   string

            checkDay?:   number

            turnDateTime?:   string

            isSalConOut?:   number

            deleted?:   number

            checkMode?:   number

            isPostCheckAll?:   number

            isCheckByNegative?:   number

            adLat?:   number

            cusAddPriceRate?:   number

            searchWord?:   string

            modifierName?:   string

        }

        type FindDepotPeriodListOutput = {

            code?:   string

            lastModifiedAt?:   string

            orgName?:   string

            previousPeriod?:   string

            orgCode?:   string

            name?:   string

            modifierName?:   string

            currentPeriod?:   string

        }

        type AddImportCheckGoodsQtyInput = {

            result?:   string

            goodsCode?:   string

            goodsName?:   string

            goodsQty?:   string

            goodsSpec?:   string

            goodsUnitName?:   string

        }

        type AddLimitTransGoodsInput = {

            goodsClassCode?:   string

            goodsId?:   number

            goodsClassId?:   number

            remark?:   string

            goodsCode:   string

            goodsClassName?:   string

            goodsName?:   string

            limitType:   number

            transTypeCode:   string

        }

        type FindInvDepotCatClassOutput = {

            code?:   string

            entId?:   number

            parentPath?:   string

            fullName?:   string

            remark?:   string

            isLeaf?:   number

            parentCatClassId?:   number

            catId?:   number

            levelNum?:   number

            isEnabled?:   number

            name?:   string

            showOrder?:   number

            id?:   number

            parentCatClassCode?:   string

            parentCatClassName?:   string

        }

        type PageableDTOFindDepotOnPublicInput = {

            pageable?:  Pageable

            dto?:  FindDepotOnPublicInput

        }

        type UpdateInvTransTypeInput = {

            transName:   string

            totalOutFlag?:   number

            isBackCostPrice?:   number

            isTransBillShow?:   number

            totalInFixFlag?:   number

            initialFlag?:   number

            allowModifyPrice?:   number

            compType?:   number

            onhandFlag?:   number

            totalInFloatFlag?:   number

            totalInFoaFlag?:   number

            ioFlag?:   number

            totalInFixNonFlag?:   number

            totalInFlag?:   number

            id:   number

            transCode:   string

            allocationFlag?:   number

            inOutFlag?:   number

        }

        type FindInvDepotInput = {

            code?:   string

            id?:   number

            orgId?:   number

        }

        type BatchSetDepotInput = {

            isGoodsTypeAll?:   number

            isPurRetOut?:   number

            isSalRetOut?:   number

            isCheckByAdd?:   number

            isCheckQtyZero?:   number

            isSalRetIn?:   number

            isCheckByPost?:   number

            isDefDualDepot?:   number

            goodsTypeList?:    Array < InvDepotGoodsType对象 >

            isEnableLot?:   number

            isSalConOut?:   number

            wmsDepotType?:   string

            checkInvRange?:   number

            isSalConIn?:   number

            dealInvDepotList?:    Array < DelInvDepotInput >

            isPurRecIn?:   number

        }

        type UpdateInvDepotBaseInput = {

            isCheckPermitDecimal?:   number

            autoInboundCostPrice?:   number

            isStoreVisible?:   number

            checkMode?:   number

            isAllowInvOut?:   number

            isExportSales?:   number

            autoInboundCostType?:   number

            id?:   number

            autoInboundDepotCode?:   string

            isShowNegInv?:   number

        }

        type AddInvDepotInput = {

            isPurRetOut?:   number

            isCheckByAdd?:   number

            adLng?:   number

            depotPhone?:   string

            depotGroupId?:   number

            wmsCode?:   string

            manageType?:   number

            wmsDepotType?:   string

            adCode?:   string

            isSalConIn?:   number

            defCatClassName?:   string

            isCusAutoSale?:   number

            orgName?:   string

            isTurnExistNoPost?:   number

            entId?:   number

            isOrg?:   number

            isSimpleProcess?:   number

            isSalRetIn?:   number

            orgClassId?:   number

            goodsTypeList?:    Array < InvDepotGoodsType对象 >

            checkInvRange?:   number

            cusOrgName?:   string

            isEnabled?:   number

            name?:   string

            isOut?:   number

            depotGroupCode?:   string

            isPurRecIn?:   number

            isGoodsTypeAll?:   number

            orgClassCode?:   string

            isSalRetOut?:   number

            code?:   string

            cusOrgCode?:   string

            cusOrgId?:   number

            isCheckQtyZero?:   number

            isAllowInvOut?:   number

            isCheckByPost?:   number

            defCatClassId?:   number

            orgId?:   number

            isDefDualDepot?:   number

            previousPeriod?:   string

            orgCode?:   string

            isDualDepot?:   number

            orgClassName?:   string

            defCatClassCode?:   string

            balOrgId?:   number

            currentPeriod?:   string

            depotGroupName?:   string

            depotAddress?:   string

            checkDay?:   number

            isSalConOut?:   number

            checkMode?:   number

            adLat?:   number

            cusAddPriceRate?:   number

        }

        type PageFindInvDepotCatClassOutput = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < FindInvDepotCatClassOutput >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type UpdateOrCreateCatClassRef = {

            catId?:   number

            catClassCode?:   string

            depotId?:   number

            depotCode?:   string

            catClassId?:   number

            id?:   number

        }

        type PageableDTOFindInvDepotCatClassInput = {

            pageable?:  Pageable

            dto?:  FindInvDepotCatClassInput

        }

        type PageFindInvTransTypeOutPut = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < FindInvTransTypeOutPut >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type FindInvDepotByCatClassInput = {

            catId?:   number

            path?:   string

            code?:   string

            endCurrentPeriod?:   string

            orgIdList?:    Array < number >

            startCurrentPeriod?:   string

            isEnabled?:   number

            name?:   string

            catClassId?:   number

            orgCodeList?:    Array < string >

        }

        type AddInvDepotCatInput = {

            isDefault:   number

            code:   string

            isEditableFlag?:   number

            isEnabled:   number

            purposeCode?:   string

            name:   string

            remark?:   string

            showOrder?:   number

            levelNumLimit?:   number

        }

        type FindLimitGoodsInput = {

            goodsCodeList?:    Array < string >

            limitType?:   number

            transTypeCode?:   string

        }

        type UpdateInvDepotInput = {

            isPurRetOut?:   number

            orgClassCode?:   string

            isSalRetOut?:   number

            code?:   string

            isCheckByAdd?:   number

            cusOrgCode?:   string

            adLng?:   number

            cusOrgId?:   number

            isCheckQtyZero?:   number

            depotPhone?:   string

            isCheckByPost?:   number

            depotGroupId?:   number

            orgId?:   number

            isDefDualDepot?:   number

            wmsCode?:   string

            isEnableLot?:   string

            wmsDepotType?:   string

            orgCode?:   string

            isDualDepot?:   number

            isSalConIn?:   number

            orgClassName?:   string

            id?:   number

            currentPeriod?:   string

            depotGroupName?:   string

            isCusAutoSale?:   number

            orgName?:   string

            isTurnExistNoPost?:   number

            depotAddress?:   string

            isOrg?:   number

            isSimpleProcess?:   number

            isSalRetIn?:   number

            orgClassId?:   number

            goodsTypeList?:    Array < InvDepotGoodsType对象 >

            isSalConOut?:   number

            checkInvRange?:   number

            cusOrgName?:   string

            isEnabled?:   number

            name?:   string

            adLat?:   number

            cusAddPriceRate?:   number

            isOut?:   number

            depotGroupCode?:   string

            isPurRecIn?:   number

        }

        type FindDepotBasSettingOutput = {

            isCheckPermitDecimal?:   number

            code?:   string

            lastModifiedAt?:   string

            orgName?:   string

            autoInboundCostPrice?:   number

            isStoreVisible?:   number

            isOrg?:   number

            isAllowInvOut?:   number

            isExportSales?:   number

            autoInboundCostType?:   number

            autoInboundDepotName?:   string

            version?:   number

            autoInboundDepotCode?:   string

            checkMode?:   number

            orgCode?:   string

            name?:   string

            id?:   number

            modifierName?:   string

            isShowNegInv?:   number

        }

        type FindInvDepotListOutput = {

            isPurRetOut?:   number

            isCheckByAdd?:   number

            turnUserName?:   string

            adLng?:   number

            defCatName?:   string

            depotPhone?:   string

            isCheckInputNeedCheck?:   number

            prePeriodFlag?:   number

            depotGroupId?:   number

            isPostDailyDetail?:   number

            modifierCode?:   string

            wmsCode?:   string

            createdAt?:   string

            isOnWork?:   number

            manageType?:   number

            wmsDepotType?:   string

            adCode?:   string

            isSalConIn?:   number

            id?:   number

            modifierOrgCode?:   string

            defCatClassName?:   string

            isCusAutoSale?:   number

            creatorCode?:   string

            orgName?:   string

            isPickAllocation?:   number

            isStoreVisible?:   number

            isTurnExistNoPost?:   number

            entId?:   number

            isOrg?:   number

            isSimpleProcess?:   number

            isStoEnabled?:   number

            isSalRetIn?:   number

            orgClassId?:   number

            defCatCode?:   string

            version?:   number

            goodsTypeList?:    Array < InvDepotGoodsType对象 >

            checkInvRange?:   number

            cusOrgName?:   string

            isEnabled?:   number

            name?:   string

            isOut?:   number

            defCatId?:   number

            depotGroupCode?:   string

            isPurRecIn?:   number

            isGoodsTypeAll?:   number

            orgClassCode?:   string

            isCheckPermitDecimal?:   number

            isCheckShowFinQty?:   number

            isSalRetOut?:   number

            code?:   string

            lastModifiedAt?:   string

            cusOrgCode?:   string

            cusOrgId?:   number

            isCheckQtyZero?:   number

            creatorName?:   string

            isAllowInvOut?:   number

            isExportSales?:   number

            isSetWms?:   number

            autoInboundCostType?:   number

            isCheckByPost?:   number

            defCatClassId?:   number

            orgId?:   number

            creatorOrgCode?:   string

            isDefDualDepot?:   number

            previousPeriod?:   string

            isPostDaily?:   number

            orgCode?:   string

            isDualDepot?:   number

            orgClassName?:   string

            defCatClassCode?:   string

            balOrgId?:   number

            currentPeriod?:   string

            depotGroupName?:   string

            isShowNegInv?:   number

            measureType?:   number

            autoInboundCostPrice?:   number

            turnUserCode?:   string

            depotAddress?:   string

            autoInboundDepotId?:   number

            isAllowPostReturn?:   number

            stoPeriod?:   string

            checkDay?:   number

            turnDateTime?:   string

            isSalConOut?:   number

            deleted?:   number

            checkMode?:   number

            isPostCheckAll?:   number

            isCheckByNegative?:   number

            adLat?:   number

            cusAddPriceRate?:   number

            searchWord?:   string

            modifierName?:   string

        }

        type PageFindLimitGoodsOutput = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < FindLimitGoodsOutput >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type FindInvDepotOutput_1 = {

            code?:   string

            creatorCode?:   string

            lastModifiedAt?:   string

            entId?:   number

            isEditableFlag?:   number

            creatorName?:   string

            remark?:   string

            levelNumLimit?:   number

            version?:   number

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            isDefault?:   number

            deleted?:   number

            isEnabled?:   number

            purposeCode?:   string

            name?:   string

            showOrder?:   number

            id?:   number

            modifierName?:   string

            modifierOrgCode?:   string

        }

        type FindDepotOnPublicInput = {

            routeCode?:    Array < string >

            depotIdList?:    Array < number >

            authResourceCode?:   string

            code?:   string

            read?:   boolean

            endCurrentPeriod?:   string

            searchType?:   number

            defCatClassCodeList?:    Array < string >

            startCurrentPeriod?:   string

            endCreatedAt?:   string

            isSimpleProcess?:   number

            statrtCreatedAt?:   string

            codeList?:    Array < string >

            depotPhone?:   string

            defCatIdList?:    Array < number >

            orgCodeList?:    Array < string >

            defCatClassIdList?:    Array < number >

            orgClassCodeList?:    Array < string >

            orgIdList?:    Array < number >

            isEnabled?:   number

            name?:   string

            isDualDepot?:   number

            defCatCodeList?:    Array < string >

            cusDualCodeList?:    Array < string >

        }

        type PageableDTOFindDepotPeriodListInput = {

            pageable?:  Pageable

            dto?:  FindDepotPeriodListInput

        }

        type AddInvDepotCatClassInput = {

            catId?:   number

            code?:   string

            isEnabled?:   number

            name?:   string

            remark?:   string

            showOrder?:   number

            parentCatClassId?:   number

        }

        type InvDepotCatClassTreeOutput = {

            catId?:   number

            parent?:  InvDepotCatClassTreeOutput

            code?:   string

            levelNum?:   number

            children?:    Array < InvDepotCatClassTreeOutput >

            entId?:   number

            isEnabled?:   number

            name?:   string

            fullName?:   string

            id?:   number

            isLeaf?:   number

        }

        type BasPeriod对象 = {

            creatorCode?:   string

            lastModifiedAt?:   string

            nextPeriodNo?:   string

            endDate?:   string

            entId?:   number

            creatorName?:   string

            version?:   number

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            deleted?:   number

            id?:   number

            modifierName?:   string

            isYearOpen?:   number

            modifierOrgCode?:   string

            prePeriodNo?:   string

            startDate?:   string

            periodNo?:   string

        }

        type UpdateLimitTransGoodsInput = {

            goodsClassCode?:   string

            goodsId?:   number

            goodsClassId?:   number

            remark?:   string

            goodsCode:   string

            id?:   number

            goodsClassName?:   string

            goodsName?:   string

            limitType:   number

            transTypeCode:   string

        }

        type PageableDTOFindInvDepotCat = {

            pageable?:  Pageable

            dto?:  FindInvDepotCat

        }

        type ExportOutputVo = {

            faildList?:    Array < any >

            successList?:    Array < any >

            retCode?:   number

            retMsg?:   string

        }

        type FindLimitTypeOutput = {

            limitType?:   number

            limitTypeName?:   string

        }

        type FindLimitGoodsOutput = {

            lastModifiedAt?:   string

            entId?:   number

            goodsCode?:   string

            id?:   number

            modifierName?:   string

            transTypeName?:   string

            goodsName?:   string

            limitType?:   number

            limitTypeName?:   string

            transTypeCode?:   string

        }

        type SelectMiscTransTypeInput = {

            orgId?:   number

        }

        type FindDefaultClassTreeInput = {

            isDefault?:   number

            isEnabled?:   number

        }

        type FindInvDepotByConditionInput = {

            code?:   string

            deleted?:   number

            orgClassCodeList?:    Array < string >

            defCatClassCodeList?:    Array < string >

            orgIdList?:    Array < number >

            isEnabled?:   number

            name?:   string

            codeList?:    Array < string >

            idList?:    Array < number >

            currentPeriod?:   string

            orgCodeList?:    Array < string >

        }

        type EnableInvDepotInput = {

            isOrg?:   number

            isOut?:   number

            id?:   number

            isCus?:   number

            orgId?:   number

        }

        type FindInvTransTypeInput = {

            transName?:   string

            ioFlag?:   number

            id?:   number

            transCode?:   string

            isTransBillShow?:   number

            compTypeList?:    Array < number >

        }

        type InvDepotCatClass对象 = {

            code?:   string

            lastModifiedAt?:   string

            parentPath?:   string

            creatorName?:   string

            remark?:   string

            isLeaf?:   number

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            levelNum?:   number

            id?:   number

            modifierOrgCode?:   string

            parentCatClassName?:   string

            creatorCode?:   string

            entId?:   number

            fullName?:   string

            version?:   number

            parentCatClassId?:   number

            catId?:   number

            deleted?:   number

            isEnabled?:   number

            name?:   string

            showOrder?:   number

            modifierName?:   string

            parentCatClassCode?:   string

        }

        type PageFindDepotPeriodListOutput = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < FindDepotPeriodListOutput >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type InvDepotCatClassRel对象 = {

            depotName?:   string

            creatorCode?:   string

            lastModifiedAt?:   string

            entId?:   number

            creatorName?:   string

            depotId?:   number

            fullName?:   string

            catClassId?:   number

            version?:   number

            catClassName?:   string

            creatorOrgCode?:   string

            modifierCode?:   string

            catId?:   number

            createdAt?:   string

            path?:   string

            deleted?:   number

            catClassCode?:   string

            depotCode?:   string

            id?:   number

            modifierName?:   string

            modifierOrgCode?:   string

            uniqueId?:   number

        }

        type InvDepotCat对象 = {

            code?:   string

            creatorCode?:   string

            lastModifiedAt?:   string

            entId?:   number

            isEditableFlag?:   number

            creatorName?:   string

            remark?:   string

            levelNumLimit?:   number

            version?:   number

            creatorOrgCode?:   string

            isMulti?:   string

            modifierCode?:   string

            createdAt?:   string

            isDefault?:   number

            deleted?:   number

            isEnabled?:   number

            purposeCode?:   string

            name?:   string

            searchWord?:   string

            showOrder?:   number

            id?:   number

            modifierName?:   string

            modifierOrgCode?:   string

        }

        type PageableDTOFindInvTransTypeInput = {

            pageable?:  Pageable

            dto?:  FindInvTransTypeInput

        }

        type InvTransType对象 = {

            lastModifiedAt?:   string

            creatorName?:   string

            totalInFixFlag?:   number

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            compType?:   number

            onhandFlag?:   number

            ioFlag?:   number

            id?:   number

            transCode?:   string

            modifierOrgCode?:   string

            transName?:   string

            totalOutFlag?:   number

            creatorCode?:   string

            entId?:   number

            isBackCostPrice?:   number

            isTransBillShow?:   number

            initialFlag?:   number

            version?:   number

            allowModifyPrice?:   number

            deleted?:   number

            totalInFloatFlag?:   number

            totalInFoaFlag?:   number

            totalInFixNonFlag?:   number

            totalInFlag?:   number

            modifierName?:   string

            allocationFlag?:   number

            inOutFlag?:   number

        }

        type Type = {

            typeName?:   string

        }

        type FindInvDepotCat = {

            outNoClassCat?:   boolean

            endLastModifiedAt?:   string

            isDefault?:   number

            startCreatedAt?:   string

            code?:   string

            endCreatedAt?:   string

            isEditableFlag?:   number

            isEnabled?:   number

            purposeCode?:   string

            name?:   string

            id:   number

            startLastModifiedAt?:   string

        }

        type UpdateInvDepotCatInput = {

            isDefault?:   number

            code?:   string

            isEditableFlag?:   number

            isEnabled?:   number

            purposeCode?:   string

            name?:   string

            remark?:   string

            showOrder?:   number

            id?:   number

            levelNumLimit?:   number

        }

        type FindInvTransTypeOutPut = {

            lastModifiedAt?:   string

            creatorName?:   string

            totalInFixFlag?:   number

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            compType?:   number

            onhandFlag?:   number

            ioFlag?:   number

            id?:   number

            transCode?:   string

            modifierOrgCode?:   string

            transName?:   string

            totalOutFlag?:   number

            creatorCode?:   string

            entId?:   number

            isBackCostPrice?:   number

            isTransBillShow?:   number

            initialFlag?:   number

            version?:   number

            allowModifyPrice?:   number

            deleted?:   number

            totalInFloatFlag?:   number

            totalInFoaFlag?:   number

            totalInFixNonFlag?:   number

            totalInFlag?:   number

            modifierName?:   string

            allocationFlag?:   number

            inOutFlag?:   number

        }

        type AddInvTransTypeInput = {

            transName:   string

            totalOutFlag?:   number

            isBackCostPrice?:   number

            isTransBillShow?:   number

            totalInFixFlag?:   number

            initialFlag?:   number

            allowModifyPrice?:   number

            compType?:   number

            onhandFlag?:   number

            totalInFloatFlag?:   number

            totalInFoaFlag?:   number

            ioFlag?:   number

            totalInFixNonFlag?:   number

            totalInFlag?:   number

            transCode:   string

            allocationFlag?:   number

            inOutFlag?:   number

        }

        type PageFindDepotBasSettingOutput = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < FindDepotBasSettingOutput >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }



      /**description
       * 仓配域
       */

        /**
        * 导入商品盘点数量
        * @method
        * @name 导入商品盘点数量
        * @param integer checkOrderId - 盘点单Id * @param file multipartFile - 导入文件 * @param integer orgId - 机构Id * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvCheckStockImportCheckGoodsQty (

            parameters : {
              'checkOrderId'  ?: number,
              'multipartFile'  ?: any,
              'orgId'  ?: number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 添加库房目录
        * @method
        * @name 添加库房目录
        * @param  body - addInvDepotCatInput * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotCatAdd (

            parameters : {
              'body'  : Array<AddInvDepotCatInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量创建库房分类
        * @method
        * @name 批量创建库房分类
        * @param  body - addInvDepotCatClassInputs * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotCatClassAdd (

            parameters : {
              'body'  : Array<AddInvDepotCatClassInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询直接子分类
        * @method
        * @name 查询直接子分类
        * @param  body - invDepotCatClassInput * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotCatClassChildList (

            parameters : {
              'body'  : FindInvDepotCatClassInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 库房基础设置列表查询
        * @method
        * @name 库房基础设置列表查询
        * @param  body - pageableDTO * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotBaseSettingPage (

            parameters : {
              'body'  : PageableDTOFindInvDepotBaseSettingInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 添加入出库商品限制
        * @method
        * @name 添加入出库商品限制
        * @param  body - addLimitTransGoodsInput * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvLimitTransGoodsAdd (

            parameters : {
              'body'  : Array<AddLimitTransGoodsInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 导出库房详情
        * @method
        * @name 导出库房详情
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotExportDetail (

            parameters : {
              'body'  : FindInvDepotInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 通过code和name查询库房
        * @method
        * @name 通过code和name查询库房
        * @param  body - pageableDTO * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotPageByCodeOrName (

            parameters : {
              'body'  : PageableDTOFindInvDepotByCodeOrNameInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 禁用
        * @method
        * @name 禁用
        * @param  body - idList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotDisEnabled (

            parameters : {
              'body'  : Array<number>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量调整父节点分类
        * @method
        * @name 批量调整父节点分类
        * @param  body - updateParentCatClassInputs * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotCatClassSetParentCatClass (

            parameters : {
              'body'  : Array<UpdateParentCatClassInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 删除库房关系引用
        * @method
        * @name 删除库房关系引用
        * @param  body - idList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotCatClassRefDeleteCatClassRef (

            parameters : {
              'body'  : Array<number>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询分页列表
        * @method
        * @name 查询分页列表
        * @param  body - pageableDTO * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotCatPage (

            parameters : {
              'body'  : PageableDTOFindInvDepotCat,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询核算期列表
        * @method
        * @name 查询核算期列表
        * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getStockInvDepotPeriodPeriodList (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 获取单据限制对象
        * @method
        * @name 获取单据限制对象
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvLimitTransGoodsGetLimitType (

            parameters : {
              'body'  : FindLimitGoodsInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询库房二级分类详情: 分页
        * @method
        * @name 查询库房二级分类详情: 分页
        * @param  body - pageableDTO * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotCatClassChildPage (

            parameters : {
              'body'  : PageableDTOFindInvDepotCatClassInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 开启上一核算期
        * @method
        * @name 开启上一核算期
        * @param  body - depotIdList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotPeriodOpenLastPeriod (

            parameters : {
              'body'  : Array<number>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 导出库房基础设置列表
        * @method
        * @name 导出库房基础设置列表
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotBaseSettingExport (

            parameters : {
              'body'  : FindInvDepotBaseSettingInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 导出
        * @method
        * @name 导出
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvTransTypeExport (

            parameters : {
              'body'  : FindInvTransTypeInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 库房详情
        * @method
        * @name 库房详情
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotDetail (

            parameters : {
              'body'  : FindInvDepotInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询库房分类树
        * @method
        * @name 查询库房分类树
        * @param  body - invDepotClassTree * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotCatClassTreeList (

            parameters : {
              'body'  : FindInvDepotClassTreeInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 入出库类型枚举
        * @method
        * @name 入出库类型枚举
        * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getStockInvTransTypeGetEnum (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量设置wms
        * @method
        * @name 批量设置wms
        * @param  body - depotInputs * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotSetDepotWms (

            parameters : {
              'body'  : BatchSetDepotInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 删除库房目录
        * @method
        * @name 删除库房目录
        * @param  body - idList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotCatDelete (

            parameters : {
              'body'  : Array<number>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询分页列表
        * @method
        * @name 查询分页列表
        * @param  body - pageableDTO * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotPeriodPage (

            parameters : {
              'body'  : PageableDTOFindDepotPeriodListInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 修改
        * @method
        * @name 修改
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotUpdate (

            parameters : {
              'body'  : UpdateInvDepotInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 导出
        * @method
        * @name 导出
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotExport (

            parameters : {
              'body'  : FindInvDepotByConditionInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 修改入出库商品限制
        * @method
        * @name 修改入出库商品限制
        * @param  body - idList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvLimitTransGoodsDelete (

            parameters : {
              'body'  : Array<number>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询入出库类型
        * @method
        * @name 查询入出库类型
        * @param  body - findInvTransTypeInput * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvTransTypeDetail (

            parameters : {
              'body'  : FindInvTransTypeInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 修改入出库类型
        * @method
        * @name 修改入出库类型
        * @param  body - updateInvTransTypeInput * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvTransTypeUpdate (

            parameters : {
              'body'  : UpdateInvTransTypeInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 库房高级搜索
        * @method
        * @name 库房高级搜索
        * @param  body - pageableDTO * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotPageDepotOnPublic (

            parameters : {
              'body'  : PageableDTOFindDepotOnPublicInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 所有表设置Redis最大主键
        * @method
        * @name 所有表设置Redis最大主键
        * @param  body - tableNameList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockRedisGeneratorId (

            parameters : {
              'body'  : Array<string>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 设置库房关系引用
        * @method
        * @name 设置库房关系引用
        * @param  body - updateOrCreateCatClassRefList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotCatClassRefSetCatClassRef (

            parameters : {
              'body'  : Array<UpdateOrCreateCatClassRef>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量设置
        * @method
        * @name 批量设置
        * @param  body - depotInputs * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotSetDepot (

            parameters : {
              'body'  : BatchSetDepotInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 更新多条InvDepot记录
        * @method
        * @name 更新多条InvDepot记录
        * @param  body - updateInvDepotInputList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotBaseSettingUpdate (

            parameters : {
              'body'  : Array<UpdateInvDepotBaseInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询分页列表
        * @method
        * @name 查询分页列表
        * @param  body - pageableDTO * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvLimitTransGoodsPage (

            parameters : {
              'body'  : PageableDTOFindLimitGoodsInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量修改库房分类
        * @method
        * @name 批量修改库房分类
        * @param  body - idList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotCatClassDelete (

            parameters : {
              'body'  : Array<number>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 导出列表查询结果接口
        * @method
        * @name 导出列表查询结果接口
        * @param  body - queryLimitGoodsInput * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvLimitTransGoodsExportList (

            parameters : {
              'body'  : FindLimitGoodsInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 下载导入模板
        * @method
        * @name 下载导入模板
        * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getStockInvDepotDownImportTemplate (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 检测是否可以删除和禁用
        * @method
        * @name 检测是否可以删除和禁用
        * @param  body - idList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotCatClassCheckDisableOrDelete (

            parameters : {
              'body'  : Array<number>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 导出
        * @method
        * @name 导出
        * @param  body - findDepotPeriodListInput * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotPeriodExport (

            parameters : {
              'body'  : FindDepotPeriodListInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 检测库房目录是否可以删除
        * @method
        * @name 检测库房目录是否可以删除
        * @param  body - idList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotCatCheckDisableOrDelete (

            parameters : {
              'body'  : Array<number>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 新增
        * @method
        * @name 新增
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotAdd (

            parameters : {
              'body'  : AddInvDepotInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据条件查询库房目录
        * @method
        * @name 根据条件查询库房目录
        * @param  body - findInvDepotCatInput * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotCatList (

            parameters : {
              'body'  : FindInvDepotCat,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询库房分类分页关系引用
        * @method
        * @name 查询库房分类分页关系引用
        * @param  body - pageableDTO * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotCatClassRefPage (

            parameters : {
              'body'  : PageableDTOFindInvDepotByCatClassInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 库房目录详情
        * @method
        * @name 库房目录详情
        * @param  body - findInvDepotCatInput * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotCatDetail (

            parameters : {
              'body'  : FindInvDepotCat,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 关闭上一核算期
        * @method
        * @name 关闭上一核算期
        * @param  body - depotIdList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotPeriodColseLastPeriod (

            parameters : {
              'body'  : Array<number>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 删除
        * @method
        * @name 删除
        * @param  body - idList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotDelete (

            parameters : {
              'body'  : Array<number>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 导入库房列表
        * @method
        * @name 导入库房列表
        * @param file multipartFile - 导入文件 * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotImport (

            parameters : {
              'multipartFile'  ?: any,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 获取入出库类型
        * @method
        * @name 获取入出库类型
        * @param  body - selectMiscTransTypeInput * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvTransTypeList (

            parameters : {
              'body'  : SelectMiscTransTypeInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量调整限制的字段（盘点类型、是否允许负库存，是否库存数量可查等字段）
        * @method
        * @name 批量调整限制的字段（盘点类型、是否允许负库存，是否库存数量可查等字段）
        * @param  body - updateInvDepotInputList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotBaseSettingUpdateLimitField (

            parameters : {
              'body'  : Array<UpdateInvDepotBaseInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量修改库房目录
        * @method
        * @name 批量修改库房目录
        * @param  body - updateInvDepotCatInput * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotCatUpdate (

            parameters : {
              'body'  : Array<UpdateInvDepotCatInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量修改库房分类
        * @method
        * @name 批量修改库房分类
        * @param  body - updateInvDepotCatInput * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotCatClassUpdate (

            parameters : {
              'body'  : Array<UpdateInvDepotCatClassInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询分页列表
        * @method
        * @name 查询分页列表
        * @param  body - pageableDTO * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotPage (

            parameters : {
              'body'  : PageableDTOFindInvDepotByConditionInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询分页列表
        * @method
        * @name 查询分页列表
        * @param  body - pageableDTO * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvTransTypePage (

            parameters : {
              'body'  : PageableDTOFindInvTransTypeInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 下载盘点单录入模板
        * @method
        * @name 下载盘点单录入模板
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getStockInvCheckStockDownloadCheckOrderInputTemplate (

            parameters : {
              'body'  : FindInvCheckStockInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询默认分类树
        * @method
        * @name 查询默认分类树
        * @param  body - findDefaultClassTreeInput * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotCatClassListDefaultClassTree (

            parameters : {
              'body'  : FindDefaultClassTreeInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 启用
        * @method
        * @name 启用
        * @param  body - enableInvDepotList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvDepotEnabled (

            parameters : {
              'body'  : Array<EnableInvDepotInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 修改入出库商品限制
        * @method
        * @name 修改入出库商品限制
        * @param  body - updateInvTransTypeInput * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvLimitTransGoodsUpdate (

            parameters : {
              'body'  : Array<UpdateLimitTransGoodsInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 添加入出库类型
        * @method
        * @name 添加入出库类型
        * @param  body - addInvTransTypeInput * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postStockInvTransTypeAdd (

            parameters : {
              'body'  : AddInvTransTypeInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

      }
  }
}
export {}

