{"name": "serverless_runtime", "version": "1.0.0", "private": true, "scripts": {"commit": "git-cz", "watch_app": "tsc -w -p app.tsconfig.json", "build_app": "tsc -p app.tsconfig.json", "debug_app": "node --inspect=0.0.0.0:9229 dist/app.js", "download_api": "node ./cli/download_api.js", "business_build": "node ./cli/business_build.js", "init": "node ./cli/init.js", "watch": "node ./node_modules/typescript/lib/tsc-hacked.js -w -p project.tsconfig.json", "dev": "node-dev --max-old-space-size=4096 ./dist/app.js"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx,json,css,scss,md}": ["prettier --write", "git add"]}, "config": {"commitizen": {"path": "node_modules/cz-conventional-changelog"}}, "dependencies": {"@cloudbase/node-sdk": "^2.7.1", "@pagoda-tools/cos-node": "^1.0.1", "@pagoda-tools/node-helper": "0.0.4", "@tswjs/open-platform-plugin": "^1.3.1", "@tswjs/tsw": "^2.6.0", "@types/request": "^2.48.3", "axios": "^0.19.0", "connect-redis": "^4.0.3", "cookie": "0.4.0", "cookie-parser": "~1.4.3", "cookie-signature": "1.0.6", "cors": "^2.8.5", "cos-nodejs-sdk-v5": "^2.11.3", "cron": "^1.8.2", "cross-env": "^7.0.0", "debug": "~2.6.9", "depd": "~2.0.0", "ejs": "~2.5.7", "esdk-obs-nodejs": "^3.21.6", "exceljs": "^3.8.2", "express": "~4.16.0", "express-fileupload": "^1.1.7-alpha.3", "express-jwt": "^6.0.0", "express-session": "^1.17.0", "express-xml-bodyparser": "^0.3.0", "form-data": "^3.0.0", "fs-extra": "^8.1.0", "http-status-codes": "^1.4.0", "ioredis": "^4.16.0", "joi": "^17.2.1", "js-base64": "^2.5.1", "json-bigint": "^1.0.0", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.15", "logtube": "^2.2.1", "lrucache": "^1.0.3", "moment": "^2.29.1", "mongodb": "^4.7.0", "mongoose": "^6.4.4", "morgan": "~1.9.0", "mysql": "^2.17.1", "node-dev": "^7.4.3", "on-headers": "~1.0.2", "pagoda-serverless": "^1.0.15", "parseurl": "~1.3.3", "qcloud-cos-sts": "^3.0.6", "querystring": "^0.2.0", "redlock": "^4.1.0", "reflect-metadata": "^0.1.13", "request": "^2.88.0", "sa-sdk-node": "^1.2.3", "safe-buffer": "5.2.0", "sax": "^1.2.4", "tencentcloud-sdk-nodejs": "^3.0.242", "typeorm": "^0.2.22", "typescript": "^4.7.4", "uid-safe": "~2.1.5", "winston": "^3.3.3", "xlsx": "^0.16.9", "xml2js": "^0.4.22"}, "devDependencies": {"@types/connect-redis": "0.0.13", "@types/cookie-parser": "^1.4.1", "@types/cors": "^2.8.12", "@types/cron": "^1.7.1", "@types/debug": "^4.1.5", "@types/express-serve-static-core": "^4.16.1", "@types/express-session": "^1.15.16", "@types/fs-extra": "^8.0.0", "@types/http-errors": "^1.6.1", "@types/ioredis": "^4.14.7", "@types/jquery": "^3.3.31", "@types/morgan": "^1.7.35", "@types/mysql": "^2.15.7", "@types/node": "^16.11.10", "@types/redis": "^2.8.14", "@types/redlock": "^4.0.1", "adm-zip": "^0.5.10", "commitizen": "^4.0.3", "cz-conventional-changelog": "^3.1.0", "dotenv": "^8.2.0", "husky": "^3.0.5", "lint-staged": "^9.3.0", "prettier": "^1.19.1", "replace": "^1.1.5", "ts-node": "^8.3.0"}}