<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1687247835138" clover="3.2.0">
  <project timestamp="1687247835138" name="All files">
    <metrics statements="3" coveredstatements="3" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0" elements="3" coveredelements="3" complexity="0" loc="3" ncloc="3" packages="1" files="1" classes="1"/>
    <file name="index.js" path="D:\project\jest\wxapp\test\common\index.js">
      <metrics statements="3" coveredstatements="3" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <line num="1" count="1" type="stmt"/>
      <line num="2" count="1" type="stmt"/>
      <line num="9" count="1" type="stmt"/>
    </file>
  </project>
</coverage>
