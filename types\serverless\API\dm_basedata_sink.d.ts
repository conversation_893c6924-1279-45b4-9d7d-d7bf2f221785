/*this is generated code */
import { AxiosResponse} from 'axios'
declare global{
  namespace API {
    /** dm-basedata-sink
      * 文档链接：http://op.pagoda.com.cn/?#/technical-doc?docId=1721
      * {"description":"Api Documentation","version":"1.0","title":"Api Documentation","termsOfService":"urn:tos","contact":{},"license":{"name":"Apache 2.0","url":"http://www.apache.org/licenses/LICENSE-2.0"}}
      * **********:8001
    */
    namespace dm_basedata_sink {

        type LifeCycle = {

            avgCustomer?:   number

            avgPreCustomer?:   number

            lifeCyclePeriod?:   number

        }

        type ApiResultMemberLifeCycleResp = {

            data?:  MemberLifeCycleResp

            errorMsg?:   string

            resultCode:   number

        }

        type MemberLifeCycleReq = {

            datePeriodType?:   number

            memberLifeCycleType?:   number

            store?:  StoreModel

        }

        type MemberReq = {

            datePeriod?:  DatePeriodModel

            memberType?:   number

            store?:  StoreModel

        }

        type SupportedTypesRespint = {

            supported?:    Array < Descint >

        }

        type MemberLifeCycleResp = {

            lifeCycles?:    Array < LifeCycle >

        }

        type ApiResultMemberResp = {

            data?:  MemberResp

            errorMsg?:   string

            resultCode:   number

        }

        type MemberResp = {

            counter?:   number

            storeCode?:   string

        }

        type ApiResultSupportedTypesRespint = {

            data?:  SupportedTypesRespint

            errorMsg?:   string

            resultCode:   number

        }

        type DatePeriodModel = {

            datePeriodType?:   number

        }

        type Descint = {

            desc?:   string

            type?:   number

        }

        type StoreModel = {

            dateStr?:   string

            storeCode?:   string

        }



      /**description
       * Api Documentation
       */

        /**
        * fetchMemberLifeCycleResp
        * @method
        * @name fetchMemberLifeCycleResp
        * @param  body - memberLifeCycleReq
        */
        function postSinkWebDataUnionMemberV1LifeCycle (

            parameters : {
              'body'  : MemberLifeCycleReq,

            }


        ): Promise < AxiosResponse >

        /**
        * listSupportedDateRangeTypes
        * @method
        * @name listSupportedDateRangeTypes

        */
        function getSinkWebDataUnionMemberV1ListSupportedDateRangeTypes (





        ): Promise < AxiosResponse >

        /**
        * listSupportedMemGrowUpStatus
        * @method
        * @name listSupportedMemGrowUpStatus

        */
        function getSinkWebDataUnionMemberV1ListSupportedMemGrowTypes (





        ): Promise < AxiosResponse >

        /**
        * 查看会员枚举类型
        * @method
        * @name 查看会员枚举类型

        */
        function getSinkWebDataUnionMemberV1ListSupportedMemberTypes (





        ): Promise < AxiosResponse >

        /**
        * obtainMember
        * @method
        * @name obtainMember
        * @param  body - memberReq
        */
        function postSinkWebDataUnionMemberV1Number (

            parameters : {
              'body'  : MemberReq,

            }


        ): Promise < AxiosResponse >

      }
  }
}
export {}

