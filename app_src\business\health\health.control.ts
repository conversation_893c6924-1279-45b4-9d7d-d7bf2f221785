import { Router, Request, Response, NextFunction } from 'express'
import { IResponseError } from '../../interfaces/IResponseError.interface'
import * as HttpStatus from 'http-status-codes'
import message from '../../util/message'

const health: Router = Router()

health.route('/check').get((req: Request, res: Response, next: NextFunction) => {
  try {
    message.success(res, { resultCode: 0 })
  } catch (error) {
    const err: IResponseError = {
      error,
      errorCode: HttpStatus.SERVICE_UNAVAILABLE
    }
    next(err)
  }
})

export default health
