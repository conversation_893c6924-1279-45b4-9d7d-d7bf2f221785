/*this is generated code */
import  { AxiosResponse } from 'axios'
declare global{
  namespace API {
    /** mt-dm-communi
      * 文档链接：http://op.pagoda.com.cn/?#/technical-doc
      * {"description":"通信触达域","version":"1.0.0","title":"1.0.0"}
      * **********
    */
    namespace mt_dm_communi {

        type ApiResultAppPushSendRespDto = {

            data?:  AppPushSendRespDto

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultData = {

            data?:  Data

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultJSONObject = {

            data?:   any

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListCommunicationConfigOutput = {

            data?:    Array < CommunicationConfigOutput >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListCommunicationGraphResult = {

            data?:    Array < CommunicationGraphResult >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListWxRegisterResult = {

            data?:    Array < WxRegisterResult >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListstring = {

            data?:    Array < string >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultPageResultCommunicationDetailResult = {

            data?:  PageResultCommunicationDetailResult

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultProxyDelayTask对象 = {

            data?:  ProxyDelayTask对象

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultProxyResult = {

            data?:  ProxyResult

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultSmsDelayTaskDto = {

            data?:  SmsDelayTaskDto

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultSmsResultData = {

            data?:  SmsResultData

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultSmsTaskResult = {

            data?:  SmsTaskResult

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultSuccessRespDto = {

            data?:  SuccessRespDto

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultWxRegisterResult = {

            data?:  WxRegisterResult

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultboolean = {

            data?:   boolean

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultlong = {

            data?:   number

            errorMsg?:   string

            resultCode:   number

        }

        type AppPushSendReqDto = {

            appID:   string

            message:   any

            options?:   any

            receiver:  Receiver

            reported?:   boolean

            type:   string

        }

        type AppPushSendRespDto = {

            taskId?:   string

        }

        type CallAiVoiceReqDto = {

            dealType?:   number

            ivrRouterId?:   number

            numberList?:    Array < Number >

            outboundTaskId?:   number

        }

        type CallAiVoiceV2ReqDto = {

            appID?:   string

            numberList?:    Array < Number >

            outboundTaskId?:   number

            pid?:   string

        }

        type CommunicationConfigInput = {

            parentKey?:   string

            type?:   number

        }

        type CommunicationConfigOutput = {

            key?:   string

            value?:   string

        }

        type CommunicationDetailInput = {

            application?:   string

            channel?:   string

            endDate?:   string

            page?:   number

            size?:   number

            startDate?:   string

            supplier?:   string

            type?:   string

        }

        type CommunicationDetailResult = {

            application?:   string

            channel?:   string

            failCount?:   number

            requestCount?:   number

            sendDate?:   string

            successCount?:   number

            supplier?:   string

            taskCount?:   number

            totalCount?:   number

            type?:   string

        }

        type CommunicationGraphInput = {

            application?:   string

            channel?:   string

            endDate?:   string

            metric?:   number

            spacing?:   number

            startDate?:   string

            supplier?:   string

            type?:   string

        }

        type CommunicationGraphResult = {

            items?:    Array < Item >

            label?:   string

        }

        type CommunicationReportInput = {

            application?:   string

            channel?:   string

            requestCount?:   number

            sendDate?:   string

            successCount?:   number

            supplier?:   string

            taskCount?:   number

            totalCount?:   number

            type?:   string

        }

        type Data = {

            failedCount?:   number

            failedNumberList?:    Array < FailedNumber >

            httpErrorCode?:   number

            successCount?:   number

        }

        type DeduplicationInfo = {

            limit?:   number

            strategy?:   number

        }

        type DelayInfo = {

            absoluteTime?:   string

            relativeTime?:   number

        }

        type ErrorSmsRecord = {

            errorCode?:   string

            errorDesc?:   string

            key?:   string

            phoneNum?:   string

            smsContent?:   string

            status?:   string

            type?:   string

        }

        type FailedNumber = {

            failedReason?:   string

            mobile?:   string

            name?:   string

            remark?:   string

            variates?:   string

        }

        type Item = {

            date?:   string

            value?:   number

        }

        type JSONObject = {

        }

        type Number = {

            mobile?:   string

            name?:   string

            remark?:   string

            variates?:    Array < Variate >

        }

        type OneDetail = {

            content?:   string

            name?:   string

            receiver?:  Receiver

        }

        type PageResultCommunicationDetailResult = {

            items?:    Array < CommunicationDetailResult >

            page?:   number

            size?:   number

            total?:   number

        }

        type ProxyDelayTask对象 = {

            absoluteTime?:   string

            createTime?:   string

            deleted?:   number

            id?:   number

            proxyResult?:   string

            proxyType?:   number

            status?:   string

            updateTime?:   string

        }

        type ProxyResult = {

            absoluteTime?:   string

            result?:   any

            status?:   string

        }

        type PushMsgReqDto = {

            appId?:   string

            content?:   any

            deduplication?:  DeduplicationInfo

            delay?:  DelayInfo

            ext?:   any

            receivers?:    Array < Receiver >

            report?:   boolean

            sendDate?:   string

            taskId?:   number

            type?:   string

        }

        type QueryPushMsgResultDto = {

            taskId?:   string

            type?:   string

        }

        type Receiver = {

            key?:   string

            type?:   string

        }

        type SaveDevicesReqDto = {

            alias?:   string

            appID:   string

            clientID?:   string

            mobile?:   string

            tags?:  Tag

            type:   string

        }

        type SmsDelayTaskDto = {

            absoluteTime?:   string

            createTime?:   string

            deleted?:   number

            id?:   number

            status?:   string

            updateTime?:   string

        }

        type SmsDelayTask对象 = {

            absoluteTime?:   string

            createTime?:   string

            deleted?:   number

            id?:   number

            status?:   string

            updateTime?:   string

        }

        type SmsRecord = {

            failedCode?:   string

            failedReason?:   string

            key?:   string

            mobile?:   string

            name?:   string

            type?:   string

        }

        type SmsReqDto = {

            callbackUrl?:   string

            channel?:   string

            content?:   string

            deduplication?:  DeduplicationInfo

            delay?:  DelayInfo

            ext?:   string

            flag?:   number

            forcedSend?:   boolean

            name?:   string

            numberList?:    Array < OneDetail >

            receiver?:  Receiver

            scene?:   number

            supplier?:   string

        }

        type SmsResultData = {

            msgCount?:   number

            taskId?:   string

        }

        type SmsSendBatchAsyncInput = {

            appId?:   string

            callbackUrl?:   string

            channelNumber?:   string

            deduplication?:  DeduplicationInfo

            directSendTime?:   string

            errorSmsRecords?:    Array < ErrorSmsRecord >

            ext?:   string

            flag?:   number

            forcedSend?:   boolean

            scene?:   number

            supplier?:   string

            taskDetails?:    Array < SmsSendDetails >

            taskId?:   number

        }

        type SmsSendDetails = {

            content?:   string

            key?:   string

            name?:   string

            phoneNumber?:   string

            type?:   string

        }

        type SmsSendInput = {

            appId?:   string

            callbackUrl?:   string

            channelNumber?:   string

            content?:   string

            deduplication?:  DeduplicationInfo

            directSendTime?:   string

            errorSmsRecords?:    Array < ErrorSmsRecord >

            ext?:   string

            flag?:   number

            forcedSend?:   boolean

            key?:   string

            name?:   string

            phoneNumber?:   string

            scene?:   number

            supplier?:   string

            taskId?:   number

            type?:   string

        }

        type SmsTaskResult = {

            absoluteTime?:   string

            failedCount?:   number

            failedNumberList?:    Array < SmsRecord >

            status?:   string

            successCount?:   number

        }

        type SmsVoiceReqDto = {

            channel?:   string

            content?:   string

            mobile?:   string

            name?:   string

            supplier?:   string

        }

        type SpecialSmsReqDto = {

            callbackUrl?:   string

            channel?:   string

            content?:   string

            deduplication?:  DeduplicationInfo

            delay?:  DelayInfo

            ext?:   string

            flag?:   number

            forcedSend?:   boolean

            mobile?:   string

            name?:   string

            numberList?:    Array < OneDetail >

            scene?:   number

            supplier?:   string

        }

        type SuccessRespDto = {

            status?:   boolean

        }

        type Tag = {

            add?:    Array < string >

            remove?:    Array < string >

        }

        type Timestamp = {

            date?:   number

            day?:   number

            hours?:   number

            minutes?:   number

            month?:   number

            nanos?:   number

            seconds?:   number

            time?:   number

            timezoneOffset?:   number

            year?:   number

        }

        type Variate = {

            key?:   string

            type?:   string

            value?:   string

        }

        type WeworkReqDto = {

            agentId?:   string

            appId?:   string

            content?:   any

            corpId?:   string

            delay?:  DelayInfo

            directSendTime?:   string

            method?:   string

            targetUrl?:   string

            taskId?:   number

        }

        type WxGenerateUrlReqDto = {

            scope?:   string

            targetUrl?:   string

            timeout?:   number

            userId?:   string

            valid?:   boolean

            wxOrg?:   string

        }

        type WxProxyReqDto = {

            appId?:   string

            content?:   any

            delay?:  DelayInfo

            directSendTime?:   string

            method?:   string

            targetUrl?:   string

            taskId?:   number

            userId?:   string

            wxOrg?:   string

        }

        type WxRegisterReqDto = {

            callbackUrl?:   string

            contentType?:   string

            forwardMethod?:   string

            msgType?:   string

            msgValue?:   string

            registerId?:   number

            userId?:   string

            wxOrg?:   string

        }

        type WxRegisterResult = {

            callbackUrl?:   string

            contentType?:   string

            createTime?:  Timestamp

            forwardMethod?:   string

            lastUpdate?:  Timestamp

            msgType?:   string

            msgValue?:   string

            registerId?:   string

            status?:   string

            userId?:   string

            wxChannelId?:   string

            wxOrg?:   string

        }



      /**description
       * 通信触达域
       */

        /**
        * callAIVoicePhone
        * @method
        * @name callAIVoicePhone
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1ConsumerConventionalCallAiVoicePhone (

            parameters : {
              'body'  : CallAiVoiceReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * querySenSmsTask
        * @method
        * @name querySenSmsTask
        * @param string channel - channel * @param string taskId - taskId * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1ConsumerConventionalQuerySendSmsTask (

            parameters : {
              'channel'  ?: string,
              'taskId'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * sendSpecialSms
        * @method
        * @name sendSpecialSms
        * @param  body - specialSmsReqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1ConsumerConventionalSendSpecialSms (

            parameters : {
              'body'  : SpecialSmsReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * sendVoiceSms
        * @method
        * @name sendVoiceSms
        * @param  body - smsVoiceReqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1ConsumerConventionalSendVoiceSms (

            parameters : {
              'body'  : SmsVoiceReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * pushMsg
        * @method
        * @name pushMsg
        * @param  body - pushMsgReqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1ConsumerMessagePush (

            parameters : {
              'body'  : PushMsgReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryPushResult
        * @method
        * @name queryPushResult
        * @param  body - queryPushMsgResultDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1ConsumerMessageQueryResult (

            parameters : {
              'body'  : QueryPushMsgResultDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryProxyResult
        * @method
        * @name queryProxyResult
        * @param string taskId - taskId * @param  x-pagoda-envoy-route-project -
        */
        function getApiV1ConsumerProxyQueryResult (

            parameters : {
              'taskId'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * weworkProxy
        * @method
        * @name weworkProxy
        * @param  body - weworkReqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1ConsumerWeworkProxy (

            parameters : {
              'body'  : WeworkReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * wxForwardList
        * @method
        * @name wxForwardList
        * @param string userId - userId * @param string wxOrg - wxOrg * @param  x-pagoda-envoy-route-project -
        */
        function getApiV1ConsumerWxForwardList (

            parameters : {
              'userId'  : string,
              'wxOrg'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * wxForwardRegister
        * @method
        * @name wxForwardRegister
        * @param  body - wxRegisterReqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1ConsumerWxForwardRegister (

            parameters : {
              'body'  : WxRegisterReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * wxForwardRemove
        * @method
        * @name wxForwardRemove
        * @param  body - wxRegisterReqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1ConsumerWxForwardRemove (

            parameters : {
              'body'  : WxRegisterReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * wxForwardUpdate
        * @method
        * @name wxForwardUpdate
        * @param  body - wxRegisterReqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1ConsumerWxForwardUpdate (

            parameters : {
              'body'  : WxRegisterReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * wxGenerateOauthUrl
        * @method
        * @name wxGenerateOauthUrl
        * @param  body - wxGenerateUrlReqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1ConsumerWxGenerateOauthUrl (

            parameters : {
              'body'  : WxGenerateUrlReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * wxProxy
        * @method
        * @name wxProxy
        * @param  body - wxProxyReqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1ConsumerWxProxy (

            parameters : {
              'body'  : WxProxyReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * saveDevices
        * @method
        * @name saveDevices
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1ConventionalAppPushSaveDevices (

            parameters : {
              'body'  : SaveDevicesReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * smsReport
        * @method
        * @name smsReport
        * @param string smsFlag - smsFlag * @param  x-pagoda-envoy-route-project -
        */
        function getApiV1ExternalReportSmsBySmsFlag (

            parameters : {
              'smsFlag'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * smsReport
        * @method
        * @name smsReport
        * @param string smsFlag - smsFlag
        */
        function postApiV1ExternalReportSmsBySmsFlag (

            parameters : {
              'smsFlag'  : string,

            }


        ): Promise < AxiosResponse >

        /**
        * aiVoiceTelephoneCloseTask
        * @method
        * @name aiVoiceTelephoneCloseTask
        * @param  x-pagoda-envoy-route-project -
        */
        function getApiV1ExternalScheduleServiceAiVoiceTelephoneCloseTask (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * smsUpload
        * @method
        * @name smsUpload
        * @param string smsFlag - smsFlag * @param  x-pagoda-envoy-route-project -
        */
        function getApiV1ExternalUploadSmsBySmsFlag (

            parameters : {
              'smsFlag'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * smsUpload
        * @method
        * @name smsUpload
        * @param string smsFlag - smsFlag
        */
        function postApiV1ExternalUploadSmsBySmsFlag (

            parameters : {
              'smsFlag'  : string,

            }


        ): Promise < AxiosResponse >

        /**
        * communicationConfig
        * @method
        * @name communicationConfig
        * @param  body - communicationConfigInput * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1ReportCommunicationConfig (

            parameters : {
              'body'  : CommunicationConfigInput,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * communicationDetail
        * @method
        * @name communicationDetail
        * @param  body - communicationDetailInput * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1ReportCommunicationDetail (

            parameters : {
              'body'  : CommunicationDetailInput,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * communicationGraph
        * @method
        * @name communicationGraph
        * @param  body - communicationGraphInput * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1ReportCommunicationGraph (

            parameters : {
              'body'  : CommunicationGraphInput,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * callAiVoicePhone
        * @method
        * @name callAiVoicePhone
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV2ConsumerConventionalCallAiVoicePhone (

            parameters : {
              'body'  : CallAiVoiceV2ReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * sendSpecialSms
        * @method
        * @name sendSpecialSms
        * @param  body - smsReqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV2ConsumerConventionalSendSpecialSms (

            parameters : {
              'body'  : SmsReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * send
        * @method
        * @name send
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV2ConventionalAppPushSend (

            parameters : {
              'body'  : AppPushSendReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * saveDevices
        * @method
        * @name saveDevices
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postRpcV1ConventionalAppPushSaveDevices (

            parameters : {
              'body'  : SaveDevicesReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * send
        * @method
        * @name send
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postRpcV1ConventionalAppPushSend (

            parameters : {
              'body'  : AppPushSendReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryTask
        * @method
        * @name queryTask
        * @param string taskId - taskId * @param  x-pagoda-envoy-route-project -
        */
        function getRpcV1ProxyQueryTask (

            parameters : {
              'taskId'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * saveTask
        * @method
        * @name saveTask
        * @param  body - proxyDelayTask * @param  x-pagoda-envoy-route-project -
        */
        function postRpcV1ProxySaveTask (

            parameters : {
              'body'  : ProxyDelayTask对象,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * updateTask
        * @method
        * @name updateTask
        * @param  body - proxyDelayTask * @param  x-pagoda-envoy-route-project -
        */
        function postRpcV1ProxyUpdateTask (

            parameters : {
              'body'  : ProxyDelayTask对象,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * communicationDetail
        * @method
        * @name communicationDetail
        * @param  body - communicationDetailInput * @param  x-pagoda-envoy-route-project -
        */
        function postRpcV1ReportCommunicationDetail (

            parameters : {
              'body'  : CommunicationDetailInput,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * communicationGraph
        * @method
        * @name communicationGraph
        * @param  body - communicationGraphInput * @param  x-pagoda-envoy-route-project -
        */
        function postRpcV1ReportCommunicationGraph (

            parameters : {
              'body'  : CommunicationGraphInput,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * updateReport
        * @method
        * @name updateReport
        * @param  body - communicationReportInput * @param  x-pagoda-envoy-route-project -
        */
        function postRpcV1ReportCommunicationUpdate (

            parameters : {
              'body'  : CommunicationReportInput,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * aiVoiceTelephoneCloseTask
        * @method
        * @name aiVoiceTelephoneCloseTask
        * @param  x-pagoda-envoy-route-project -
        */
        function getRpcV1ScheduleServiceAiVoiceTelephoneCloseTask (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * checkInput
        * @method
        * @name checkInput
        * @param string channel - channel * @param integer scene - scene * @param  x-pagoda-envoy-route-project -
        */
        function postRpcV1SmsServiceCheckInput (

            parameters : {
              'channel'  : string,
              'scene'  : number,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getTaskId
        * @method
        * @name getTaskId
        * @param  x-pagoda-envoy-route-project -
        */
        function getRpcV1SmsServiceGetTaskId (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * hasDelayTask
        * @method
        * @name hasDelayTask
        * @param string taskId - taskId * @param  x-pagoda-envoy-route-project -
        */
        function getRpcV1SmsServiceHasDelayTask (

            parameters : {
              'taskId'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * localSend
        * @method
        * @name localSend
        * @param  x-pagoda-envoy-route-project -
        */
        function postRpcV1SmsServiceLocalSend (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * ping
        * @method
        * @name ping
        * @param  body - jsonObject * @param  x-pagoda-envoy-route-project -
        */
        function postRpcV1SmsServicePing (

            parameters : {
              'body'  : any,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryChannels
        * @method
        * @name queryChannels
        * @param integer serviceProvider - serviceProvider * @param  x-pagoda-envoy-route-project -
        */
        function postRpcV1SmsServiceQueryChannelsByServiceProvider (

            parameters : {
              'serviceProvider'  : number,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * querySendSmsTask
        * @method
        * @name querySendSmsTask
        * @param string taskId - taskId * @param  x-pagoda-envoy-route-project -
        */
        function postRpcV1SmsServiceQuerySendSmsTask (

            parameters : {
              'taskId'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * saveDelayDiffContent
        * @method
        * @name saveDelayDiffContent
        * @param  body - smsSendBatchAsyncInput * @param  x-pagoda-envoy-route-project -
        */
        function postRpcV1SmsServiceSaveDelayDiffContent (

            parameters : {
              'body'  : SmsSendBatchAsyncInput,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * saveDelaySameContent
        * @method
        * @name saveDelaySameContent
        * @param  body - smsSendInput * @param  x-pagoda-envoy-route-project -
        */
        function postRpcV1SmsServiceSaveDelaySameContent (

            parameters : {
              'body'  : SmsSendInput,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * saveTaskId
        * @method
        * @name saveTaskId
        * @param  body - smsDelayTask * @param  x-pagoda-envoy-route-project -
        */
        function postRpcV1SmsServiceSaveTaskId (

            parameters : {
              'body'  : SmsDelayTask对象,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * smsSendAsync
        * @method
        * @name smsSendAsync
        * @param  body - input * @param  x-pagoda-envoy-route-project -
        */
        function postRpcV1SmsServiceSendSmsAsync (

            parameters : {
              'body'  : SmsSendInput,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * smsSendBatchAsync
        * @method
        * @name smsSendBatchAsync
        * @param  body - input * @param  x-pagoda-envoy-route-project -
        */
        function postRpcV1SmsServiceSendSmsBatchAsync (

            parameters : {
              'body'  : SmsSendBatchAsyncInput,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * smsSendSync
        * @method
        * @name smsSendSync
        * @param  body - input * @param  x-pagoda-envoy-route-project -
        */
        function postRpcV1SmsServiceSendSmsSync (

            parameters : {
              'body'  : SmsSendInput,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * smsReport
        * @method
        * @name smsReport
        * @param integer id - id * @param  body - requestData * @param  x-pagoda-envoy-route-project -
        */
        function postRpcV1SmsServiceSmsReportById (

            parameters : {
              'id'  : number,
              'body'  : any,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * smsUpload
        * @method
        * @name smsUpload
        * @param integer id - id * @param  body - jsonObject * @param  x-pagoda-envoy-route-project -
        */
        function postRpcV1SmsServiceSmsUploadById (

            parameters : {
              'id'  : number,
              'body'  : any,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * updateDelayTask
        * @method
        * @name updateDelayTask
        * @param integer taskId - taskId * @param  x-pagoda-envoy-route-project -
        */
        function postRpcV1SmsServiceUpdateDelayTask (

            parameters : {
              'taskId'  : number,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * callAiVoice
        * @method
        * @name callAiVoice
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postRpcV2TelephoneCallAiVoice (

            parameters : {
              'body'  : CallAiVoiceV2ReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

      }
  }
}
export {}

