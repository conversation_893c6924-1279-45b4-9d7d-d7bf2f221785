import { Entity, Column } from 'typeorm'

@Entity('function')
export class FunctionEntity {
  @Column('varchar', { length: 50 })
  project_name: string

  @Column('varchar', { length: 50 })
  module_name: string

  @Column('varchar', { length: 100 })
  function_name: string

  @Column('varchar', { length: 10, default: 'GET', comment: '请求方法 GET 或 POST 或其他' })
  method: string

  @Column('tinyint', {
    width: 2,
    default: 2,
    comment: '0：不接入单点登录 1：接入单点登录 2：默认，即由所属项目的caslogin决定'
  })
  caslogin?: number
}

export var CASLOGIN = {
  DEFAULT: undefined,
  ENABLE: true,
  DISABLE: false
}
