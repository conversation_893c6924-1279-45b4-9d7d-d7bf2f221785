import fs from 'fs-extra'
import path from 'path'
import { getRepository, Repository } from 'typeorm'
import { messageClient } from '../../util/communication'
import Log from '../../util/Log'
import cleanCache from '../../util/util'
import { MGCode } from './middleground.code.entity'

export class MiddleGroundService {
  mgCodeRepository: Repository<MGCode>
  constructor() {
    this.mgCodeRepository = getRepository(MGCode)
  }

  async getJSCode(env: string, service_key?: string) {
    const list = await this.mgCodeRepository
      .createQueryBuilder('mgCode')
      .addFrom(subQuery => {
        return subQuery
          .select('sub.mgservice', 'mgservice')
          .addSelect('MAX(sub.version)', 'maxVersion')
          .from(MGCode, 'sub')
          .where(`is_delete=0 AND env= :env ${service_key ? ' AND mgservice= :mgservice' : ''}`, {
            env,
            mgservice: service_key
          })
          .groupBy('sub.mgservice')
      }, 'sub')
      .where(
        `mgCode.mgservice = sub.mgservice AND mgCode.version = sub.maxVersion AND mgCode.is_delete = 0 AND mgCode.env= :env ${
          service_key ? ' AND mgCode.mgservice= :mgservice' : ''
        }`,
        {
          env,
          mgservice: service_key
        }
      )
      .getMany()

    return (list as MGCode[]).map(data => {
      //@ts-ignore
      delete data.dts_content
      return data
    })
  }
  getJSCodeFromDisk(env: string, service_key?: string): Partial<MGCode>[] {
    var list: Partial<MGCode>[] = []

    var fileNames = fs.readdirSync(path.resolve(__dirname, '../../loaded_middleground_code'))
    // 只加载特定的库
    if (service_key) {
      if (fileNames.includes(service_key + '.js')) {
        return [
          {
            mgservice: service_key,
            js_content: fs
              .readFileSync(path.resolve(__dirname, '../../loaded_middleground_code', service_key + '.js'))
              .toString()
          }
        ]
      } else {
        return []
      }
    } else {
      fileNames.forEach(filename => {
        var service_key = filename.slice(0, -3)
        list.push({
          mgservice: service_key,
          js_content: fs
            .readFileSync(path.resolve(__dirname, '../../loaded_middleground_code', service_key + '.js'))
            .toString()
        })
      })
    }
    return list
  }

  async loadMiddleGroundJSCode() {
    //中台各个环境的域名
    global['MIDDLE_SERVICE_ENV'] = {
      draft: 'http://envoyproxy.diep.kd1.pagoda.com.cn',
      dev: 'http://envoyproxy.diep.kd1.pagoda.com.cn',
      test: 'http://envoyproxy.diep.kt3.pagoda.com.cn',
      uat: 'http://envoyproxy.diep.ks1.pagoda.com.cn',
      drill: 'http://10.8.49.23:9090',
      prod: 'http://envoy-dm.serverless:9090'
    }

    this.generateMiddleServiceCode(process.env.API_ENV || 'dev')
  }

  async onLoadMiddleGroundJScode() {
    messageClient.subscribe('svl_middleGround')
    messageClient.on('message', async (channel, message) => {
      if (channel === 'svl_middleGround') {
        var data = JSON.parse(message)
        if (data.env == (process.env.API_ENV || 'prod')) {
          this.generateMiddleServiceCode(data.env, data.service_key)
        }
      }
    })
  }

  async generateMiddleServiceCode(env: string, service_key?: string) {
    const codes = await this.getJSCodeFromDisk(env, service_key)
    if (!service_key) {
      //加载所有中台代码的时候
      Log.log('loadMiddlegroundJSCode...start:' + env)

      global['API_draft'] = {}
      global['API_dev'] = {}
      global['API_test'] = {}
      global['API_uat'] = {}
      global['API_drill'] = {}
      global['API_prod'] = {}

      fs.emptyDirSync(path.resolve(__dirname, '../../loaded_middleground_code'))
    } else {
      Log.log(`loadMiddlegroundJSCode...${env}:${service_key}`)
    }

    if (service_key && codes.length == 0) {
      // 表示该中台接口被删除了

      const relativePath = '../../loaded_middleground_code/' + service_key + '.js'
      const fullName = path.resolve(__dirname, relativePath)

      fs.removeSync(fullName)
      cleanCache(path.resolve(__dirname, relativePath))

      global['API_draft'][service_key] = undefined
      global['API_dev'][service_key] = undefined
      global['API_test'][service_key] = undefined
      global['API_uat'][service_key] = undefined
      global['API_drill'][service_key] = undefined
      global['API_prod'][service_key] = undefined
      Log.log('loadMiddleGroundJSCode...delete end')
      return
    }
    codes.forEach(function(gen_code) {
      const relativePath = '../../loaded_middleground_code/' + gen_code.mgservice + '.js'
      const fullName = path.resolve(__dirname, relativePath)

      fs.ensureFileSync(fullName)
      fs.writeFileSync(fullName, gen_code.js_content!)

      cleanCache(path.resolve(__dirname, relativePath))
      var apiinstance = require(relativePath).default

      global['API_draft'][gen_code.mgservice] = { ...apiinstance, env: 'draft' }
      global['API_dev'][gen_code.mgservice] = { ...apiinstance, env: 'dev' }
      global['API_test'][gen_code.mgservice] = { ...apiinstance, env: 'test' }
      global['API_uat'][gen_code.mgservice] = { ...apiinstance, env: 'uat' }
      global['API_drill'][gen_code.mgservice] = { ...apiinstance, env: 'drill' }
      global['API_prod'][gen_code.mgservice] = { ...apiinstance, env: 'prod' }

      Log.log('loadMiddleGroundJSCode...:', gen_code.mgservice)
    })
    Log.log('loadMiddleGroundJSCode...end')
  }
}
