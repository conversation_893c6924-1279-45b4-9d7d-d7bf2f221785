import fs from 'fs-extra'
import path from 'path'
import { Request, Response, NextFunction } from 'express'
import { RedisCache } from '../../util/redisCache'

import createContext from './run.type'
import Log, { getAppProjectName } from '../../util/Log'
import cleanCache, { parseOriginalUrl } from '../../util/util'
import config from '../../config'

export class ExcService {
  async exc(req: Request, res: Response, next: NextFunction) {
    const serverlessConfig = getProjectInfo(req.originalUrl)

    var data = parseOriginalUrl(req.url)
    const env = data.env ?? ''
    const realProjectName = data.project_name //20190806005_demo 等
    const redisCache = new RedisCache(realProjectName, env!)

    function handleMiddleware(name: string) {
      return new Promise<void>(function(resolve, reject) {
        const middlewarePath = path.resolve(
          `${config.CODE_DIST_PATH}`,
          realProjectName,
          serverlessConfig.project_code_dir,
          `__common/${name}.js`
        )

        //1先找一下当前项目下有没有中间件
        if (fs.existsSync(middlewarePath)) {
          // 如果有的话把内容复制到代码缓存文件夹里面，然后cleanCache 之后，重新require 对应的代码

          var fullPath = copyCacheCodeAndCleanRequireCache(
            realProjectName,
            serverlessConfig.project_code_dir,
            `./__common/${name}.js`
          )

          var middleware = require(fullPath)
          middleware.__serverless_runtime_export_function(
            ...createContext({
              req,
              res,
              next,
              redisCache,
              project: realProjectName,
              env,
              middlewareCallback: async function middlewareCallback(flag: boolean) {
                if (flag) {
                  resolve()
                }
              }
            })
          )
        } else {
          //中间件不存在直接往后执行
          resolve()
        }
      })
    }

    function handleRun() {
      try {
        run({
          req,
          res,
          next,
          redisCache,
          project: realProjectName,
          env,
          project_code_dir: serverlessConfig.project_code_dir
        })
      } catch (error) {
        errorHandler(error, req, res)
        error.hasLogged = true
        throw error
      }
    }

    //1.先执行 before 中间件
    //2.校验函数的是否存在和 method
    // 不判断了，直接调，调不了就报错 这样就不用在serverless.config.js 文件中配置了
    //3.执行 middleware 中间件
    //4.执行函数具体的代码

    await handleMiddleware('beforeware')
    // handleFnValiate()
    await handleMiddleware('middleware')
    handleRun()
  }
}
/** 返回清除的全路径 D:\xxx\xxx.js */
function copyCacheCodeAndCleanRequireCache(
  projectName: string,
  project_code_dir: string,
  relativePath: string
): string {
  // var distPath = path.resolve(__dirname, `../../loaded_function_code/${projectName}`, project_code_dir, relativePath)
  var originPath = path.resolve(config.CODE_DIST_PATH!, projectName, project_code_dir, relativePath)
  return excCopyCacheCodeAndCleanRequireCache(originPath)
}

export function GlobalCopyCacheCodeAndCleanRequireCache(dirname: string, url: string) {
  var path = require('path')

  if (!url.endsWith('.js') && !url.endsWith('.json')) {
    url += '.js'
  }
  var distPath = path.resolve(dirname, url)
  var originPath = path.resolve(
    dirname.replace(path.resolve(__dirname, '../../loaded_function_code'), config.CODE_DIST_PATH!),
    url
  )
  excCopyCacheCodeAndCleanRequireCache(distPath)
}

function excCopyCacheCodeAndCleanRequireCache(originPath: string) {
  // ensureFileSync(distPath)
  // var content = readFileSync(originPath)
  // writeFileSync(
  //   distPath,
  //   content
  // )

  cleanCache(originPath)
  return originPath
}

function run({ req, res, next, redisCache, project, env, project_code_dir }) {
  var data = parseOriginalUrl(req.url)
  var fullPath = copyCacheCodeAndCleanRequireCache(project, project_code_dir, data.module_file_name + '.js')

  require(fullPath).__serverless_runtime_export_function(...createContext({ req, res, next, redisCache, project, env }))
}

export function getCasInfo(originalUrl: string): boolean {
  var parsedInfo = parseOriginalUrl(originalUrl)
  var path = `${config.CODE_PATH}/${parsedInfo.project_name}/serverless.config.js`
  path = require('path').resolve(path)
  console.log(path)
  cleanCache(path)
  var s_config: ServerlessConfig = require(path)
  var func_data = s_config.url[parsedInfo.module_file_name]

  if (func_data?.caslogin != undefined) {
    return func_data?.caslogin
  }
  return s_config.project_caslogin ?? false
}

export function getProjectInfo(originalUrl: string): ServerlessConfig {
  var parsedInfo = parseOriginalUrl(originalUrl)
  var path = `${config.CODE_PATH}/${parsedInfo.project_name}/serverless.config.js`
  path = require('path').resolve(path)
  console.log(path)
  cleanCache(path)
  var s_config: ServerlessConfig = require(path)
  if (!s_config) {
    throw new Error('缺少 serverless.config.js 文件')
  }
  return s_config
}

function errorHandler(error, req?, res?) {
  var errorMessage = ''
  if (error.code == 'MODULE_NOT_FOUND') {
    errorMessage = '接口调用失败:MODULE_NOT_FOUND:404'
  }
  Log.error(errorMessage || '接口调用抛出错误:', {
    app_project_name: getAppProjectName(req.originalUrl),
    originalUrl: req.originalUrl || '',
    message: error.message || error.msg || error,
    trackId: req?.query?.trackId ?? req?.body?.trackId,
    crid: res.locals.crid || '',
    stack: error.stack
  })
  error.hasLogged = true
}

function warnHandler(error: Error, req?, res?) {
  Log.warn(error.message, {
    app_project_name: getAppProjectName(req.originalUrl),
    originalUrl: req.originalUrl || '',
    message: error.message,
    trackId: req?.query?.trackId ?? req?.body?.trackId,
    crid: res.locals.crid || '',
    stack: error.stack
  })
  error['hasLogged'] = true
}
