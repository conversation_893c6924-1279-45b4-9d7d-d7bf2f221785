import config from '../config'
import Redis from 'ioredis'
import logtube from './logtube'

const redisClient = new Redis({ ...config.redis, keyPrefix: 'serverless_runtime_debug_log:' })
async function logIntoRedis(trackId: string, message: string) {
  redisClient.rpush(trackId, message)
  redisClient.expire(trackId, 600)
}

//TODO 还是没处理 {error:Error}的情况 需要循环处理
function args2string(args: any[]): string {
  var msgs: any[] = []
  for (let index = 0; index < args.length; index++) {
    const element = args[index]

    switch (getType(element)) {
      case 'Error':
        msgs.push(
          JSON.stringify({ name: element.name ?? '', stack: element.stack ?? '', message: element.message ?? '' })
        )
        break
      case 'Object':
        try {
          msgs.push(JSON.stringify(element))
        } catch (error) {
          msgs.push('' + element) //不输出日志 防止循环调用
        }
        break
      default:
        msgs.push('' + element)
        break
    }
  }
  return msgs.join('|||')
}

function getType(el: any): string {
  return Object.prototype.toString.call(el).split(/[\]\s]/g)[1]
}

/**
 * Log.xxx 是同时往控制台和xlog平台写日志
 * xlog 是只往xlog平台写日志 console 写日志
 * console 是只往控制台写日志 和 grafana 写日志
 */
var Log = {
  user_operation_log: function(req: Express.Request, ...args) {
    let user_id = req.session?.userInfo?.user ?? ''
    let user_name = req.session?.userInfo?.attributes?.employeeName?.[0] ?? ''
    Log.log('user_operation_log:', user_id, user_name, ...args)
  },
  debug: function(message?: any, ...optionalParams: any[]) {
    xlog.debug(...arguments)
  },
  info: function(message?: any, ...optionalParams: any[]) {
    xlog.info(...arguments)
  },
  log: function(message?: any, ...optionalParams: any[]) {
    xlog.log(...arguments)
  },
  warn: function(message?: any, ...optionalParams: any[]) {
    xlog.warn(...arguments)
  },
  error: function(message?: any, ...optionalParams: any[]) {
    xlog.error(...arguments)
  }
}

var xlog = {
  debug: function(message?: any, ...optionalParams: any[]) {
    const messages = [...arguments]
    const inputs = messages.slice(0, message.length)
    console.debug(...inputs)
    xlog.common('info', ...arguments)
  },
  info: function(message?: any, ...optionalParams: any[]) {
    const messages = [...arguments]
    const inputs = messages.slice(0, message.length)
    console.info(...inputs)
    xlog.common('info', ...arguments)
  },
  log: function(message?: any, ...optionalParams: any[]) {
    const messages = [...arguments]
    const inputs = messages.slice(0, message.length)
    console.log(...inputs)
    xlog.common('info', ...arguments)
  },
  warn: function(message?: any, ...optionalParams: any[]) {
    console.warn(...arguments)
    xlog.common('warn', ...arguments)
  },
  error: function(message?: any, ...optionalParams: any[]) {
    const messages = [...arguments]
    const inputs = messages.slice(0, message.length)
    console.error(...inputs)
    xlog.common('err', ...arguments)
  },
  common: function(logLevel: string, ...args) {
    let info = args2string(args)
    let stack = ''
    var last = args[args.length - 1]
    var module = last?.app_project_name ?? getProjectNameFromMessage(info)
    if (module) {
      logtube
        .event(logLevel)
        .x('module', module)
        .k(info)
        .msg(stack || '')
    } else {
      logtube
        .event(logLevel)
        .k(info)
        .msg(stack || '')
    }
    // logtube.log(logLevel, info, stack || '')
  }
}

export function getAppProjectName(originalUrl: string) {
  // /exc/xxx/....
  return originalUrl?.split('/')?.[2] || ''
}

var re = /^\/[^\/]*exc\/([^/]*)\//
export function getProjectName(originalUrl: string): string {
  // /exc/xxx/....
  var result = re.exec(originalUrl)
  return result?.[1] ?? ''
}

var re2 = /\/[0-9a-zA-Z_]*exc\/([0-9a-zA-Z_]*)\//
export function getProjectNameFromMessage(message: string): string {
  // /exc/xxx/....
  var result = re2.exec(message)
  return result?.[1] ?? ''
}

export { xlog }

export default Log
