{"compilerOptions": {"module": "commonjs", "esModuleInterop": true, "target": "es6", "noImplicitAny": false, "moduleResolution": "node", "allowJs": true, "sourceMap": true, "outDir": "dist/project_code", "rootDir": "project_code", "strict": true, "strictNullChecks": true, "emitDecoratorMetadata": true, "strictPropertyInitialization": false, "experimentalDecorators": true, "resolveJsonModule": true, "baseUrl": ".", "skipDefaultLibCheck": true, "assumeChangesOnlyAffectDirectDependencies": true, "skipLibCheck": true, "typeRoots": ["./node_modules/@types", "./src/types"]}, "include": ["project_code/**/*"], "exclude": ["node_modules"]}