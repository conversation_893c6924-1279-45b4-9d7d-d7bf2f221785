/*this is generated code */
import  { AxiosResponse } from 'axios'
declare global{
  namespace API {
    /** mt-dm-order-comment
      * 文档链接：http://op.pagoda.com.cn/?#/technical-doc
      * {"description":"order comment rest api","version":"1.0-SNAPSHOT","title":"1.0-SNAPSHOT"}
      * dm-order-comment.kt3.pagoda.com.cn
    */
    namespace mt_dm_order_comment {

        type AccusationAdd = {

            accusation?:   string

            accusationObjectId?:   string

            content?:   string

            creatorCode?:   string

            creatorInfo?:   any

            index?:   string

        }

        type AccusationSearch = {

            accusationObjectId?:   string

            creatorCode?:   string

            index?:   string

        }

        type ApiResult = {

            data?:   any

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultBatchResult = {

            data?:  BatchResult

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultDataMenuRes = {

            data?:  DataMenuRes

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultEsPageResult = {

            data?:  EsPageResult

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultExportRes = {

            data?:  ExportRes

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListDataMenu = {

            data?:    Array < DataMenu >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListMapstringobject = {

            data?:    Array < Mapstringobject >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultListstring = {

            data?:    Array < string >

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultLockOperatorRes = {

            data?:  LockOperatorRes

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultMapstringobject = {

            data?:   any

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultMap = {

            data?:   any

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultOrderCommentBatchCreateRes = {

            data?:  OrderCommentBatchCreateRes

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultOrderCommentExportQueryRes = {

            data?:  OrderCommentExportQueryRes

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultOrderCommentRes = {

            data?:  OrderCommentRes

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultboolean = {

            data?:   boolean

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultint = {

            data?:   number

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultlong = {

            data?:   number

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultobject = {

            data?:   any

            errorMsg?:   string

            resultCode:   number

        }

        type ApiResultstring = {

            data?:   string

            errorMsg?:   string

            resultCode:   number

        }

        type BatchResult = {

            successSize?:   number

        }

        type BoolQueryBuilder = {

            fragment?:   boolean

            name?:   string

            writeableName?:   string

        }

        type CheckComplaintStatusReq = {

            itemsIdList?:    Array < string >

            orderCommentId?:   string

        }

        type CheckTimeOutNoAppealAuditStatusReq = {

            itemId?:   string

            orderCommentId?:   string

        }

        type CommentReq = {

            commentContent?:   string

            commentSource?:   number

            commentTime?:   string

            commentUser?:   string

            itemCode?:   string

            orderCommentId?:   string

        }

        type Comments = {

            commentContent?:   string

            commentPic?:    Array < string >

            commentSource?:   number

            commentTime?:   string

            commentUser?:   string

            commentVideo?:    Array < string >

            id?:   string

            refScore?:    Array < string >

            tag?:   string

            type?:   string

        }

        type ConditionContainer = {

            boolBuilder?:  BoolQueryBuilder

            conditions?:    Array < NestCondition >

            logical?:   string

        }

        type DataDictionaryAddReq = {

            dutyDepartmentCode?:   string

            dutyDepartmentName?:   string

            menuNodeId?:   string

            menuNodeName?:   string

            operatorId?:   string

            operatorName?:   string

            remarks?:   string

        }

        type DataDictionaryQueryReq = {

            from?:   number

            inValid?:   boolean

            index?:   string

            menuNodeId?:   string

            orderBy?:    Array < OrderSortPair >

            pageNumber?:   number

            pageSize?:   number

            searchName?:   string

        }

        type DataDictionaryUpdateReq = {

            dutyDepartmentCode?:   string

            dutyDepartmentName?:   string

            id?:   string

            menuNodeName?:   string

            operatorId?:   string

            operatorName?:   string

            parentId?:   string

            remarks?:   string

        }

        type DataMenu = {

            createTime?:   string

            id?:   string

            lastUpdateTime?:   string

            name?:   string

            nodePath?:   string

            parentId?:   string

        }

        type DataMenuAddReq = {

            name?:   string

        }

        type DataMenuRes = {

            nodeList?:    Array < DataMenu >

        }

        type EsPageResult = {

            content?:    Array < Mapstringobject >

            pageNumber?:   number

            pageSize?:   number

            totalElements?:   number

            totalPages?:   number

        }

        type EsQueryReq = {

            from?:   number

            inValid?:   boolean

            index?:   string

            orderBy?:    Array < OrderSortPair >

            pageNumber?:   number

            pageSize?:   number

            query?:  ConditionContainer

        }

        type ExportRes = {

            excelId?:   string

        }

        type File = {

            absolute?:   boolean

            absoluteFile?:  File

            absolutePath?:   string

            canonicalFile?:  File

            canonicalPath?:   string

            directory?:   boolean

            executable?:   boolean

            file?:   boolean

            freeSpace?:   number

            hidden?:   boolean

            lastModified?:   number

            name?:   string

            parent?:   string

            parentFile?:  File

            path?:   string

            readable?:   boolean

            totalSpace?:   number

            usableSpace?:   number

            writable?:   boolean

        }

        type GoodsAccuseReq = {

            complaintLevel?:   string

            complaintResult?:   string

            complaintType?:   string

            dutyDepartmentCode?:   string

            dutyDepartmentName?:   string

            effective?:   number

            goodsCommentId?:   string

            operator?:   string

            orderCommentId?:   string

            refundAmount?:   string

            salesMaintainWay?:   string

        }

        type InputStream = {

        }

        type Items = {

            appealExamineTime?:   string

            appealReason?:   string

            appealRemark?:   string

            appealResource?:    Array < string >

            appealResult?:   number

            appealStatus?:   number

            appealTime?:   string

            commentContent?:   string

            commentPic?:    Array < string >

            commentTime?:   string

            complaintLevel?:   string

            complaintResult?:   string

            complaintStatus?:   string

            complaintType?:   string

            customerServiceScore?:   number

            dutyDepartmentCode?:   string

            dutyDepartmentName?:   string

            effective?:   number

            goodsAmount?:   string

            id?:   string

            itemCode?:   string

            lastDutyDepartmentCode?:   string

            lastDutyDepartmentName?:   string

            name?:   string

            operator?:   string

            orderCommentId?:   string

            refundAmount?:   string

            refundedAmount?:   string

            salesMaintainWay?:   string

            score?:   number

            skuCode?:   string

        }

        type LockOperatorReq = {

            expireTime?:   number

            operatorId?:   string

            orderCommentId?:   string

        }

        type LockOperatorRes = {

            operatorId?:   string

        }

        type MapstringListstring = {

        }

        type Mapstringobject = {

        }

        type Mapstringstring = {

        }

        type NestCondition = {

        }

        type OptionalListstring = {

            present?:   boolean

        }

        type OptionalMapstringListstring = {

            present?:   boolean

        }

        type OptionalMapstringstring = {

            present?:   boolean

        }

        type Optionalstring = {

            present?:   boolean

        }

        type OrderAccuseReq = {

            channel?:   string

            complaintCategory?:   string

            complaintChannel?:   string

            mainComplaintType?:   string

            mainDutyDepartmentCode?:   string

            mainDutyDepartmentName?:   string

            orderCommentId?:   string

        }

        type OrderComment = {

            anonymousRating?:   number

            areaCode?:   string

            areaName?:   string

            channel?:   string

            cityName?:   string

            commentTime?:   string

            comments?:    Array < Comments >

            complaintCategory?:   string

            complaintChannel?:   string

            complaintStatus?:   string

            complaintType?:   string

            createTime?:   string

            customerPhone?:   string

            customerServiceScore?:   number

            deliveryScore?:   number

            dispatchChannel?:   string

            dutyDepartmentCode?:   string

            dutyDepartmentName?:   string

            externalCommentId?:   string

            externalOrderNo?:   string

            hashCode?:   string

            id?:   string

            isReply?:   number

            isReview?:   number

            items?:    Array < Items >

            lastUpdateTime?:   string

            operator?:   string

            orderCreatedTime?:   string

            orderNo?:   string

            orderReceiveTime?:   string

            orderRemark?:   string

            orderType?:   string

            orgCode?:   string

            orgName?:   string

            packingScore?:   number

            paymentAmount?:   number

            qualityScore?:   number

            receiverPhone?:   string

            reviewHashCode?:   string

            score?:   number

            storeScore?:   number

            subAreaCode?:   string

            subAreaName?:   string

            tag?:    Array < string >

            warehouseCode?:   string

            warehouseName?:   string

        }

        type OrderCommentAdd = {

            accusation?:    Array < string >

            commentContent?:   string

            commentObjectId?:   string

            createTime?:   string

            creatorCode?:   string

            creatorInfo?:   any

            expandInfo?:   any

            goodsId?:   string

            goodsName?:   string

            grade?:   number

            id?:   string

            images?:    Array < string >

            index?:   string

            replyId?:   string

            replyType?:   string

            status?:   number

            tag?:    Array < string >

            thumbNum?:   number

            toUserCode?:   string

            updateCode?:   string

            updateTime?:   string

        }

        type OrderCommentAppealExamineReq = {

            appealRemark?:   string

            appealResult?:   number

            complaintType?:   string

            dutyDepartmentCode?:   string

            dutyDepartmentName?:   string

            effective?:   number

            itemCommentId?:   string

            mainComplaintType?:   string

            mainDutyDepartmentCode?:   string

            mainDutyDepartmentName?:   string

            orderCommentId?:   string

        }

        type OrderCommentAppealReq = {

            appealReason?:   string

            appealResource?:    Array < string >

            itemCommentId?:   string

            orderCommentId?:   string

        }

        type OrderCommentBatchCreateRes = {

            fail?:    Array < OrderCommentCreateRes >

            success?:    Array < OrderCommentCreateRes >

        }

        type OrderCommentCreateRes = {

            commentId?:   string

            orderNo?:   string

        }

        type OrderCommentDetailsReq = {

            orderCommentId?:   string

        }

        type OrderCommentExportQueryReq = {

            excelId?:   string

        }

        type OrderCommentExportQueryRes = {

            url?:   string

        }

        type OrderCommentExportReq = {

            from?:   number

            inValid?:   boolean

            index?:   string

            orderBy?:    Array < OrderSortPair >

            pageNumber?:   number

            pageSize?:   number

            query?:  ConditionContainer

        }

        type OrderCommentReq = {

            anonymousRating?:   number

            areaCode?:   string

            areaName?:   string

            channel?:   string

            cityName?:   string

            commentTime?:   string

            comments?:    Array < Comments >

            complaintCategory?:   string

            complaintChannel?:   string

            complaintStatus?:   string

            complaintType?:   string

            createTime?:   string

            customerPhone?:   string

            customerServiceScore?:   number

            deliveryScore?:   number

            dispatchChannel?:   string

            dutyDepartmentCode?:   string

            dutyDepartmentName?:   string

            externalCommentId?:   string

            externalOrderNo?:   string

            hashCode?:   string

            id?:   string

            isReply?:   number

            isReview?:   number

            items?:    Array < Items >

            lastUpdateTime?:   string

            operator?:   string

            orderCreatedTime?:   string

            orderNo?:   string

            orderReceiveTime?:   string

            orderRemark?:   string

            orderType?:   string

            orgCode?:   string

            orgName?:   string

            packingScore?:   number

            paymentAmount?:   number

            qualityScore?:   number

            receiverPhone?:   string

            reviewHashCode?:   string

            score?:   number

            storeScore?:   number

            subAreaCode?:   string

            subAreaName?:   string

            tag?:    Array < string >

            warehouseCode?:   string

            warehouseName?:   string

        }

        type OrderCommentRes = {

            commentId?:   string

        }

        type OrderCommentSearch = {

            commentObjectId?:   string

            extension?:   any

            from?:   number

            goodsId?:   string

            id?:   string

            inValid?:   boolean

            index?:   string

            orderBy?:    Array < OrderSortPair >

            pageNumber?:   number

            pageSize?:   number

            replyId?:   string

            replyType?:   string

            status?:   number

            tag?:    Array < string >

            validCommentObjectId?:  Optionalstring

            validExtension?:  OptionalMapstringstring

            validGoodsId?:  Optionalstring

            validId?:  Optionalstring

            validReplyId?:  Optionalstring

            validReplyType?:  Optionalstring

            validTag?:  OptionalListstring

        }

        type OrderCommentThirdReq = {

            anonymousRating?:   number

            channel?:   string

            commentTime?:   string

            comments?:    Array < Comments >

            customerServiceScore?:   number

            deliveryScore?:   number

            externalCommentId?:   string

            externalOrderNo?:   string

            isUpdate?:   number

            items?:    Array < Items >

            orderNo?:   string

            orgCode?:   string

            orgName?:   string

            packingScore?:   number

            qualityScore?:   number

            score?:   number

            storeScore?:   number

        }

        type OrderCommentUpdate = {

            id?:   string

            index?:   string

            status?:   number

            updateCode?:   string

        }

        type OrderCommentUpdateReq = {

            channel?:   string

            complaintCategory?:   string

            complaintChannel?:   string

            complaintStatus?:   string

            complaintType?:   string

            dutyDepartmentCode?:   string

            dutyDepartmentName?:   string

            id?:   string

            items?:    Array < Items >

            operator?:   string

            processedStatus?:   boolean

        }

        type OrderReportAdd = {

            accusation?:    Array < string >

            activityCode?:   string

            activityName?:   string

            allowComments?:   number

            content?:   string

            createTime?:   string

            creatorCode?:   string

            creatorInfo?:   any

            extendInfo?:   any

            goodsId?:   string

            goodsName?:   string

            grade?:   any

            id?:   string

            images?:    Array < string >

            index?:   string

            name?:   string

            reportObjectId?:   string

            status?:   number

            tag?:    Array < string >

            thumbNum?:   number

            updateCode?:   string

            updateTime?:   string

        }

        type OrderReportBatchAdd = {

            index?:   string

            orderReports?:    Array < OrderReportAdd >

        }

        type OrderReportBatchUpdate = {

            index?:   string

            orderReports?:    Array < OrderReportUpdate >

        }

        type OrderReportSearch = {

            activityCode?:   string

            exclusion?:   any

            extension?:   any

            from?:   number

            goodsId?:   string

            id?:   string

            inValid?:   boolean

            index?:   string

            orderBy?:    Array < OrderSortPair >

            pageNumber?:   number

            pageSize?:   number

            reportObjectId?:   string

            status?:   number

            tag?:    Array < string >

            thumbNumber?:   boolean

            validActivityCode?:  Optionalstring

            validExclusionMap?:  OptionalMapstringListstring

            validExtension?:  OptionalMapstringstring

            validGoodsId?:  Optionalstring

            validId?:  Optionalstring

            validReportObjectId?:  Optionalstring

            validTag?:  OptionalListstring

        }

        type OrderReportUpdate = {

            accusation?:    Array < string >

            activityCode?:   string

            activityName?:   string

            allowComments?:   number

            content?:   string

            createTime?:   string

            creatorCode?:   string

            creatorInfo?:   any

            extendInfo?:   any

            goodsId?:   string

            goodsName?:   string

            grade?:   any

            id?:   string

            images?:    Array < string >

            index?:   string

            name?:   string

            reportObjectId?:   string

            status?:   number

            tag?:    Array < string >

            thumbNum?:   number

            updateCode?:   string

            updateTime?:   string

        }

        type OrderSortPair = {

            field?:   string

            sort?:   string

        }

        type PartnerGoodsCreateReq = {

            commentContent?:   string

            commentPic?:    Array < string >

            complaintLevel?:   string

            complaintResult?:   string

            complaintStatus?:   string

            complaintType?:   string

            customerServiceScore?:   number

            dutyDepartmentCode?:   string

            dutyDepartmentName?:   string

            effective?:   number

            goodsAmount?:   string

            id?:   string

            itemCode?:   string

            name?:   string

            refundAmount?:   string

            refundedAmount?:   string

            salesMaintainWay?:   string

            score?:   number

        }

        type ReplyTemplateReq = {

            complaintType?:   string

            content?:   string

            createTime?:   string

            goodsName?:   string

            hashCode?:   string

            id?:   string

            keywords?:    Array < string >

            lastUpdateTime?:   string

            replyType?:   string

        }

        type Resource = {

            description?:   string

            file?:  File

            filename?:   string

            inputStream?:  InputStream

            open?:   boolean

            readable?:   boolean

            uri?:  URI

            url?:  URL

        }

        type ReviewCommentReq = {

            commentContent?:   string

            commentId?:   string

            commentTime?:   string

            commentUser?:   string

            externalCommentId?:   string

            hashCode?:   string

        }

        type ThumbAddAndRemove = {

            creatorCode?:   string

            creatorInfo?:   any

            index?:   string

            removeThumbObjectIds?:    Array < string >

            thumbObjectIds?:    Array < string >

        }

        type ThumbNumber = {

            index?:   string

            thumbObjectId?:   string

        }

        type TurnBadCommentReq = {

            itemCommentId?:   string

            orderCommentId?:   string

        }

        type URI = {

            absolute?:   boolean

            authority?:   string

            fragment?:   string

            host?:   string

            opaque?:   boolean

            path?:   string

            port?:   number

            query?:   string

            rawAuthority?:   string

            rawFragment?:   string

            rawPath?:   string

            rawQuery?:   string

            rawSchemeSpecificPart?:   string

            rawUserInfo?:   string

            scheme?:   string

            schemeSpecificPart?:   string

            userInfo?:   string

        }

        type URL = {

            authority?:   string

            content?:   any

            defaultPort?:   number

            deserializedFields?:  URLStreamHandler

            file?:   string

            host?:   string

            path?:   string

            port?:   number

            protocol?:   string

            query?:   string

            ref?:   string

            serializedHashCode?:   number

            userInfo?:   string

        }

        type URLStreamHandler = {

        }

        type WhetherThumb = {

            index?:   string

            thumbObjectId?:   string

            userCode?:   string

        }



      /**description
       * order comment rest api
       */

        /**
        * 新增举报
        * @method
        * @name 新增举报
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1AccusationAdd (

            parameters : {
              'body'  : AccusationAdd,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询用户是否举报
        * @method
        * @name 查询用户是否举报
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1AccusationQueryUserAccusation (

            parameters : {
              'body'  : AccusationSearch,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 更新 评论/报告 举报类型 (提供mq-consumer模块调用)
        * @method
        * @name 更新 评论/报告 举报类型 (提供mq-consumer模块调用)
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1AccusationUpdateCommentAccusation (

            parameters : {
              'body'  : AccusationAdd,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 新增评论
        * @method
        * @name 新增评论
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1CommentAdd (

            parameters : {
              'body'  : OrderCommentAdd,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * appeal
        * @method
        * @name appeal
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1CommentAppeal (

            parameters : {
              'body'  : OrderCommentAppealReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * appealExamine
        * @method
        * @name appealExamine
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1CommentAppealExamine (

            parameters : {
              'body'  : OrderCommentAppealExamineReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * orderCommentDetails
        * @method
        * @name orderCommentDetails
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1CommentDetails (

            parameters : {
              'body'  : OrderCommentDetailsReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * lockOperator
        * @method
        * @name lockOperator
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1CommentLockOperator (

            parameters : {
              'body'  : LockOperatorReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * replyCommentBatch
        * @method
        * @name replyCommentBatch
        * @param  body - reqs * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1CommentReplyUpdateBatch (

            parameters : {
              'body'  : Array<CommentReq>,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * APP查询报告评论列表
        * @method
        * @name APP查询报告评论列表
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1CommentSelectPage (

            parameters : {
              'body'  : OrderCommentSearch,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * turnBadComment
        * @method
        * @name turnBadComment
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1CommentTurnBad (

            parameters : {
              'body'  : TurnBadCommentReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 更新评论
        * @method
        * @name 更新评论
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1CommentUpdate (

            parameters : {
              'body'  : OrderCommentUpdate,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryEnum
        * @method
        * @name queryEnum
        * @param string key - key * @param  x-pagoda-envoy-route-project -
        */
        function getOrderCommentV1CommonQueryEnum (

            parameters : {
              'key'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 管理台通用分页多条件查询
        * @method
        * @name 管理台通用分页多条件查询
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1CommonSelectPage (

            parameters : {
              'body'  : EsQueryReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * addDataMenu
        * @method
        * @name addDataMenu
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1DataMenuAdd (

            parameters : {
              'body'  : DataMenuAddReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryChildren
        * @method
        * @name queryChildren
        * @param string name - name * @param  x-pagoda-envoy-route-project -
        */
        function getOrderCommentV1DataMenuQueryChildren (

            parameters : {
              'name'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryDataMenuList
        * @method
        * @name queryDataMenuList
        * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1DataMenuSearch (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * addDataDictionary
        * @method
        * @name addDataDictionary
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1DictionaryAdd (

            parameters : {
              'body'  : Array<DataDictionaryAddReq>,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryDataDictionary
        * @method
        * @name queryDataDictionary
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1DictionaryQuery (

            parameters : {
              'body'  : DataDictionaryQueryReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * updateDataDictionary
        * @method
        * @name updateDataDictionary
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1DictionaryUpdate (

            parameters : {
              'body'  : DataDictionaryUpdateReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * goodsAccuse
        * @method
        * @name goodsAccuse
        * @param  body - reqs * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1OrderCommentAccuseGoods (

            parameters : {
              'body'  : Array<GoodsAccuseReq>,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * orderAccuse
        * @method
        * @name orderAccuse
        * @param  body - reqs * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1OrderCommentAccuseOrder (

            parameters : {
              'body'  : Array<OrderAccuseReq>,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量新增订单评论
        * @method
        * @name 批量新增订单评论
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1OrderCommentAddBatch (

            parameters : {
              'body'  : Array<OrderComment>,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * checkComplaintStatus
        * @method
        * @name checkComplaintStatus
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1OrderCommentCheckStatus (

            parameters : {
              'body'  : CheckComplaintStatusReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * checkTimeOutNoAppealAudit
        * @method
        * @name checkTimeOutNoAppealAudit
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1OrderCommentCheckTimeOutNoAppealAuditStatus (

            parameters : {
              'body'  : CheckTimeOutNoAppealAuditStatusReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * exportOrderComment
        * @method
        * @name exportOrderComment
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1OrderCommentExport (

            parameters : {
              'body'  : OrderCommentExportReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * exportQuery
        * @method
        * @name exportQuery
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1OrderCommentExportQuery (

            parameters : {
              'body'  : OrderCommentExportQueryReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * importOtherOrderComment
        * @method
        * @name importOtherOrderComment
        * @param file file - file * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1OrderCommentImport (

            parameters : {
              'file'  : any,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * importMtOrderComment
        * @method
        * @name importMtOrderComment
        * @param file file - file * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1OrderCommentImportMeituan (

            parameters : {
              'file'  : any,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * addPagodaOrderComment
        * @method
        * @name addPagodaOrderComment
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1OrderCommentPagodaAdd (

            parameters : {
              'body'  : OrderCommentReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * createPartnerGoods
        * @method
        * @name createPartnerGoods
        * @param  body - reqs * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1OrderCommentPartnerGoodsCreate (

            parameters : {
              'body'  : Array<PartnerGoodsCreateReq>,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * importReplyTemplate
        * @method
        * @name importReplyTemplate
        * @param file file - file * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1OrderCommentReplyCommentImport (

            parameters : {
              'file'  ?: any,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryOrderComment
        * @method
        * @name queryOrderComment
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1OrderCommentSearch (

            parameters : {
              'body'  : EsQueryReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * addThirdOrderComment
        * @method
        * @name addThirdOrderComment
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1OrderCommentThirdAdd (

            parameters : {
              'body'  : Array<OrderCommentThirdReq>,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * reviewThirdOrderComment
        * @method
        * @name reviewThirdOrderComment
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1OrderCommentThirdReview (

            parameters : {
              'body'  : Array<ReviewCommentReq>,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * updateOrderComment
        * @method
        * @name updateOrderComment
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1OrderCommentUpdate (

            parameters : {
              'body'  : OrderCommentUpdateReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量修改订单评论
        * @method
        * @name 批量修改订单评论
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1OrderCommentUpdateBatch (

            parameters : {
              'body'  : Array<OrderComment>,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * addReplyTemplate
        * @method
        * @name addReplyTemplate
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1ReplyTemplateAdd (

            parameters : {
              'body'  : ReplyTemplateReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * deleteReplyTemplate
        * @method
        * @name deleteReplyTemplate
        * @param  body - reqs * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1ReplyTemplateDelete (

            parameters : {
              'body'  : Array<string>,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * queryReplyTemplate
        * @method
        * @name queryReplyTemplate
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1ReplyTemplateSearch (

            parameters : {
              'body'  : EsQueryReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * updateReplyTemplate
        * @method
        * @name updateReplyTemplate
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1ReplyTemplateUpdate (

            parameters : {
              'body'  : ReplyTemplateReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 新增报告
        * @method
        * @name 新增报告
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1ReportAdd (

            parameters : {
              'body'  : OrderReportAdd,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量新增报告
        * @method
        * @name 批量新增报告
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1ReportBatchAdd (

            parameters : {
              'body'  : OrderReportBatchAdd,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量更新报告
        * @method
        * @name 批量更新报告
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1ReportBatchUpdate (

            parameters : {
              'body'  : OrderReportBatchUpdate,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量更新报告,并推送mq逻辑(目前只有设为精选)
        * @method
        * @name 批量更新报告,并推送mq逻辑(目前只有设为精选)
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1ReportBatchUpdateReportChange (

            parameters : {
              'body'  : OrderReportBatchUpdate,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 异步导出报告列表
        * @method
        * @name 异步导出报告列表
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1ReportExportOrderReport (

            parameters : {
              'body'  : EsQueryReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * APP查询报告列表
        * @method
        * @name APP查询报告列表
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1ReportSelectPage (

            parameters : {
              'body'  : OrderReportSearch,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 更新报告
        * @method
        * @name 更新报告
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1ReportUpdate (

            parameters : {
              'body'  : OrderReportUpdate,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 点赞&取消点赞
        * @method
        * @name 点赞&取消点赞
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1ThumbAddAndRemove (

            parameters : {
              'body'  : ThumbAddAndRemove,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 某个报告或者评论点赞次数
        * @method
        * @name 某个报告或者评论点赞次数
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1ThumbNumber (

            parameters : {
              'body'  : ThumbNumber,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 更新点赞数量到ES(定时任务)
        * @method
        * @name 更新点赞数量到ES(定时任务)
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1ThumbUpdateNumber (

            parameters : {
              'body'  : ThumbNumber,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 判断用户是否对某个评论点赞过
        * @method
        * @name 判断用户是否对某个评论点赞过
        * @param  body - req * @param  x-pagoda-envoy-route-project -
        */
        function postOrderCommentV1ThumbWhetherThumb (

            parameters : {
              'body'  : WhetherThumb,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

      }
  }
}
export {}

