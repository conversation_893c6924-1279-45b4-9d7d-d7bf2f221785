import { Request, Response, NextFunction } from 'express'
import message from '../util/message'
import { IResponseError } from '../interfaces/IResponseError.interface'
import Log, { getAppProjectName } from '../util/Log'

//TODO IResponseError
export default function genericErrorHandler(error: IResponseError, req: Request, res: Response, next: NextFunction) {
  const errorCode = error.errorCode || 500
  const errorMsg = error.error?.['message'] ?? error.error
  const errorInfo = {
    errCode: errorCode,
    errorCode,
    message: errorMsg,
    crid: res?.locals?.crid || '',
    app_project_name: getAppProjectName(req.originalUrl),
    trackId: req?.query?.trackId ?? req?.body?.trackId,
    originalUrl: req?.originalUrl || '',
    stack: error.error?.['stack']
  } as any

  message.fail(res, errorInfo)
  if (!error.error?.['hasLogged']) {
    Log.error({ ...errorInfo, stack: error.error?.['stack'] })
  }
}
