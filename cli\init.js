/**
 * 第一步：创建 project_code 文件夹
 * 第二步：hack tsc
 *  2 替换内容 到 tsc_hacked.js
    2.1 替换 mappings: mappings, -> mappings: ";" + mappings,
    2.2 替换 writer.writeComment("//# " + "sourceMappingURL" + "=" + sourceMappingURL);
        为 writer.writeComment("}//# " + "sourceMappingURL" + "=" + sourceMappingURL);
    2.3 替换 ts.writeFile(host, emitterDiagnostics, jsFilePath, writer.getText(), !!compilerOptions.emitBOM, sourceFiles);
        为 ts.writeFile(host, emitterDiagnostics, jsFilePath, `var __serverless_require=require;require=function(url){  if(url.indexOf('.') === 0){__serverless_require(url).__serverless_runtime_export_function(m_req,m_res,m_API,m_RUNTIME_ENV,m_xlog,m_redisCache,m_next,m_getAPI,m_middlewareCallback);return __serverless_require(url)}else{    return __serverless_require(url)}};var m_req; var m_res; var m_API; var m_RUNTIME_ENV; var m_xlog; var m_redisCache; var m_next; var m_getAPI; var m_middlewareCallback;exports.__serverless_runtime_export_function=function(req,res,API,RUNTIME_ENV,xlog,redisCache,next,getAPI,middlewareCallback){m_req = req; m_res = res; m_API = API; m_RUNTIME_ENV = RUNTIME_ENV; m_xlog = xlog; m_redisCache = redisCache; m_next = next; m_getAPI = getAPI; m_middlewareCallback = middlewareCallback;
    `+writer.getText(), !!compilerOptions.emitBOM, sourceFiles);
*/


const { readFileSync, writeFileSync, ensureFileSync, ensureDirSync } = require("fs-extra");
ensureDirSync("project_code")

let text = readFileSync("./node_modules/typescript/lib/tsc.js", "utf-8")
text = text.replace('mappings: mappings,', 'mappings: ";" + mappings,')
text = text.replace('writer.writeComment("//# "', 'writer.writeComment("}//# "')
const wrapper = "var __serverless_require=require;require=function(url){  if(url.indexOf('.') === 0){__serverless_require(url).__serverless_runtime_export_function(m_req,m_res,m_API,m_RUNTIME_ENV,m_xlog,m_redisCache,m_next,m_getAPI,m_middlewareCallback);return __serverless_require(url)}else{    return __serverless_require(url)}};var m_req; var m_res; var m_API; var m_RUNTIME_ENV; var m_xlog; var m_redisCache; var m_next; var m_getAPI; var m_middlewareCallback;exports.__serverless_runtime_export_function=function(req,res,API,RUNTIME_ENV,xlog,redisCache,next,getAPI,middlewareCallback){m_req = req; m_res = res; m_API = API; m_RUNTIME_ENV = RUNTIME_ENV; m_xlog = xlog; m_redisCache = redisCache; m_next = next; m_getAPI = getAPI; m_middlewareCallback = middlewareCallback;"
text = text.replace('ts.writeFile(host, emitterDiagnostics, jsFilePath, writer.getText()', 'ts.writeFile(host, emitterDiagnostics, jsFilePath, "' + wrapper + '\\n"+writer.getText()')
writeFileSync("./node_modules/typescript/lib/tsc-hacked.js", text)