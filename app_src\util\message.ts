import { Response } from 'express'
import * as HttpStatus from 'http-status-codes'

export default {
  success: (res: Response, { message = '', errorCode = 0, ...data }) =>
    res.status(HttpStatus.OK).json({
      success: true,
      msg: message,
      errCode: errorCode,
      ...data
    }),
  fail: (res: Response, { errCode = 500, message = '查询失败' }) =>
    res.status(errCode).json({
      success: false,
      errCode,
      msg: message
    })
}
