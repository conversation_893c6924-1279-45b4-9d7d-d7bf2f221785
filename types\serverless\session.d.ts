/// <reference types="express-session" />



import express = require('express');

declare global {
    namespace Express {
        interface Request {
            [attr:string]:any
        }

        interface SessionData {
            userInfo: OldTypeUserInfo | NewTypeUserInfo
        }
    }
}
interface OldTypeUserInfo {
  user: string
  attributes: {
    employeeName: string[]
    isFromNewLogin: boolean[]
    authenticationDate: number[]
    successfulAuthenticationHandlers: string[]
    telephone: string[]
    userCode: string[]
    employeeCode: [string]
    credentialType: string[]
    authenticationMethod: string[]
    orgCode: string[]
    longTermAuthenticationRequestTokenUsed: boolean[]
    authType: string[]
    oaid: [string]
  }
}

interface NewTypeUserInfo {
  user: string
  attributes: {
    birthday: string[]
    postname: string[]
    unitdeptname: string[]
    code: string[]
    entrytime: any[]
    modifytime: string[]
    deptid: string[]
    postid: string[]
    userCode: string[]
    employeeCode: string[]
    unitname: string[]
    zbmdbs: string[]
    longTermAuthenticationRequestTokenUsed: boolean[]
    hrzgzdlist: any[]
    authType: string[]
    oaid: [string]
    employeeName: [string]
    dyrybh: any[]
    isFromNewLogin: boolean[]
    workcode: any[]
    authenticationDate: number[]
    sfzz: any[]
    successfulAuthenticationHandlers: string[]
    postcode: string[]
    mobile: string[]
    telephone: string[]
    dept: string[]
    canonicalcode: string[]
    deptname: string[]
    credentialType: string[]
    unit: string[]
    authenticationMethod: string[]
    idcard: string[]
    name: string[]
    unitid: string[]
    status: string[]
  }
}

