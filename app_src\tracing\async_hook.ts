import * as asyncHooks from 'async_hooks'
import console_hack from './console.hack'
import request_hack from './request.hack'
const { executionAsyncId } = asyncHooks

// 保存异步调用的上下文。
/**@type {any} */
export const contexts = {}

const hooks = asyncHooks.createHook({
  // 对象构造时会触发 init 事件。
  init: function(asyncId, type, triggerId, resource) {
    // triggerId 即为当前函数的调用者的 asyncId 。
    if (contexts[triggerId]) {
      // 设置当前函数的异步上下文与调用者的异步上下文一致。
      contexts[asyncId] = contexts[triggerId]
    }
  },
  // 在销毁对象后会触发 destroy 事件。
  destroy: function(asyncId) {
    if (!contexts[asyncId]) return
    // 销毁当前异步上下文。
    delete contexts[asyncId]
  }
})

// 关键！允许该实例中对异步函数启用 hooks 。
// hooks.enable()

export default function hackhook() {
  hooks.enable()
  console_hack()
  request_hack()
}

export function tracingMiddleware(req, res, next) {
  var context: any = {}
  var tack_headers = [
    'x-correlation-id',
    'x-request-id',
    'x-ot-span-context',
    'x-datadog-trace-id',
    'x-datadog-parent-id',
    'x-datadog-sampling-priority',
    'traceparent',
    'tracestate',
    'x-cloud-trace-context',
    'grpc-trace-bin',
    'x-b3-traceid',
    'x-b3-spanid',
    'x-b3-parentspanid',
    'x-b3-sampled',
    'x-b3-flags',
    'end-user',
    'user-agent'
  ]

  //xlog 的crid
  context = { ...res.locals.logtubeHeaders }

  // 链路追踪的日志平台
  for (const key in req.headers) {
    if (tack_headers.includes(key.toLowerCase())) {
      context[key] = req.headers[key]
    }
  }
  context.originalUrl = req.originalUrl
  contexts[executionAsyncId()] = context
  next()
}
