import * as logtube from 'logtube'

logtube.setup({
  project: process.env.XLOG_PROJECT_NAME || 'serverless_runtime', // 设置项目名，只允许数字，- 和 _
  env: process.env.XLOG_ENV! || 'local', // 设置项目环境名，一般为 dev, test, uat/staging, prod
  console: {
    // 命令行输出
    topics: [] // 设置要通过命令行输出的主题，"*" 代表全部主题
  },
  file: {
    // 日志文件输出
    topics: process.env.XLOG_FILE_DISABLE?[]:['*'], // 设置要通过日志文件输出的主题, "*" 代表全部主题
    dir: 'logs', // 设置日志输出的跟目录
    subdirs: {
      important: ['*'] // 可以额外指定某些主题输出到某个子目录中，这一行示例代表 err 和 warn 主题输出到 logs 目录中的 important 子目录下
    }
  }
})

export default logtube
