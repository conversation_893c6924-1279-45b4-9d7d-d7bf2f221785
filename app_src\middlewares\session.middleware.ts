import { Express, Request } from 'express'
import session from '../hacked_lib/express-session'
import Redis from 'ioredis'
import config from '../config'
import { REDIS_EXPIRE } from '../util/constants'
import connect_redis from 'connect-redis'
import Log from '../util/Log'
import { CookieOptions } from 'express-serve-static-core'

//@ts-ignore
const RedisStore = connect_redis(session)
const client = new Redis(config.redis)
const redisStore = new RedisStore({ client })

client.on('error', function(...args) {
  Log.error(...args)
})

const sessionMiddle = (app: Express) => {
  // if (config.env === 'production') {
  app.set('trust proxy', 1)
  // }

  return session({
    //@ts-ignore
    name: function(req: Request) {
      if (req.secure) {
        return 'https_' + config.redis.keyPrefix
      } else {
        return 'http_' + config.redis.keyPrefix
      }
    },
    secret: 'serverless_runtime_secret',
    resave: false,
    saveUninitialized: false,
    //@ts-ignore
    cookie: function(req: Request) {
      var cookie = {
        path: '/',
        httpOnly: true,
        maxAge: REDIS_EXPIRE
      } as CookieOptions
      if (req.secure) {
        cookie.secure = true
        if (!disallowsSameSiteNone(req.headers['user-agent'])) {
          cookie.sameSite = 'none'
        }
      }
      return cookie
    },

    store: redisStore
  })
}

function disallowsSameSiteNone(userAgent: string | undefined): boolean {
  if (!userAgent) {
    return false
  }
  // Cover all iOS based browsers here. This includes:
  // - Safari on iOS 12 for iPhone, iPod Touch, iPad
  // - WkWebview on iOS 12 for iPhone, iPod Touch, iPad
  // - Chrome on iOS 12 for iPhone, iPod Touch, iPad
  // All of which are broken by SameSite=None, because they use the iOS networking
  // stack.
  if (userAgent.includes('CPU iPhone OS 12') || userAgent.includes('iPad; CPU OS 12')) {
    return true
  }

  // Cover Mac OS X based browsers that use the Mac OS networking stack.
  // This includes:
  // - Safari on Mac OS X.
  // This does not include:
  // - Chrome on Mac OS X
  // Because they do not use the Mac OS networking stack.
  if (
    userAgent.includes('Macintosh; Intel Mac OS X 10_14') &&
    userAgent.includes('Version/') &&
    userAgent.includes('Safari')
  ) {
    return true
  }

  // Cover Chrome 50-69, because some versions are broken by SameSite=None,
  // and none in this range require it.
  // Note: this covers some pre-Chromium Edge versions,
  // but pre-Chromium Edge does not require SameSite=None.
  if (userAgent.includes('Chrome/5') || userAgent.includes('Chrome/6')) {
    return true
  }

  return false
}

export { sessionMiddle, redisStore, client as redisClient }
export default sessionMiddle
