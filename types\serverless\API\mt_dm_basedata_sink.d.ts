/*this is generated code */
import  { AxiosResponse } from 'axios'
declare global{
  namespace API {
    /** mt-dm-basedata-sink
      * 文档链接：http://op.pagoda.com.cn/?#/technical-doc
      * {"license":{"name":"Apache 2.0","url":"http://www.apache.org/licenses/LICENSE-2.0"},"contact":{},"description":"Api Documentation","termsOfService":"urn:tos","title":"Api Documentation","version":"1.0"}
      * **********:8001
    */
    namespace mt_dm_basedata_sink {

        type LifeCycle = {

            avgPreCustomer?:   number

            lifeCyclePeriod?:   number

            avgCustomer?:   number

        }

        type ApiResultMemberLifeCycleResp = {

            data?:  MemberLifeCycleResp

            resultCode:   number

            errorMsg?:   string

        }

        type MemberLifeCycleResp = {

            lifeCycles?:    Array < LifeCycle >

        }

        type ApiResultMemberResp = {

            data?:  MemberResp

            resultCode:   number

            errorMsg?:   string

        }

        type MemberResp = {

            counter?:   number

            storeCode?:   string

        }

        type DatePeriodModel = {

            datePeriodType?:   number

        }

        type StoreModel = {

            dateStr?:   string

            storeCode?:   string

        }

        type ApiResult = {

            data?:   any

            resultCode:   number

            errorMsg?:   string

        }

        type MemberLifeCycleReq = {

            datePeriodType?:   number

            store?:  StoreModel

            memberLifeCycleType?:   number

        }

        type MemberReq = {

            datePeriod?:  DatePeriodModel

            memberType?:   number

            store?:  StoreModel

        }

        type SupportedTypesRespint = {

            supported?:    Array < Descint >

        }

        type ApiResultSupportedTypesRespint = {

            data?:  SupportedTypesRespint

            resultCode:   number

            errorMsg?:   string

        }

        type Descint = {

            type?:   number

            desc?:   string

        }



      /**description
       * Api Documentation
       */

        /**
        * listSupportedDateRangeTypes
        * @method
        * @name listSupportedDateRangeTypes
        * @param  x-pagoda-envoy-route-project -
        */
        function getSinkWebDataUnionMemberV1ListSupportedDateRangeTypes (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listSupportedMemGrowUpStatus
        * @method
        * @name listSupportedMemGrowUpStatus
        * @param  x-pagoda-envoy-route-project -
        */
        function getSinkWebDataUnionMemberV1ListSupportedMemGrowTypes (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * obtainMember
        * @method
        * @name obtainMember
        * @param  body - memberReq * @param  x-pagoda-envoy-route-project -
        */
        function postSinkWebDataUnionMemberV1Number (

            parameters : {
              'body'  : MemberReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * serverLessHealthCheck
        * @method
        * @name serverLessHealthCheck
        * @param  x-pagoda-envoy-route-project -
        */
        function getHealthCheck (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查看会员枚举类型
        * @method
        * @name 查看会员枚举类型
        * @param  x-pagoda-envoy-route-project -
        */
        function getSinkWebDataUnionMemberV1ListSupportedMemberTypes (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * fetchMemberLifeCycleResp
        * @method
        * @name fetchMemberLifeCycleResp
        * @param  body - memberLifeCycleReq * @param  x-pagoda-envoy-route-project -
        */
        function postSinkWebDataUnionMemberV1LifeCycle (

            parameters : {
              'body'  : MemberLifeCycleReq,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

      }
  }
}
export {}

