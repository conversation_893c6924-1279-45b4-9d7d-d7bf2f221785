//@ts-nocheck
import * as asyncHooks from 'async_hooks'
const { executionAsyncId } = asyncHooks
import { contexts } from './async_hook'

var http = require('http')
var https = require('https')

type requestProtocol = 'http:' | 'https:'

const hack = <T extends typeof http.request>(
  originRequest: T,
  protocol: requestProtocol
): ((...args: unknown[]) => http.ClientRequest) => (...args): http.ClientRequest => {
  let options

  let url: string | URL
  let callback: Function

  // 第一个参数 是 url 或者 option
  if (typeof args[0] === 'string') {
    url = args[0]
  } else if (args[0] instanceof URL) {
    url = args[0]
  } else {
    options = args[0]
  }

  // 第二个参数 是 option 或 callback

  if (args[1]) {
    if (typeof args[1] === 'function') {
      callback = args[1]
    } else {
      options = args[1]
    }
  }

  if (typeof args[2] === 'function') {
    callback = args[2]
  }

  //把 context 附加上去
  var context = contexts[executionAsyncId()]
  if (context) {
    if (!options) {
      options = {
        headers: {}
      }
    }

    if (!options.headers) {
      options = {
        headers: {}
      }
    }

    options.headers = { ...context, ...options.headers }
  }

  var arr: any = []
  //@ts-ignore
  if (url) {
    arr.push(url)
  }

  if (options) {
    arr.push(options)
  }
  //@ts-ignore
  if (callback) {
    arr.push(callback)
  }
  //记录每次请求的日志
  // console.log(arr)
  // Execute request
  const request: http.ClientRequest = originRequest.apply(this, arr)

  return request
}

let hacked = false
let originHttpRequest: any = null
let originHttpsRequest: any = null
export default function request_hack() {
  if (!hacked) {
    originHttpRequest = http.request
    originHttpsRequest = https.request
    // eslint-disable-next-line
    // @ts-ignore
    // By default, ts not allow us to rewrite original methods.
    http.request = hack(http.request, 'http:')
    // eslint-disable-next-line
    // @ts-ignore
    // By default, ts not allow us to rewrite original methods.
    https.request = hack(https.request, 'https:')

    hacked = true
  }
}
