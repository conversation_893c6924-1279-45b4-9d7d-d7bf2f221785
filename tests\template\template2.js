import express from "express";
import cors from 'cors'
// console.log('/gupiao/list')
const app = express();
app.use(cors({
  origin: true,
  methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS,CONNECT,TRACE',
  preflightContinue: false,
  optionsSuccessStatus: 204,
  credentials: true,
  exposedHeaders: '*'
}))
app.use(express.static('static'))
const port = process.env.port || 4000;
app.set("trust proxy", 1);
app.listen(port, () => {
  console.log(`Example app listening on port ${port}`);
});

app.get("/coupon/fruit/v1/redeemCoupon", (req, res) => {

})
