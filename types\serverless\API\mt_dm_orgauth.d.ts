/*this is generated code */
import  { AxiosResponse } from 'axios'
declare global{
  namespace API {
    /** mt-dm-orgauth
      * 文档链接：http://op.pagoda.com.cn/?#/technical-doc
      * {"description":"组织架构权限域","version":"1.0.0","title":"1.0.0","contact":{}}
      * *********
    */
    namespace mt_dm_orgauth {

        type AddApplicationReqDto = {

            appType?:   number

            code?:   string

            description?:   string

            logoUrl?:   string

            name?:   string

            operator?:   string

            personnels?:    Array < ApplicationPersonnelDto >

            status?:   boolean

        }

        type AddAuthoritySystemReqDto = {

            applicationCodes?:    Array < string >

            code?:   string

            description?:   string

            name?:   string

            operator?:   string

            status?:   boolean

        }

        type AddDataDictionaryReqDto = {

            description?:   string

            enabled?:   boolean

            fieldType?:   number

            name?:   string

            nickname?:   string

            only?:   boolean

            operator?:   string

            orgType?:   number

            required?:   boolean

            updated?:   boolean

        }

        type AddDeptReqDto = {

            code?:   string

            dataSource?:   number

            dataSourceObj?:  DataSourceReqDto

            description?:   string

            expands?:    Array < ExpandDto >

            leader?:  Leader

            legalSubjectName?:   string

            name?:   string

            operator?:   string

            treeStruct?:  TreeStructDto

        }

        type AddOrgAssociationReqDto = {

            dataSource?:   number

            operator?:   string

            primaryCode?:   string

            secondaryCode?:   string

            type?:   number

        }

        type AddPersonnelAssociationReqDto = {

            dataSource?:   number

            isMainAssociation?:   boolean

            operator?:   string

            organizationAssociationCode?:   string

            organizationType?:   number

            personnelCode?:   string

        }

        type AddPersonnelReqDto = {

            associations?:    Array < Association >

            code?:   string

            description?:   string

            email?:   string

            expands?:    Array < ExpandDto >

            hometown?:   string

            idNo?:   string

            name?:   string

            operator?:   string

            phone?:   string

            sex?:   number

            worked?:   number

        }

        type AddPostReqDto = {

            code?:   string

            dataSource?:   number

            deptCode?:   string

            description?:   string

            expands?:    Array < ExpandDto >

            name?:   string

            operator?:   string

            treeStruct?:  TreeStructDto

        }

        type AddRoleReqDto = {

            ascode?:   string

            code?:   string

            description?:   string

            name?:   string

            operator?:   string

            status?:   boolean

        }

        type ApiResultUnionKeyRespDto = {

            data?:  UnionKeyRespDto

            errorMsg?:   string

            resultCode:   number

        }

        type ApplicationListReqDto = {

            authoritySystem?:   string

            bindAs?:   boolean

            code?:   string

            description?:   string

            name?:   string

            page?:   number

            size?:   number

            status?:   boolean

            types?:    Array < number >

        }

        type ApplicationListRespDto = {

            authoritySystem?:   string

            bindPersonnelType?:    Array < number >

            code?:   string

            createdAt?:   string

            createdOperator?:   string

            description?:   string

            logoUrl?:   string

            name?:   string

            status?:   boolean

            type?:   number

        }

        type ApplicationPersonnelDto = {

            code?:   string

            name?:   string

            type?:   number

        }

        type ApplicationPersonnelReqDto = {

            applicationCode?:   string

            permissionCode?:   string

            userCode?:   string

        }

        type ApplicationRespDto = {

            appSecret?:   string

            appType?:   number

            applications?:    Array < ApplicationRespDto >

            authoritySystem?:   string

            code?:   string

            createdAt?:   string

            createdOperator?:   string

            description?:   string

            logoUrl?:   string

            name?:   string

            personnels?:    Array < ApplicationPersonnelDto >

            status?:   boolean

            updatedAt?:   string

            updatedOperator?:   string

        }

        type Association = {

            dataSource?:   number

            deptCode?:   string

            isMainAssociation?:   boolean

            postCode?:   string

        }

        type AuthDataSourceBaseFieldRespDto = {

            apiFieldName?:   string

            pageFieldContent?:   string

            pageFieldName?:   string

            pageFieldType?:   string

        }

        type AuthDateSourceRespDto = {

            code?:   string

            enable?:   boolean

            name?:   string

            pageMaps?:    Array < AuthDataSourceBaseFieldRespDto >

        }

        type AuthoritySystemListReqDto = {

            code?:   string

            name?:   string

            page?:   number

            size?:   number

        }

        type AuthoritySystemListRespDto = {

            code?:   string

            createdAt?:   string

            createdOperator?:   string

            defaulted?:   boolean

            description?:   string

            name?:   string

            status?:   boolean

        }

        type Avatar = {

            normal?:   string

            thumb?:   string

        }

        type Condition = {

            page?:   number

            parameters?:    Array < Parameter >

            size?:   number

        }

        type DataDictionary对象 = {

            createdAt?:   string

            createdOperator?:   string

            description?:   string

            enabled?:   boolean

            fieldType?:   number

            id?:   number

            name?:   string

            nickname?:   string

            only?:   boolean

            orgType?:   number

            required?:   boolean

            tenantId?:   string

            updated?:   boolean

            updatedAt?:   string

            updatedOperator?:   string

            version?:   number

        }

        type DataSourceApiReqDto = {

            apiContent?:   string

            apiParameters?:  DataSourceParameterDto

            apiType?:   "HTTP_GET" | "HTTP_POST" | "DB_ORACLE" | "DB_MYSQL" | "CUSTOM"

            apiUrl?:   string

        }

        type DataSourceBaseFieldReqDto = {

            apiFieldName?:   string

            pageFieldContent?:   string

            pageFieldName?:   string

            pageFieldType?:   string

        }

        type DataSourceColumnReqDto = {

            description?:   string

            name?:   string

            type?:   string

        }

        type DataSourceConditionDto = {

            page?:   number

            parameters?:    Array < DataSourceKeyDto >

            size?:   number

        }

        type DataSourceDbParameterDto = {

            column?:    Array < DataSourceColumnReqDto >

            passWord?:   string

            userName?:   string

        }

        type DataSourceDetailReqDto = {

            code?:   string

        }

        type DataSourceEditReqDto = {

            apiInfo?:  DataSourceApiReqDto

            asCode?:   string

            code?:   string

            description?:   string

            filterMode?:   boolean

            name?:   string

            operator?:   string

            pageMaps?:    Array < DataSourceBaseFieldReqDto >

            pagination?:   boolean

            tags?:   string

        }

        type DataSourceInsertReqDto = {

            apiInfo?:  DataSourceApiReqDto

            asCode?:   string

            code?:   string

            dataType?:   string

            description?:   string

            filterMode?:   boolean

            name?:   string

            operator?:   string

            pageMaps?:    Array < DataSourceBaseFieldReqDto >

            pagination?:   boolean

            tags?:   string

        }

        type DataSourceKeyDto = {

            key?:   string

            value?:   string

        }

        type DataSourceKeyRespDto = {

            field?:   string

            value?:   any

        }

        type DataSourceListReqDto = {

            ASCode?:   string

            apiType?:   string

            dataType?:   string

            enable?:   boolean

            name?:   string

            page?:   number

            size?:   number

            tags?:   string

        }

        type DataSourceListRespDto = {

            apiType?:   string

            authSystems?:    Array < AuthoritySystemListRespDto >

            code?:   string

            dataType?:   number

            description?:   string

            enable?:   boolean

            filterMode?:   boolean

            name?:   string

            pagination?:   boolean

            tags?:    Array < string >

        }

        type DataSourceParameterDto = {

            dataBase?:  DataSourceDbParameterDto

            httpGet?:   string

            httpPost?:   string

        }

        type DataSourceParseDto = {

            code?:   string

            condition?:  DataSourceConditionDto

        }

        type DataSourceParseRespDto = {

            fields?:    Array < Mapstringstring >

            items?:    Array < DataSourceRowRespDto >

            keys?:    Array < string >

            page?:   number

            size?:   number

            structure?:   number

            total?:   number

        }

        type DataSourceReqDto = {

            created?:   boolean

            isCreated?:   boolean

            isUpdated?:   boolean

            name?:   string

            operator?:   string

            updated?:   boolean

        }

        type DataSourceRowRespDto = {

            row?:    Array < DataSourceKeyRespDto >

        }

        type DeletePersonnelAssociationReqDto = {

            dataSource?:   number

            operator?:   string

            organizationAssociationCode?:   string

            organizationType?:   number

            personnelCode?:   string

        }

        type DeleteRoleReqDto = {

            ascode?:   string

            operator?:   string

            roleCodes?:    Array < string >

        }

        type DeptInfo = {

            code?:   string

            dataSource?:   number

            name?:   string

            treeCodeFullPath?:   string

            treeNameFullPath?:   string

        }

        type DetailDeptRespDto = {

            code?:   string

            createdAt?:   string

            dataSource?:   number

            description?:   string

            enabled?:   boolean

            expands?:    Array < ExpandDto >

            leader?:  Leader

            leafed?:   boolean

            legalSubjectName?:   string

            name?:   string

            treeCodeFullPath?:   string

            treeNameFullPath?:   string

            treeNodeHeight?:   number

            treeNodeSort?:   number

            updatedAt?:   string

        }

        type DetailPostRespDto = {

            code?:   string

            createdAt?:   string

            dataSource?:   number

            description?:   string

            enabled?:   boolean

            expands?:    Array < ExpandDto >

            leafed?:   boolean

            name?:   string

            treeCodeFullPath?:   string

            treeNameFullPath?:   string

            treeNodeSort?:   number

            updatedAt?:   string

        }

        type DetailReqDto = {

            code?:   string

            dataSource?:   number

        }

        type EditApplicationReqDto = {

            appType?:   number

            code?:   string

            description?:   string

            logoUrl?:   string

            name?:   string

            operator?:   string

            personnels?:    Array < ApplicationPersonnelDto >

            status?:   boolean

        }

        type EditAuthoritySystemReqDto = {

            applicationCodes?:    Array < string >

            code?:   string

            description?:   string

            name?:   string

            operator?:   string

            status?:   boolean

        }

        type EntityPermissionNodeRespDto = {

            authoritySystemCode?:   string

            child?:    Array < EntityPermissionNodeRespDto >

            code?:   string

            name?:   string

            permissionType?:   number

        }

        type EntityPermissionTreeRespDto = {

            root?:    Array < EntityPermissionNodeRespDto >

        }

        type EntityRoleBaseReqDto = {

            code?:   string

            expireTime?:   string

            name?:   string

            roleCode?:   string

        }

        type EntityRoleUpdateReqDto = {

            ASCode?:   string

            authorizations?:    Array < EntityRoleBaseReqDto >

            entity?:   string

            operator?:   string

        }

        type ExistOrganizationReqDto = {

            cacheKey?:   string

            code?:   string

            dataSource?:   string

            dataSourceAry?:   string

            email?:   string

            filterVirtual?:   boolean

            idNo?:   string

            phone?:   string

            tenantId?:   string

            worked?:   string

        }

        type ExistOrganizationRespDto = {

            dataSourceAry?:   string

        }

        type ExpandDto = {

            name?:   string

            only?:   boolean

            required?:   boolean

            updated?:   boolean

            value?:   string

        }

        type ExternalDataSourceReqDto = {

            code?:   string

            condition?:  Condition

        }

        type ExternalDataSourceRespDto = {

            fields?:    Array < Field >

            items?:    Array < Row >

            keys?:    Array < string >

            page?:   number

            size?:   number

            structure?:   number

            total?:   number

        }

        type Field = {

            field?:   string

            value?:   any

        }

        type GetDataDictionaryReqDto = {

            name?:   string

            orgType?:   number

        }

        type HasPermissionReqDto = {

            ascode?:   string

            permissionCode?:   string

            personnelCode?:   string

        }

        type HasRoleReqDto = {

            ascode?:   string

            personnelCode?:   string

            roleCode?:   string

        }

        type ISqlSegment = {

            sqlSegment?:   string

        }

        type Leader = {

            code?:   string

            name?:   string

        }

        type ListDataDictionaryReqDto = {

            fieldType?:   number

            name?:   string

            nickname?:   string

            orgType?:   number

            page?:   number

            size?:   number

        }

        type ListDataDictionaryRespDto = {

            description?:   string

            enabled?:   boolean

            fieldType?:   number

            name?:   string

            nickname?:   string

            only?:   boolean

            orgType?:   number

            required?:   boolean

            updated?:   boolean

        }

        type ListDataSourceRespDto = {

            code?:   number

            isCreated?:   boolean

            isUpdated?:   boolean

            name?:   string

        }

        type ListDeptReqDto = {

            code?:   string

            dataSource?:   number

            enabled?:   boolean

            name?:   string

            page?:   number

            size?:   number

            treeCodeFullPath?:   string

        }

        type ListDeptRespDto = {

            code?:   string

            createdAt?:   string

            dataSource?:   number

            description?:   string

            enabled?:   boolean

            leader?:  Leader

            leafed?:   boolean

            legalSubjectName?:   string

            name?:   string

            treeCodeFullPath?:   string

            treeNameFullPath?:   string

            treeNodeHeight?:   number

            updatedAt?:   string

        }

        type ListFranchiseePersonnelReqDto = {

            changeDate?:   string

            page?:   number

            size?:   number

        }

        type ListFranchiseePersonnelRespDto = {

            code?:   string

            email?:   string

            idNo?:   string

            name?:   string

            phone?:   string

            sex?:   number

            worked?:   number

        }

        type ListLeaderReqDto = {

            code?:   string

            dataSource?:   number

            postCode?:   string

            worked?:   string

        }

        type ListLeaderRespDto = {

            code?:   string

            dataSource?:   number

            isMainAssociation?:   boolean

            name?:   string

            postCode?:   string

            postName?:   string

            worked?:   number

        }

        type ListNextDeptReqDto = {

            currentCode?:   string

            dataSource?:   string

            dataSourceAry?:   string

            enabled?:   boolean

        }

        type ListNextPostByCodeReqDto = {

            currentCode?:   string

            dataSource?:   string

            dataSourceAry?:   string

            enabled?:   boolean

        }

        type ListNextPostByCodeRespDto = {

            code?:   string

            complexCode?:   string

            dataSource?:   number

            description?:   string

            enabled?:   boolean

            leafed?:   boolean

            name?:   string

            treeCodeFullPath?:   string

            treeNameFullPath?:   string

            treeNodeHeight?:   number

        }

        type ListPersonnelAndPostReqDto = {

            dataSource?:   number

            personnelCode?:   string

            postCode?:   string

        }

        type ListPersonnelByDeptReqDto = {

            dataSource?:   number

            deptCode?:   string

            fetchChild?:   boolean

            fields?:   string

            page?:   number

            personnelName?:   string

            size?:   number

            worked?:   string

        }

        type ListPersonnelByStoreReqDto = {

            code?:   string

            dataSource?:   number

            erpCode?:   string

            excludePostCodes?:   string

            excludePostNames?:   string

            fields?:   string

            isMainAssociation?:   boolean

            personnelByStoreQuery?:  PersonnelByStoreQuery

            worked?:   string

        }

        type ListPersonnelReqDto = {

            changeDate?:   string

            code?:   string

            codeAry?:    Array < string >

            deptCode?:   string

            fields?:   string

            identityCodes?:    Array < string >

            name?:   string

            nameAry?:    Array < string >

            page?:   number

            personnelQuery?:  PersonnelQuery

            phone?:   string

            postCode?:   string

            size?:   number

            virtual?:   boolean

            worked?:   string

        }

        type ListPersonnelRespDto = {

            avatar?:  Avatar

            birthday?:   string

            code?:   string

            createdAt?:   string

            description?:   string

            email?:   string

            enabled?:   boolean

            idNo?:   string

            name?:   string

            organizations?:    Array < Organization >

            phone?:   string

            realPeopleCode?:   string

            sex?:   number

            updatedAt?:   string

            worked?:   number

        }

        type ListPostByDeptReqDto = {

            code?:   string

            dataSource?:   string

            dataSourceAry?:   string

            enabled?:   boolean

        }

        type ListPostReqDto = {

            code?:   string

            dataSource?:   number

            enabled?:   boolean

            name?:   string

            page?:   number

            size?:   number

        }

        type ListPostRespDto = {

            code?:   string

            createdAt?:   string

            dataSource?:   number

            description?:   string

            enabled?:   boolean

            leafed?:   boolean

            name?:   string

            treeCodeFullPath?:   string

            treeNameFullPath?:   string

            updatedAt?:   string

        }

        type ListRespDtoApplicationListRespDto = {

            items?:    Array < ApplicationListRespDto >

            page?:   number

            size?:   number

            total?:   number

        }

        type ListRespDtoAuthoritySystemListRespDto = {

            items?:    Array < AuthoritySystemListRespDto >

            page?:   number

            size?:   number

            total?:   number

        }

        type ListRespDtoDataSourceListRespDto = {

            items?:    Array < DataSourceListRespDto >

            page?:   number

            size?:   number

            total?:   number

        }

        type ListRespDtoListDataDictionaryRespDto = {

            items?:    Array < ListDataDictionaryRespDto >

            page?:   number

            size?:   number

            total?:   number

        }

        type ListRespDtoListDataSourceRespDto = {

            items?:    Array < ListDataSourceRespDto >

            page?:   number

            size?:   number

            total?:   number

        }

        type ListRespDtoListDeptRespDto = {

            items?:    Array < ListDeptRespDto >

            page?:   number

            size?:   number

            total?:   number

        }

        type ListRespDtoListFranchiseePersonnelRespDto = {

            items?:    Array < ListFranchiseePersonnelRespDto >

            page?:   number

            size?:   number

            total?:   number

        }

        type ListRespDtoListLeaderRespDto = {

            items?:    Array < ListLeaderRespDto >

            page?:   number

            size?:   number

            total?:   number

        }

        type ListRespDtoListNextPostByCodeRespDto = {

            items?:    Array < ListNextPostByCodeRespDto >

            page?:   number

            size?:   number

            total?:   number

        }

        type ListRespDtoListPersonnelRespDto = {

            items?:    Array < ListPersonnelRespDto >

            page?:   number

            size?:   number

            total?:   number

        }

        type ListRespDtoListPostRespDto = {

            items?:    Array < ListPostRespDto >

            page?:   number

            size?:   number

            total?:   number

        }

        type ListRespDtoListStoreAndAreaManagerRespDto = {

            items?:    Array < ListStoreAndAreaManagerRespDto >

            page?:   number

            size?:   number

            total?:   number

        }

        type ListRespDtoListStoreByPersonnelPostRespDto = {

            items?:    Array < ListStoreByPersonnelPostRespDto >

            page?:   number

            size?:   number

            total?:   number

        }

        type ListRespDtoListStoreRespDto = {

            items?:    Array < ListStoreRespDto >

            page?:   number

            size?:   number

            total?:   number

        }

        type ListRespDtoListSubordinateByPersonnelAndPostRespDto = {

            items?:    Array < ListSubordinateByPersonnelAndPostRespDto >

            page?:   number

            size?:   number

            total?:   number

        }

        type ListRespDtoOperationLogListRespDto = {

            items?:    Array < OperationLogListRespDto >

            page?:   number

            size?:   number

            total?:   number

        }

        type ListRespDtoPermissionBaseRespDto = {

            items?:    Array < PermissionBaseRespDto >

            page?:   number

            size?:   number

            total?:   number

        }

        type ListRespDtoPermissionByPersonnelListDto = {

            items?:    Array < PermissionByPersonnelListDto >

            page?:   number

            size?:   number

            total?:   number

        }

        type ListRespDtoPersonnelRoleListRespDto = {

            items?:    Array < PersonnelRoleListRespDto >

            page?:   number

            size?:   number

            total?:   number

        }

        type ListRespDtoRoleListRespDto = {

            items?:    Array < RoleListRespDto >

            page?:   number

            size?:   number

            total?:   number

        }

        type ListRespDtoRolePermissionListRespDto = {

            items?:    Array < RolePermissionListRespDto >

            page?:   number

            size?:   number

            total?:   number

        }

        type ListRespDtoRolePersonnelListRespDto = {

            items?:    Array < RolePersonnelListRespDto >

            page?:   number

            size?:   number

            total?:   number

        }

        type ListStoreAndAreaManagerReqDto = {

            dataSource?:   string

            dataSourceAry?:   string

            enabled?:   boolean

            storeCodeAry?:    Array < string >

        }

        type ListStoreAndAreaManagerRespDto = {

            areaManagerAvatar?:   string

            areaManagerCode?:   string

            areaManagerName?:   string

            areaManagerPhone?:   string

            enabled?:   boolean

            erpCode?:   string

            franchiseeCode?:   string

            franchiseeName?:   string

            franchiseePhone?:   string

            storeCode?:   string

            storeManagerAvatar?:   string

            storeManagerCode?:   string

            storeManagerName?:   string

            storeManagerPhone?:   string

            storeName?:   string

        }

        type ListStoreByPersonnelPostRespDto = {

            code?:   string

            dataSource?:   number

            name?:   string

        }

        type ListStoreReqDto = {

            code?:   string

            dataSource?:   number

            erpCode?:   string

            franchiseeCode?:   string

            name?:   string

            page?:   number

            size?:   number

        }

        type ListStoreRespDto = {

            code?:   string

            dataSource?:   number

            enabled?:   boolean

            erpCode?:   string

            franchiseeCode?:   string

            name?:   string

        }

        type ListSubordinateByPersonnelAndPostRespDto = {

            code?:   string

            name?:   string

        }

        type Mapstringstring = {

        }

        type MergeSegments = {

            groupBy?:    Array < ISqlSegment >

            having?:    Array < ISqlSegment >

            normal?:    Array < ISqlSegment >

            orderBy?:    Array < ISqlSegment >

            sqlSegment?:   string

        }

        type OperationLogListReqDto = {

            end?:   string

            ip?:   string

            objectType?:   number

            operator?:   string

            page?:   number

            size?:   number

            start?:   string

            tenantId?:   string

            type?:   number

        }

        type OperationLogListRespDto = {

            contentAfter?:   string

            contentBefore?:   string

            contentModify?:   string

            createdAt?:   string

            ip?:   string

            objectType?:   number

            operator?:   string

            type?:   number

        }

        type Organization = {

            children?:    Array < Organization >

            code?:   string

            dataSource?:   number

            isMainAssociation?:   boolean

            name?:   string

            treeCodeFullPath?:   string

            treeNameFullPath?:   string

            type?:   number

        }

        type PageRequest = {

            offset?:   number

            pageNumber?:   number

            pageSize?:   number

            paged?:   boolean

            sort?:  Sort

            unpaged?:   boolean

        }

        type Parameter = {

            key?:   string

            value?:   string

        }

        type PermissionBaseReqDto = {

            expireTime?:   string

            permissionCode?:   string

        }

        type PermissionBaseRespDto = {

            authoritySystemCode?:   string

            code?:   string

            description?:   string

            name?:   string

            permissionType?:   number

            treeNameFullPath?:   string

        }

        type PermissionByPersonnelListDto = {

            code?:   string

            expireTime?:   string

            name?:   string

            source?:   string

        }

        type PermissionByPersonnelListReqDto = {

            ascode?:   string

            personnelCode?:   string

        }

        type PermissionDataInfoReqDto = {

            authorizedDataSourceCode?:   string

            conditionExpression?:   string

            permitDataList?:    Array < PermissionPermitDataDto >

            readWriteType?:   string

        }

        type PermissionDeleteReqDto = {

            asCode?:   string

            code?:   string

            operator?:   string

        }

        type PermissionDetailReqDto = {

            asCode?:   string

            code?:   string

        }

        type PermissionDetailRespDto = {

            authDataSource?:  AuthDateSourceRespDto

            authoritySystemCode?:   string

            code?:   string

            conditionExpression?:   string

            description?:   string

            name?:   string

            permissionType?:   number

            permitDataList?:    Array < PermissionPermitDataDto >

            readWriteType?:   string

            treeNameFullPath?:   string

        }

        type PermissionEditReqDto = {

            ASCode?:   string

            code?:   string

            dataInfo?:  PermissionDataInfoReqDto

            description?:   string

            name?:   string

            operator?:   string

            treeNodeSort?:   number

        }

        type PermissionInsertReqDto = {

            ASCode?:   string

            code?:   string

            dataInfo?:  PermissionDataInfoReqDto

            description?:   string

            name?:   string

            operator?:   string

            permissionType?:   number

            treeNodeSort?:   number

            treeParentCode?:   string

        }

        type PermissionListReqDto = {

            ASCode?:   string

            code?:   string

            page?:   number

            size?:   number

        }

        type PermissionPermitDataDto = {

            data?:   string

            dataName?:   string

        }

        type PermissionTreeReqDto = {

            ASCode?:   string

            name?:   string

        }

        type PermissionTreeRespDto = {

            root?:    Array < EntityPermissionNodeRespDto >

        }

        type PermissionsByPersonnelDto = {

            authorizedDataSourceCode?:   string

            code?:   string

            conditionExpression?:   string

            expireTime?:   string

            name?:   string

            permissionType?:   number

            permitData?:    Array < PermissionPermitDataDto >

            readWriteType?:   number

            treeCodeFullPath?:   string

            treeNodeSort?:   number

            treeParentCode?:   string

        }

        type PersonnelByStoreQuery = {

            code?:   string

            dataSource?:   number

            fields?:   string

            tenantId?:   string

            worked?:   string

        }

        type PersonnelDetailReqDto = {

            cacheKey?:   string

            code?:   string

            fieldAry?:    Array < string >

            fields?:   string

            isMainAssociation?:   boolean

            personnelQuery?:  PersonnelQuery

            tenantId?:   string

            worked?:   string

        }

        type PersonnelDetailRespDto = {

            avatar?:  Avatar

            code?:   string

            createdAt?:   string

            description?:   string

            email?:   string

            enabled?:   boolean

            expands?:    Array < ExpandDto >

            hometown?:   string

            idNo?:   string

            name?:   string

            organizations?:    Array < Organization >

            phone?:   string

            realPeopleCode?:   string

            sex?:   number

            updatedAt?:   string

            worked?:   number

        }

        type PersonnelPermissionTreeReqDto = {

            ASCode?:   string

            depCodeList?:    Array < string >

            personnelCode?:   string

            postCodeList?:    Array < string >

        }

        type PersonnelQuery = {

            changeDate?:   string

            code?:   string

            codeAry?:    Array < string >

            codePrecise?:   string

            deptCode?:   string

            email?:   string

            enabled?:   boolean

            endChangeDate?:   string

            fields?:   string

            filterVirtual?:   boolean

            idNo?:   string

            identityCodes?:    Array < string >

            name?:   string

            nameAry?:    Array < string >

            pageRequest?:  PageRequest

            phone?:   string

            phonePrecise?:   string

            postCode?:   string

            startChangeDate?:   string

            tenantId?:   string

            virtual?:   boolean

            worked?:   string

        }

        type PersonnelRoleListReqDto = {

            ascode?:   string

            name?:   string

            page?:   number

            personnelCode?:   string

            size?:   number

        }

        type PersonnelRoleListRespDto = {

            code?:   string

            description?:   string

            name?:   string

        }

        type PersonnelToPermissionReqDto = {

            ASCode?:   string

            operator?:   string

            permissions?:    Array < PermissionBaseReqDto >

            personnelCode?:   string

        }

        type PostInfo = {

            code?:   string

            dataSource?:   number

            mastered?:   boolean

            name?:   string

            treeCodeFullPath?:   string

            treeNameFullPath?:   string

        }

        type ResetPasswordReqDto = {

            code?:   string

            operator?:   string

            sendEmail?:   boolean

        }

        type ResetPasswordRespDto = {

            newPassword?:   string

            status?:   boolean

        }

        type RoleListReqDto = {

            ascode?:   string

            code?:   string

            name?:   string

            page?:   number

            size?:   number

            status?:   boolean

        }

        type RoleListRespDto = {

            ascode?:   string

            code?:   string

            createdAt?:   string

            createdOperator?:   string

            description?:   string

            name?:   string

            operator?:   string

            permissionAmount?:   number

            personnelAmount?:   number

            status?:   boolean

            updatedAt?:   string

            updatedOperator?:   string

        }

        type RoleOfPersonnelListRespDto = {

            code?:   string

            expireTime?:   string

            name?:   string

        }

        type RolePermissionListReqDto = {

            ascode?:   string

            page?:   number

            roleCode?:   string

            size?:   number

        }

        type RolePermissionListRespDto = {

            authorizedDataSourceCode?:   string

            bindAuthData?:   string

            code?:   string

            conditionExpression?:   string

            expireTime?:   string

            name?:   string

            permissionType?:   number

            readWriteType?:   number

            treeNodeSort?:   number

            treeParentCode?:   string

        }

        type RolePermissionTreeReqDto = {

            ASCode?:   string

            roleCode?:   string

        }

        type RolePersonnelListReqDto = {

            ascode?:   string

            entity?:    Array < number >

            name?:   string

            page?:   number

            personnelCode?:   string

            roleCode?:   string

            size?:   number

            tel?:   string

        }

        type RolePersonnelListRespDto = {

            code?:   string

            name?:   string

            path?:   string

            tel?:   string

            type?:   number

        }

        type RoleToPermissionReqDto = {

            ASCode?:   string

            operator?:   string

            permissions?:    Array < PermissionBaseReqDto >

            roleCode?:   string

        }

        type RolesAndPermissionsByPersonnelReqDto = {

            ascode?:   string

            personnelCode?:   string

        }

        type RolesAndPermissionsByPersonnelRespDto = {

            permissions?:    Array < PermissionsByPersonnelDto >

            roles?:    Array < RoleOfPersonnelListRespDto >

        }

        type Row = {

            row?:    Array < Field >

        }

        type Sort = {

            empty?:   boolean

            sorted?:   boolean

            unsorted?:   boolean

        }

        type SuccessRespDto = {

            code?:   string

            status?:   boolean

        }

        type TagSearchReqDto = {

            key?:   string

            tagType?:   "EXTERNALDATASOURCE"

        }

        type TreeStructDto = {

            treeNodeSort?:   number

            treeParentCode?:   string

        }

        type UnionKeyReqDto = {

            code?:   string

            dataSource?:   number

            deptCode?:   string

            operator?:   string

        }

        type UnionKeyRespDto = {

            code?:   string

            dataSource?:   number

        }

        type UpdateDataDictionaryReqDto = {

            description?:   string

            enabled?:   boolean

            fieldType?:   number

            name?:   string

            nickname?:   string

            only?:   boolean

            operator?:   string

            orgType?:   number

            queryWrapper?:  WrapperDataDictionary对象

            required?:   boolean

            updated?:   boolean

        }

        type UpdateDeptReqDto = {

            code?:   string

            dataSource?:   number

            description?:   string

            expands?:    Array < ExpandDto >

            leader?:  Leader

            legalSubjectName?:   string

            name?:   string

            operator?:   string

            treeStruct?:  TreeStructDto

        }

        type UpdatePasswordReqDto = {

            code?:   string

            newPassword?:   string

            oldPassword?:   string

            operator?:   string

        }

        type UpdatePersonnelReqDto = {

            code?:   string

            description?:   string

            email?:   string

            expands?:    Array < ExpandDto >

            hometown?:   string

            idNo?:   string

            name?:   string

            operator?:   string

            phone?:   string

            sex?:   number

            worked?:   number

        }

        type UpdatePostReqDto = {

            code?:   string

            dataSource?:   number

            description?:   string

            expands?:    Array < ExpandDto >

            name?:   string

            operator?:   string

            treeStruct?:  TreeStructDto

        }

        type UserPwdAuthReqDto = {

            password?:   string

            username?:   string

        }

        type WrapperDataDictionary对象 = {

            customSqlSegment?:   string

            emptyOfEntity?:   boolean

            emptyOfNormal?:   boolean

            emptyOfWhere?:   boolean

            entity?:  DataDictionary对象

            expression?:  MergeSegments

            sqlSegment?:   string

            sqlSelect?:   string

            sqlSet?:   string

        }



      /**description
       * 组织架构权限域
       */

        /**
        * add
        * @method
        * @name add
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1ApplicationAdd (

            parameters : {
              'body'  : AddApplicationReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * detail
        * @method
        * @name detail
        * @param  body - code * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1ApplicationDetail (

            parameters : {
              'body'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * edit
        * @method
        * @name edit
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1ApplicationEdit (

            parameters : {
              'body'  : EditApplicationReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listByCondition
        * @method
        * @name listByCondition
        * @param  body - applicationListReqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1ApplicationListByCondition (

            parameters : {
              'body'  : ApplicationListReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * userPassword
        * @method
        * @name userPassword
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1AuthenticateUserPassword (

            parameters : {
              'body'  : UserPwdAuthReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * add
        * @method
        * @name add
        * @param  body - dto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1AuthoritySystemAdd (

            parameters : {
              'body'  : AddAuthoritySystemReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * detail
        * @method
        * @name detail
        * @param  body - code * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1AuthoritySystemDetail (

            parameters : {
              'body'  : string,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * edit
        * @method
        * @name edit
        * @param  body - dto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1AuthoritySystemEdit (

            parameters : {
              'body'  : EditAuthoritySystemReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listByCondition
        * @method
        * @name listByCondition
        * @param  body - dto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1AuthoritySystemListByCondition (

            parameters : {
              'body'  : AuthoritySystemListReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * entityDiscardRole
        * @method
        * @name entityDiscardRole
        * @param  body - entityRoleDeleteReqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1AuthorizationCancelEntityAndRole (

            parameters : {
              'body'  : EntityRoleUpdateReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * entityToRole
        * @method
        * @name entityToRole
        * @param  body - entityRoleInsertReqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1AuthorizationEntityAndRole (

            parameters : {
              'body'  : EntityRoleUpdateReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * hasPermissionByPersonnel
        * @method
        * @name hasPermissionByPersonnel
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1AuthorizationHasPermissionByPersonel (

            parameters : {
              'body'  : HasPermissionReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * hasRoleByPersonnel
        * @method
        * @name hasRoleByPersonnel
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1AuthorizationHasRoleByPersonel (

            parameters : {
              'body'  : HasRoleReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listEntityByRole
        * @method
        * @name listEntityByRole
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1AuthorizationListEntityByRole (

            parameters : {
              'body'  : RolePersonnelListReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listPermissionByPersonnel
        * @method
        * @name listPermissionByPersonnel
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1AuthorizationListPermissionByPersonel (

            parameters : {
              'body'  : PermissionByPersonnelListReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listPermissionByRole
        * @method
        * @name listPermissionByRole
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1AuthorizationListPermissionByRole (

            parameters : {
              'body'  : RolePermissionListReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listPersonPermissionByApp
        * @method
        * @name listPersonPermissionByApp
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1AuthorizationListPersonPermissionByApp (

            parameters : {
              'body'  : ApplicationPersonnelReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listPersonRoleByApp
        * @method
        * @name listPersonRoleByApp
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1AuthorizationListPersonRoleByApp (

            parameters : {
              'body'  : ApplicationPersonnelReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listPersonRoleByCondition
        * @method
        * @name listPersonRoleByCondition
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1AuthorizationListPersonRoleByCondition (

            parameters : {
              'body'  : PersonnelRoleListReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listPersonRolePermissionByApp
        * @method
        * @name listPersonRolePermissionByApp
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1AuthorizationListPersonRolePermissionByApp (

            parameters : {
              'body'  : ApplicationPersonnelReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listRoleAndPermissionByPersonnel
        * @method
        * @name listRoleAndPermissionByPersonnel
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1AuthorizationListRoleAndPermissionByPersonnel (

            parameters : {
              'body'  : RolesAndPermissionsByPersonnelReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * personnelToPermission
        * @method
        * @name personnelToPermission
        * @param  body - personnelToPermissionReqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1AuthorizationPersonnelAndPermission (

            parameters : {
              'body'  : PersonnelToPermissionReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * personnelDiscardPermission
        * @method
        * @name personnelDiscardPermission
        * @param  body - personnelToPermissionReqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1AuthorizationPersonnelDiscardPermission (

            parameters : {
              'body'  : PersonnelToPermissionReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * roleToPermission
        * @method
        * @name roleToPermission
        * @param  body - roleToPermissionReqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1AuthorizationRoleAndPermission (

            parameters : {
              'body'  : RoleToPermissionReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * roleDiscardPermission
        * @method
        * @name roleDiscardPermission
        * @param  body - roleToPermissionReqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1AuthorizationRoleDiscardPermission (

            parameters : {
              'body'  : RoleToPermissionReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * treePersonnelPermission
        * @method
        * @name treePersonnelPermission
        * @param  body - personnelPermissionTreeReqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1AuthorizationTreePersonnelAndAuth (

            parameters : {
              'body'  : PersonnelPermissionTreeReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * treeRolePermission
        * @method
        * @name treeRolePermission
        * @param  body - rolePermissionTreeReqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1AuthorizationTreeRoleAndAuth (

            parameters : {
              'body'  : RolePermissionTreeReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * externalDataSourceParseData
        * @method
        * @name externalDataSourceParseData
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1CommonExternalDataSourceParseData (

            parameters : {
              'body'  : ExternalDataSourceReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listDataSource
        * @method
        * @name listDataSource
        * @param  x-pagoda-envoy-route-project -
        */
        function getApiV1CommonListDataSource (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listDataSource
        * @method
        * @name listDataSource

        */
        function postApiV1CommonListDataSource (





        ): Promise < AxiosResponse >

        /**
        * add
        * @method
        * @name add
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1DataDictionaryAdd (

            parameters : {
              'body'  : AddDataDictionaryReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getByCondition
        * @method
        * @name getByCondition
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1DataDictionaryGetByCondition (

            parameters : {
              'body'  : GetDataDictionaryReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listByCondition
        * @method
        * @name listByCondition
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1DataDictionaryListByCondition (

            parameters : {
              'body'  : ListDataDictionaryReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * update
        * @method
        * @name update
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1DataDictionaryUpdate (

            parameters : {
              'body'  : UpdateDataDictionaryReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * insertDataSource
        * @method
        * @name insertDataSource
        * @param  body - dataSourceInsertReqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1ExternalDataSourceAdd (

            parameters : {
              'body'  : DataSourceInsertReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * debugData
        * @method
        * @name debugData
        * @param  body - dataSourceParseReqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1ExternalDataSourceDebugData (

            parameters : {
              'body'  : DataSourceParseDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * detailDataSource
        * @method
        * @name detailDataSource
        * @param  body - dataSourceDetailReqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1ExternalDataSourceDetail (

            parameters : {
              'body'  : DataSourceDetailReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * editDataSource
        * @method
        * @name editDataSource
        * @param  body - dataSourceEditReqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1ExternalDataSourceEdit (

            parameters : {
              'body'  : DataSourceEditReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listDataSource
        * @method
        * @name listDataSource
        * @param  body - dataSourceListReqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1ExternalDataSourceListByCondition (

            parameters : {
              'body'  : DataSourceListReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * parseData
        * @method
        * @name parseData
        * @param  body - dataSourceParseReqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1ExternalDataSourceParseData (

            parameters : {
              'body'  : DataSourceParseDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * statusDataSource
        * @method
        * @name statusDataSource
        * @param  body - dataSourceEditReqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1ExternalDataSourceStatus (

            parameters : {
              'body'  : DataSourceEditReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * tags
        * @method
        * @name tags
        * @param  body - tagSearchReqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1ExternalDataSourceTags (

            parameters : {
              'body'  : TagSearchReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listByCondition
        * @method
        * @name listByCondition
        * @param  body - dto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1OperationLogListByCondition (

            parameters : {
              'body'  : OperationLogListReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * addDept
        * @method
        * @name addDept
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1OrganizationAddDept (

            parameters : {
              'body'  : AddDeptReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * addOrgAssociation
        * @method
        * @name addOrgAssociation
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1OrganizationAddOrgAssociation (

            parameters : {
              'body'  : AddOrgAssociationReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * addPersonnelAssociation
        * @method
        * @name addPersonnelAssociation
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1OrganizationAddPersonnelAssociation (

            parameters : {
              'body'  : AddPersonnelAssociationReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * addPost
        * @method
        * @name addPost
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1OrganizationAddPost (

            parameters : {
              'body'  : AddPostReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * deleteDept
        * @method
        * @name deleteDept
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1OrganizationDeleteDept (

            parameters : {
              'body'  : UnionKeyReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * deletePersonnelAssociation
        * @method
        * @name deletePersonnelAssociation
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1OrganizationDeletePersonnelAssociation (

            parameters : {
              'body'  : DeletePersonnelAssociationReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * deletePost
        * @method
        * @name deletePost
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1OrganizationDeletePost (

            parameters : {
              'body'  : UnionKeyReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getDeptByCondition
        * @method
        * @name getDeptByCondition
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1OrganizationGetDeptByCondition (

            parameters : {
              'body'  : DetailReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getPostByCondition
        * @method
        * @name getPostByCondition
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1OrganizationGetPostByCondition (

            parameters : {
              'body'  : DetailReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listDeptByCondition
        * @method
        * @name listDeptByCondition
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1OrganizationListDeptByCondition (

            parameters : {
              'body'  : ListDeptReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listNextDeptByCode
        * @method
        * @name listNextDeptByCode
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1OrganizationListNextDeptByCode (

            parameters : {
              'body'  : ListNextDeptReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listNextPostByCode
        * @method
        * @name listNextPostByCode
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1OrganizationListNextPostByCode (

            parameters : {
              'body'  : ListNextPostByCodeReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listPostByCondition
        * @method
        * @name listPostByCondition
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1OrganizationListPostByCondition (

            parameters : {
              'body'  : ListPostReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listPostByDept
        * @method
        * @name listPostByDept
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1OrganizationListPostByDept (

            parameters : {
              'body'  : ListPostByDeptReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listStoreByCondition
        * @method
        * @name listStoreByCondition
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1OrganizationListStoreByCondition (

            parameters : {
              'body'  : ListStoreReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listStoreByPersonnelAndPost
        * @method
        * @name listStoreByPersonnelAndPost
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1OrganizationListStoreByPersonnelAndPost (

            parameters : {
              'body'  : ListPersonnelAndPostReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * updateDept
        * @method
        * @name updateDept
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1OrganizationUpdateDept (

            parameters : {
              'body'  : UpdateDeptReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * updatePost
        * @method
        * @name updatePost
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1OrganizationUpdatePost (

            parameters : {
              'body'  : UpdatePostReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * insertPermission
        * @method
        * @name insertPermission
        * @param  body - permissionInsertReqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1PermissionAdd (

            parameters : {
              'body'  : PermissionInsertReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * deletePermission
        * @method
        * @name deletePermission
        * @param  body - permissionDeleteReqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1PermissionDelete (

            parameters : {
              'body'  : PermissionDeleteReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * detailPermission
        * @method
        * @name detailPermission
        * @param  body - permissionDetailReqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1PermissionDetail (

            parameters : {
              'body'  : PermissionDetailReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * editPermission
        * @method
        * @name editPermission
        * @param  body - permissionEditReqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1PermissionEdit (

            parameters : {
              'body'  : PermissionEditReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listByCondition
        * @method
        * @name listByCondition
        * @param  body - permissionListReqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1PermissionListByCondition (

            parameters : {
              'body'  : PermissionListReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * treeByCondition
        * @method
        * @name treeByCondition
        * @param  body - permissionTreeReqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1PermissionTreeByCondition (

            parameters : {
              'body'  : PermissionTreeReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * addPersonnel
        * @method
        * @name addPersonnel
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1PersonnelAddPersonnel (

            parameters : {
              'body'  : AddPersonnelReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * deletePersonnel
        * @method
        * @name deletePersonnel
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1PersonnelDeletePersonnel (

            parameters : {
              'body'  : UnionKeyReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * existOrganization
        * @method
        * @name existOrganization
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1PersonnelExistOrganization (

            parameters : {
              'body'  : ExistOrganizationReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getLeader
        * @method
        * @name getLeader
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1PersonnelGetLeader (

            parameters : {
              'body'  : ListLeaderReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getPersonnelByUnionKey
        * @method
        * @name getPersonnelByUnionKey
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1PersonnelGetPersonnelByUnionKey (

            parameters : {
              'body'  : PersonnelDetailReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listFranchiseePersonnel
        * @method
        * @name listFranchiseePersonnel
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1PersonnelListFranchiseePersonnel (

            parameters : {
              'body'  : ListFranchiseePersonnelReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listPersonnelByCondition
        * @method
        * @name listPersonnelByCondition
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1PersonnelListPersonnelByCondition (

            parameters : {
              'body'  : ListPersonnelReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listPersonnelByDept
        * @method
        * @name listPersonnelByDept
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1PersonnelListPersonnelByDept (

            parameters : {
              'body'  : ListPersonnelByDeptReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listPersonnelByStore
        * @method
        * @name listPersonnelByStore
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1PersonnelListPersonnelByStore (

            parameters : {
              'body'  : ListPersonnelByStoreReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listStoreAndAreaManagerByStore
        * @method
        * @name listStoreAndAreaManagerByStore
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1PersonnelListStoreAndAreaManagerByStore (

            parameters : {
              'body'  : ListStoreAndAreaManagerReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listSubordinatePersonnelByPersonnel
        * @method
        * @name listSubordinatePersonnelByPersonnel
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1PersonnelListSubordinateByPersonnelAndPost (

            parameters : {
              'body'  : ListPersonnelAndPostReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * resetPassword
        * @method
        * @name resetPassword
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1PersonnelResetPassword (

            parameters : {
              'body'  : ResetPasswordReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * updatePassword
        * @method
        * @name updatePassword
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1PersonnelUpdatePassword (

            parameters : {
              'body'  : UpdatePasswordReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * updatePersonnel
        * @method
        * @name updatePersonnel
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1PersonnelUpdatePersonnel (

            parameters : {
              'body'  : UpdatePersonnelReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * add
        * @method
        * @name add
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1RoleAdd (

            parameters : {
              'body'  : AddRoleReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * batchDelete
        * @method
        * @name batchDelete
        * @param  body - dto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1RoleBatchDelete (

            parameters : {
              'body'  : DeleteRoleReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * detail
        * @method
        * @name detail
        * @param  body - dto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1RoleDetail (

            parameters : {
              'body'  : RoleListRespDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * edit
        * @method
        * @name edit
        * @param  body - dto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1RoleEdit (

            parameters : {
              'body'  : RoleListRespDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listByCondition
        * @method
        * @name listByCondition
        * @param  body - dto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV1RoleListByCondition (

            parameters : {
              'body'  : RoleListReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * existOrganization
        * @method
        * @name existOrganization
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV2PersonnelExistOrganization (

            parameters : {
              'body'  : ExistOrganizationReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getPersonnelByUnionKey
        * @method
        * @name getPersonnelByUnionKey
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV2PersonnelGetPersonnelByUnionKey (

            parameters : {
              'body'  : PersonnelDetailReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listPersonnelByCondition
        * @method
        * @name listPersonnelByCondition
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV2PersonnelListPersonnelByCondition (

            parameters : {
              'body'  : ListPersonnelReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listPersonnelByDept
        * @method
        * @name listPersonnelByDept
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV2PersonnelListPersonnelByDept (

            parameters : {
              'body'  : ListPersonnelByDeptReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * listPersonnelByStore
        * @method
        * @name listPersonnelByStore
        * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
        */
        function postApiV2PersonnelListPersonnelByStore (

            parameters : {
              'body'  : ListPersonnelByStoreReqDto,
              'x-pagoda-envoy-route-project'  ?: any,

            }


        ): Promise < AxiosResponse >

      }
  }
}
export {}

