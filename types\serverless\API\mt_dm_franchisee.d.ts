/*this is generated code */
import axios, { AxiosResponse, AxiosRequestConfig } from "axiosdts";
declare global {
  namespace API {
    /** mt-dm-franchisee
     * 文档链接：http://op.pagoda.com.cn/?#/technical-doc
     * {"version":"1.0.0","title":"1.0.0","contact":{}}
     * 10.8.4.22
     */
    namespace mt_dm_franchisee {
      type AddJoinApplyReqDto = {
        area?: string;

        birth?: string;

        city?: string;

        gender?: number;

        identification?: string;

        name?: string;

        phoneNumber?: string;
      };

      type ApiResultFranchiseeContractInfo对象 = {
        data?: FranchiseeContractInfo对象;

        errorMsg?: string;

        resultCode: number;
      };

      type ApiResultListFranchiseeContractAccessory对象 = {
        data?: Array<FranchiseeContractAccessory对象>;

        errorMsg?: string;

        resultCode: number;
      };

      type ApiResultListFranchiseeContractInfo对象 = {
        data?: Array<FranchiseeContractInfo对象>;

        errorMsg?: string;

        resultCode: number;
      };

      type BatchUpdateRegionsReqDto = {
        code?: string;

        name?: string;

        status?: boolean;
      };

      type BindStoreReqDto = {
        id: number;

        operator: string;

        option: number;

        storeCode: string;
      };

      type DeleteEmployeeInfoReqDto = {
        personNo?: Array<string>;

        storeCode?: string;
      };

      type EmployeeInfoReqDto = {
        personName?: string;

        personNo?: string;

        storeCodes?: Array<string>;

        update?: boolean;
      };

      type FranchiseeContractAccessory对象 = {
        contractAccessoryName?: string;

        contractAccessoryUrl?: string;

        contractCode?: string;

        createPerson?: string;

        createTime?: string;

        id?: number;

        updatePerson?: string;

        updateTime?: string;
      };

      type FranchiseeContractInfo对象 = {
        brand?: number;

        contactName?: string;

        contactNumber?: string;

        contractAmount?: number;

        contractAuditTime?: string;

        contractCode?: string;

        contractCreateTime?: string;

        contractName?: string;

        contractSignTime?: string;

        contractType?: string;

        contractTypeName?: string;

        contractUpdateTime?: string;

        contractUrl?: string;

        createPerson?: string;

        createTime?: string;

        creatorAccount?: string;

        creatorName?: string;

        currencyName?: string;

        endTime?: string;

        franchiseeCode?: string;

        franchiseeIdentity?: string;

        franchiseeMode?: number;

        franchiseeName?: string;

        franchiseeSource?: string;

        id?: number;

        isEvisa?: number;

        primaryContent?: string;

        relatedProject?: string;

        relatedProjectName?: string;

        signingCode?: string;

        signingName?: string;

        startTime?: string;

        status?: number;

        storeAddress?: string;

        storeArea?: number;

        storeType?: number;

        updatePerson?: string;

        updateTime?: string;
      };

      type FranchiseeStoreListReqDto = {
        code?: string;

        page?: number;

        size?: number;

        status?: Array<number>;

        type?: number;
      };

      type ListBatchUpdateRegionsReqDto = {
        regions?: Array<BatchUpdateRegionsReqDto>;
      };

      type ListRegionReqDto = {
        page?: number;

        size?: number;

        status?: boolean;
      };

      type ListRegionRespDto = {
        code?: string;

        createTime?: string;

        name?: string;

        status?: boolean;

        updateTime?: string;
      };

      type ListRespDtoListRegionRespDto = {
        items?: Array<ListRegionRespDto>;

        page?: number;

        size?: number;

        total?: number;
      };

      type ListRespDtoListStoreAccountRespDto = {
        items?: Array<ListStoreAccountRespDto>;

        page?: number;

        size?: number;

        total?: number;
      };

      type ListRespDtoList门店员工信息列表 = {
        items?: Array<array>;

        page?: number;

        size?: number;

        total?: number;
      };

      type ListRespDtoMeetingJoinerRespDto = {
        items?: Array<MeetingJoinerRespDto>;

        page?: number;

        size?: number;

        total?: number;
      };

      type ListRespDtoStoreInfoDTO = {
        items?: Array<StoreInfoDTO>;

        page?: number;

        size?: number;

        total?: number;
      };

      type ListRespDto历史门店信息响应模型 = {
        items?: Array<历史门店信息响应模型>;

        page?: number;

        size?: number;

        total?: number;
      };

      type ListRespDto操作日志列表响应模型 = {
        items?: Array<操作日志列表响应模型>;

        page?: number;

        size?: number;

        total?: number;
      };

      type ListRespDto门店列表响应 = {
        items?: Array<门店列表响应>;

        page?: number;

        size?: number;

        total?: number;
      };

      type ListStoreAccountReqDto = {
        ids?: Array<number>;

        page?: number;

        size?: number;

        storeCodes?: Array<string>;

        types?: string;

        updateTimeEnd?: string;

        updateTimeStart?: string;
      };

      type ListStoreAccountRespDto = {
        accountName?: string;

        accountNumber?: string;

        bankBranchCode?: string;

        bankBranchId?: number;

        bankBranchName?: string;

        bankCityCode?: string;

        bankCityName?: string;

        bankCode?: string;

        bankId?: number;

        bankName?: string;

        bankProvinceCode?: string;

        bankProvinceName?: string;

        createTime?: string;

        employeeCode?: string;

        id?: number;

        storeCode?: string;

        type?: number;

        updatePerson?: string;

        updateTime?: string;

        version?: number;
      };

      type MeetingJoinerReqDto = {
        meetingTypes?: Array<number>;

        subscribeArea?: string;

        subscribeAreaCode?: string;

        subscribeTime?: string;
      };

      type MeetingJoinerRespDto = {
        age?: number;

        createTime: string;

        name: string;

        phone: string;

        sex?: number;

        wxUid?: number;
      };

      type SendSingleUnCommunalMessageReqDto = {
        employeeCodes?: Array<string>;
      };

      type StoreAccountReqDto = {
        accountName?: string;

        accountNumber?: string;

        bankBranchCode?: string;

        bankBranchId?: number;

        bankBranchName?: string;

        bankCityCode?: string;

        bankCityName?: string;

        bankCode?: string;

        bankId?: number;

        bankName?: string;

        bankProvinceCode?: string;

        bankProvinceName?: string;

        checkEmployeeCode?: boolean;

        employeeCode?: string;

        id?: number;

        operatorCode?: string;

        operatorName?: string;

        registerNo?: string;

        storeCode?: string;

        type?: number;

        version?: number;
      };

      type StoreInfoDTO = {
        accountName?: string;

        accountType?: string;

        approvalStatus?: number;

        bankAccountNo: string;

        bankBranchName: string;

        bankLocation: string;

        bankName: string;

        businessRegName: string;

        calculateRent?: string;

        contractAbrogaDate?: string;

        contractCode?: string;

        contractEndDate?: string;

        contractRenewalDate?: string;

        contractStartDate?: string;

        franchiseArea?: string;

        franchiseeCode: string;

        franchiseeIdentity?: string;

        franchiseeName: string;

        franchiseePhone: string;

        id?: number;

        joinModeValue?: string;

        joinTypeValue?: string;

        licenseAddress: string;

        licenseCorporation: string;

        licensePicUrl: string;

        practiceTime?: string;

        regionCode: string;

        regionName: string;

        registerTime: string;

        remark?: string;

        shopCode: string;

        socialCreditCode: string;

        storeAddress: string;

        storeCode?: string;

        storeName: string;

        storeStatus?: number;

        storeTypeValue: string;
      };

      type SuccessRespDto = {
        status?: boolean;
      };

      type 历史门店信息响应模型 = {
        code: string;

        name: string;

        openDate?: string;

        prepareOpenDate?: string;

        status: number;
      };

      type 历史门店信息请求模型 = {
        historyId?: number;

        storeStatus?: string;
      };

      type 员工信息 = {
        certificateStatus?: number;

        employeeType?: number;

        healthCertificateEndTime?: string;

        healthCertificateStartTime?: string;

        operationPerson?: string;

        personNo?: string;

        postType?: number;

        remark?: string;

        storeCode?: string;
      };

      type 导出门店账户信息请求模型 = {
        accountTypes?: Array<number>;

        accountUpdateEnd?: string;

        accountUpdateStart?: string;

        belongFranchisee?: boolean;

        deliveryCenterCode?: string;

        franchiseeCode?: string;

        franchiseeName?: string;

        joinTypeCode?: string;

        openDateEnd?: string;

        openDateStart?: string;

        page?: number;

        regionCodes?: Array<string>;

        size?: number;

        storeCodes?: Array<string>;

        storeIds?: Array<number>;

        storeStatus?: Array<number>;
      };

      type 操作日志列表响应模型 = {
        afterValue?: string;

        beforeValue?: string;

        code?: string;

        createTime?: string;

        fieldName?: string;

        id?: number;

        operatorCode?: string;

        operatorName?: string;

        remark?: string;

        resourceId?: number;

        type?: number;
      };

      type 操作日志列表请求模型 = {
        conditions?: Array<操作日志列表请求模型条件>;

        createTime?: string;

        id?: number;

        logType?: number;

        operateTypes?: string;

        operatorCode?: string;

        operatorName?: string;

        page?: number;

        size?: number;
      };

      type 操作日志列表请求模型条件 = {
        nickName?: string;

        value?: string;
      };

      type 门店信息列表入参 = {
        accountTypes?: Array<number>;

        accountUpdateEnd?: string;

        accountUpdateStart?: string;

        belongFranchisee?: boolean;

        deliveryCenterCode?: string;

        franchiseeCode?: string;

        franchiseeName?: string;

        joinTypeCode?: string;

        openDateEnd?: string;

        openDateStart?: string;

        page?: number;

        regionCodes?: Array<string>;

        size?: number;

        storeCodes?: Array<string>;

        storeIds?: Array<number>;

        storeStatus?: Array<number>;
      };

      type 门店列表响应 = {
        communalAccount?: ListStoreAccountRespDto;

        deliveryCenterCode?: string;

        deliveryCenterName?: string;

        franchiseeCode?: string;

        franchiseeEmployeeCode?: string;

        franchiseeIdentityCode?: string;

        franchiseeName?: string;

        id?: number;

        joinTypeName?: string;

        openDate?: string;

        personnelAccount?: ListStoreAccountRespDto;

        phone?: string;

        regionCode?: string;

        regionName?: string;

        registerNo?: string;

        remark?: string;

        storeCode?: string;

        storeName?: string;

        storeStatus?: number;

        storeStatusName?: string;

        updatePerson?: string;

        updateTime?: string;
      };

      type 门店员工信息列表 = {
        birthday?: string;

        certificateStatus?: number;

        createPerson?: string;

        createTime?: string;

        employeeType?: number;

        gender?: number;

        healthCertificateEndTime?: string;

        healthCertificateStartTime?: string;

        identityCode?: string;

        personName?: string;

        personNo?: string;

        personPostname?: string;

        phone?: string;

        postType?: number;

        remark?: string;

        storeCode?: string;

        systemBring?: number;

        updatePerson?: string;

        updateTime?: string;

        worked?: number;
      };

      /**description
       *
       */

      /**
       * 合同信息自动拉取定时任务
       * @method
       * @name 合同信息自动拉取定时任务
       * @param string date - date * @param  x-pagoda-envoy-route-project -
       */
      function getApiV1ContractFranchiseeContractInfo(parameters: {
        date?: string;
        "x-pagoda-envoy-route-project"?: any;
      }): Promise<AxiosResponse>;

      /**
       * 通过合同编号查询合同附件信息
       * @method
       * @name 通过合同编号查询合同附件信息
       * @param string code - code * @param  x-pagoda-envoy-route-project -
       */
      function getApiV1ContractGetContractAccessoryByCode(parameters: {
        code: string;
        "x-pagoda-envoy-route-project"?: any;
      }): Promise<AxiosResponse>;

      /**
       * 通过合同编号查询合同信息
       * @method
       * @name 通过合同编号查询合同信息
       * @param string code - code * @param  x-pagoda-envoy-route-project -
       */
      function getApiV1ContractGetContractInfoByCode(parameters: {
        code: string;
        "x-pagoda-envoy-route-project"?: any;
      }): Promise<AxiosResponse>;

      /**
       * 通过多个合同编号查询合同信息
       * @method
       * @name 通过多个合同编号查询合同信息
       * @param array code - code * @param  x-pagoda-envoy-route-project -
       */
      function getApiV1ContractGetContractInfosByCode(parameters: {
        code: Array<string>;
        "x-pagoda-envoy-route-project"?: any;
      }): Promise<AxiosResponse>;

      /**
       * bindStore
       * @method
       * @name bindStore
       * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
       */
      function postApiV1FranchiseeAssistantBindStore(parameters: {
        body: BindStoreReqDto;
        "x-pagoda-envoy-route-project"?: any;
      }): Promise<AxiosResponse>;

      /**
       * getMeetingJoiner
       * @method
       * @name getMeetingJoiner
       * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
       */
      function getApiV1FranchiseeApplyMeetingGetMeetingJoiner(parameters: {
        body: MeetingJoinerReqDto;
        "x-pagoda-envoy-route-project"?: any;
      }): Promise<AxiosResponse>;

      /**
       * getMeetingJoiner
       * @method
       * @name getMeetingJoiner
       * @param  body - reqDto
       */
      function headApiV1FranchiseeApplyMeetingGetMeetingJoiner(parameters: {
        body: MeetingJoinerReqDto;
      }): Promise<AxiosResponse>;

      /**
       * getMeetingJoiner
       * @method
       * @name getMeetingJoiner
       * @param  body - reqDto
       */
      function postApiV1FranchiseeApplyMeetingGetMeetingJoiner(parameters: {
        body: MeetingJoinerReqDto;
      }): Promise<AxiosResponse>;

      /**
       * getMeetingJoiner
       * @method
       * @name getMeetingJoiner
       * @param  body - reqDto
       */
      function putApiV1FranchiseeApplyMeetingGetMeetingJoiner(parameters: {
        body: MeetingJoinerReqDto;
      }): Promise<AxiosResponse>;

      /**
       * getMeetingJoiner
       * @method
       * @name getMeetingJoiner
       * @param  body - reqDto
       */
      function deleteApiV1FranchiseeApplyMeetingGetMeetingJoiner(parameters: {
        body: MeetingJoinerReqDto;
      }): Promise<AxiosResponse>;

      /**
       * getMeetingJoiner
       * @method
       * @name getMeetingJoiner
       * @param  body - reqDto
       */
      function optionsApiV1FranchiseeApplyMeetingGetMeetingJoiner(parameters: {
        body: MeetingJoinerReqDto;
      }): Promise<AxiosResponse>;

      /**
       * getMeetingJoiner
       * @method
       * @name getMeetingJoiner
       * @param  body - reqDto
       */
      function patchApiV1FranchiseeApplyMeetingGetMeetingJoiner(parameters: {
        body: MeetingJoinerReqDto;
      }): Promise<AxiosResponse>;

      /**
       * add
       * @method
       * @name add
       * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
       */
      function postApiV1JoinApplyAdd(parameters: {
        body: AddJoinApplyReqDto;
        "x-pagoda-envoy-route-project"?: any;
      }): Promise<AxiosResponse>;

      /**
       * batchUpdateRegions
       * @method
       * @name batchUpdateRegions
       * @param  body - reqDtoList * @param  x-pagoda-envoy-route-project -
       */
      function postApiV1JoinApplyBatchUpdateRegions(parameters: {
        body: ListBatchUpdateRegionsReqDto;
        "x-pagoda-envoy-route-project"?: any;
      }): Promise<AxiosResponse>;

      /**
       * listRegion
       * @method
       * @name listRegion
       * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
       */
      function postApiV1JoinApplyListRegion(parameters: {
        body: ListRegionReqDto;
        "x-pagoda-envoy-route-project"?: any;
      }): Promise<AxiosResponse>;

      /**
       * listByCondition
       * @method
       * @name listByCondition
       * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
       */
      function postApiV1OperateLogListByCondition(parameters: {
        body: 操作日志列表请求模型;
        "x-pagoda-envoy-route-project"?: any;
      }): Promise<AxiosResponse>;

      /**
       * 查询加盟商门店列表
       * @method
       * @name 查询加盟商门店列表
       * @param  body - franchiseeStoreListReqDto * @param  x-pagoda-envoy-route-project -
       */
      function postApiV1StoreGetFranchiseeStoreList(parameters: {
        body: FranchiseeStoreListReqDto;
        "x-pagoda-envoy-route-project"?: any;
      }): Promise<AxiosResponse>;

      /**
       * 查询门店列表
       * @method
       * @name 查询门店列表
       * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
       */
      function postApiV1StoreListByCondition(parameters: {
        body: 门店信息列表入参;
        "x-pagoda-envoy-route-project"?: any;
      }): Promise<AxiosResponse>;

      /**
       * export
       * @method
       * @name export
       * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
       */
      function postApiV1StoreAccountExport(parameters: {
        body: 导出门店账户信息请求模型;
        "x-pagoda-envoy-route-project"?: any;
      }): Promise<AxiosResponse>;

      /**
       * listByCondition
       * @method
       * @name listByCondition
       * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
       */
      function postApiV1StoreAccountListByCondition(parameters: {
        body: ListStoreAccountReqDto;
        "x-pagoda-envoy-route-project"?: any;
      }): Promise<AxiosResponse>;

      /**
       * sendSingleUnCommunalMessage
       * @method
       * @name sendSingleUnCommunalMessage
       * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
       */
      function postApiV1StoreAccountSendSingleUnCommunalMessage(parameters: {
        body: SendSingleUnCommunalMessageReqDto;
        "x-pagoda-envoy-route-project"?: any;
      }): Promise<AxiosResponse>;

      /**
       * sendUnCommunalMessage
       * @method
       * @name sendUnCommunalMessage
       * @param  x-pagoda-envoy-route-project -
       */
      function postApiV1StoreAccountSendUnCommunalMessage(parameters: {
        "x-pagoda-envoy-route-project"?: any;
      }): Promise<AxiosResponse>;

      /**
       * editStoreAccount
       * @method
       * @name editStoreAccount
       * @param  body - storeAccountReqDto * @param  x-pagoda-envoy-route-project -
       */
      function postApiV1StoreAccountUpdateOrCreate(parameters: {
        body: StoreAccountReqDto;
        "x-pagoda-envoy-route-project"?: any;
      }): Promise<AxiosResponse>;

      /**
       * 通过门店代码与员工代码移除门店与员工关系
       * @method
       * @name 通过门店代码与员工代码移除门店与员工关系
       * @param  body - deleteEmployeeInfoReqDto * @param  x-pagoda-envoy-route-project -
       */
      function postApiV1StoreEmployeeCancelStoreAndEmployee(parameters: {
        body: DeleteEmployeeInfoReqDto;
        "x-pagoda-envoy-route-project"?: any;
      }): Promise<AxiosResponse>;

      /**
       * getContactPersonLastSyncTime
       * @method
       * @name getContactPersonLastSyncTime
       * @param  x-pagoda-envoy-route-project -
       */
      function postApiV1StoreEmployeeGetContactPersonLastSyncTime(parameters: {
        "x-pagoda-envoy-route-project"?: any;
      }): Promise<AxiosResponse>;

      /**
       * 通过条件查询门店员工列表
       * @method
       * @name 通过条件查询门店员工列表
       * @param  body - employeeInfoReqDto * @param  x-pagoda-envoy-route-project -
       */
      function postApiV1StoreEmployeeListEmployeeInfoByCondition(parameters: {
        body: EmployeeInfoReqDto;
        "x-pagoda-envoy-route-project"?: any;
      }): Promise<AxiosResponse>;

      /**
       * 保存门店下员工信息与关系
       * @method
       * @name 保存门店下员工信息与关系
       * @param  body - storeEmployeeInfoReqDto * @param  x-pagoda-envoy-route-project -
       */
      function postApiV1StoreEmployeeSaveStoreEmployeeInfo(parameters: {
        body: 员工信息;
        "x-pagoda-envoy-route-project"?: any;
      }): Promise<AxiosResponse>;

      /**
       * listStore
       * @method
       * @name listStore
       * @param  body - reqDto * @param  x-pagoda-envoy-route-project -
       */
      function postApiV1StoreOpenHistoryListStore(parameters: {
        body: 历史门店信息请求模型;
        "x-pagoda-envoy-route-project"?: any;
      }): Promise<AxiosResponse>;
    }
  }
}
export {};
