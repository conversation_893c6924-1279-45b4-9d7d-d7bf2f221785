/*this is generated code */
import  { AxiosResponse } from 'axios'
declare global{
  namespace API {
    /** mt-dm-basedata
      * 文档链接：http://op.pagoda.com.cn/?#/technical-doc
      * {"contact":{},"title":"1.0.0","version":"1.0.0"}
      * dm-basedata.kd1.pagoda.com.cn
    */
    namespace mt_dm_basedata {

        type DelIdInput = {

            id?:   number

            version?:   number

            relVersion?:   number

        }

        type BasCityGroup对象 = {

            code?:   string

            creatorCode?:   string

            lastModifiedAt?:   string

            entId?:   number

            creatorName?:   string

            isSupportVip?:   string

            isSupportImport?:   string

            type?:   number

            parentId?:   number

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            deleted?:   number

            name?:   string

            id?:   number

            modifierName?:   string

            modifierOrgCode?:   string

        }

        type UpdateCostTypeOutput = {

            result?:   string

            resultDetail?:   string

            valueOutputList?:    Array < UpdateCostTypeDetailOutput >

        }

        type QueryLookupTypeOutput = {

            refTypeCode?:   string

            code?:   string

            typeDesc?:   string

            refTypeName?:   string

            isEditableFlag?:   number

            isEnabled?:   number

            valueList?:    Array < BasLookupValueOutput >

            name?:   string

            refTypeId?:   number

            id?:   number

            version?:   number

        }

        type UpdateLookupTypeInput = {

            code?:   string

            typeDesc?:   string

            updateValueList?:    Array < UpdateLookupValueInput >

            createValueList?:    Array < CreateLookupValueInput >

            name?:   string

            refTypeId?:   number

            deleteIdList?:    Array < BasLookupValueInput >

            id?:   number

            version?:   number

        }

        type QueryBankDetailInput = {

            bankCode?:   string

            code?:   string

            entId?:   number

            creatorName?:   string

            pageable?:  Pageable

            bankName?:   string

            lastModifiedAt1?:   string

            deleted?:   number

            lastModifiedAt2?:   string

            createdAt2?:   string

            createdAt1?:   string

            name?:   string

            modifierName?:   string

        }

        type AddCityGroupInput = {

            name?:   string

            id?:   number

        }

        type ProvinceAndCityListOutput = {

            code?:   string

            parentCode?:   string

            name?:   string

            cityList?:    Array < ProvinceAndCityListOutput >

            id?:   number

            type?:   number

        }

        type QueryCostTypeListOutput = {

            code?:   string

            lastModifiedAt?:   string

            typeDesc?:   string

            rootId?:   number

            isEditableFlag?:   number

            pageable?:  Pageable

            version?:   number

            expandIdList?:    Array < number >

            levelNum?:   number

            children?:    Array < QueryCostTypeListOutput >

            isEnabled?:   number

            name?:   string

            id?:   number

            modifierName?:   string

        }

        type BatchUpdateInput = {

            versionIds?:    Array < VersionIdDTO >

        }

        type ProfitSharing对象 = {

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            creatorCode?:   string

            deleted?:   number

            lastModifiedAt?:   string

            entId?:   number

            creatorName?:   string

            id?:   number

            modifierName?:   string

            modifierOrgCode?:   string

            value?:   number

        }

        type QueryCityGroupListInput = {

            name?:   string

            pageable?:  Pageable

            id?:   number

            type?:   string

        }

        type QueryRefTypeListInput = {

            code?:   string

            codeOrName?:   string

            entId?:   number

            name?:   string

            pageable?:  Pageable

            currentTypeId?:   number

        }

        type QueryBankListInput = {

            bankCode?:   string

            entId?:   number

            pageable?:  Pageable

            bankDetailCodeList?:    Array < string >

        }

        type PageQueryLookupTypeListOutput = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < QueryLookupTypeListOutput >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type QueryLookupTypeListInput = {

            code?:   string

            valueValue?:   string

            valueName?:   string

            isEnabled?:   number

            name?:   string

            pageable?:  Pageable

            valueCode?:   string

        }

        type CreateCostTypeDetailInput = {

            code?:   string

            valueDesc?:   string

            isEnabled?:   number

            showOrder?:   number

            id?:   number

            refId?:   number

            refCode?:   string

            refValue?:   string

            value?:   string

            version?:   number

        }

        type BasArea对象 = {

            parentName?:   string

            code?:   string

            depth?:   number

            parentCode?:   string

            name?:   string

            remark?:   string

            cityId?:   number

            id?:   number

        }

        type IterableBasBankHead对象 = {

        }

        type BasBankDetail对象 = {

            bankCode?:   string

            code?:   string

            creatorCode?:   string

            lastModifiedAt?:   string

            entId?:   number

            creatorName?:   string

            bankName?:   string

            remark?:   string

            version?:   number

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            bankId?:   number

            deleted?:   number

            name?:   string

            zjbzyhNm?:   string

            id?:   number

            modifierName?:   string

            modifierOrgCode?:   string

        }

        type QueryBankHeadInput = {

            code?:   string

            deleted?:   number

            lastModifiedAt2?:   string

            createdAt2?:   string

            createdAt1?:   string

            entId?:   number

            creatorName?:   string

            name?:   string

            pageable?:  Pageable

            modifierName?:   string

            lastModifiedAt1?:   string

        }

        type BasPriceTag对象 = {

            code?:   string

            creatorCode?:   string

            lastModifiedAt?:   string

            entId?:   number

            printTemplate?:   string

            creatorName?:   string

            remark?:   string

            type?:   number

            version?:   number

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            deleted?:   number

            isEnabled?:   number

            name?:   string

            priceMaker?:   string

            id?:   number

            modifierName?:   string

            modifierOrgCode?:   string

        }

        type InitCategoryDataInput = {

            msg?:   string

            code?:   number

            content?:    Array < CategoryInput >

        }

        type ViewExchangeRateForWebInput = {

            exchangeDate?:   string

            entId?:   number

            exchangeCurrencyCode:   string

            currencyCode:   string

        }

        type CityConditionQueryOutput = {

            cityName?:   string

            btoCService?:   number

            cityGroupName?:   string

            cityCode?:   string

            storeService?:   string

            cityGroupId?:   number

            cityId?:   number

            deliveryCenterId?:   number

            deliveryCenterName?:   string

        }

        type UpdateCostTypeDetailInput = {

            code?:   string

            valueDesc?:   string

            isEnabled?:   number

            showOrder?:   number

            id?:   number

            refId?:   number

            refCode?:   string

            refValue?:   string

            value?:   string

            version?:   number

        }

        type PageBasPriceTag对象 = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < BasPriceTag对象 >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type QueryBankHeadOutput = {

            createdAt?:   string

            code?:   string

            lastModifiedAt?:   string

            creatorName?:   string

            name?:   string

            remark?:   string

            id?:   number

            modifierName?:   string

            version?:   number

        }

        type DeliveryCenterTree = {

            cityGroupTreeList?:    Array < CityGroupTree >

            name?:   string

            id?:   number

        }

        type PageBasLookupType对象 = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < BasLookupType对象 >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type BasLookupValue对象 = {

            code?:   string

            lastModifiedAt?:   string

            creatorName?:   string

            refValue?:   string

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            parentCode?:   string

            id?:   number

            modifierOrgCode?:   string

            refName?:   string

            sourceFlag?:   number

            value?:   string

            creatorCode?:   string

            entId?:   number

            isShowFlag?:   number

            version?:   number

            parentId?:   number

            deleted?:   number

            valueDesc?:   string

            isEnabled?:   number

            name?:   string

            searchWord?:   string

            showOrder?:   number

            modifierName?:   string

            refId?:   number

            refCode?:   string

        }

        type QueryLookupTypeListOutput = {

            code?:   string

            lastModifiedAt?:   string

            typeDesc?:   string

            rootId?:   number

            isEditableFlag?:   number

            pageable?:  Pageable

            version?:   number

            expandIdList?:    Array < number >

            levelNum?:   number

            children?:    Array < QueryLookupTypeListOutput >

            isEnabled?:   number

            name?:   string

            id?:   number

            modifierName?:   string

        }

        type CityPullDownOutput = {

            code?:   string

            name?:   string

            cityGroupId?:   number

            id?:   number

        }

        type QueryDeliveryCenterByPageOutput = {

            cityGroupNames?:   string

            code?:   string

            lastModifiedAt?:   string

            cityNames?:   string

            name?:   string

            id?:   number

            modifierName?:   string

        }

        type BasCostTypeHead对象 = {

            code?:   string

            creatorCode?:   string

            lastModifiedAt?:   string

            typeDesc?:   string

            entId?:   number

            rootId?:   number

            isEditableFlag?:   number

            creatorName?:   string

            refTypeId?:   number

            isShowFlag?:   number

            version?:   number

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            path?:   string

            deleted?:   number

            levelNum?:   number

            isEnabled?:   number

            name?:   string

            searchWord?:   string

            id?:   number

            modifierName?:   string

            modifierOrgCode?:   string

        }

        type PageBasExchangeRate对象 = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < BasExchangeRate对象 >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type CityPullDownInput = {

            isConnected?:   number

            cityGroupId?:   number

        }

        type LinkCityAndGroupInput = {

            addRelationList?:    Array < CityIdAndGroupId >

            removeRelationList?:    Array < CityIdAndGroupId >

        }

        type BasCity对象_1 = {

            mapCityId?:   string

            xinXiangCheckTime?:   string

            code?:   string

            btoCService?:   number

            isSupportCommunityGroup?:   string

            storeService?:   string

            communityGroupCheckTime?:   string

            lon?:   string

            isSupportVip?:   string

            type?:   number

            deliverySwitch?:   string

            isDefault?:   string

            province?:   string

            cityGroupName?:   string

            createTime?:   string

            parentCode?:   string

            lastUpdate?:   string

            name?:   string

            cityGroupId?:   number

            id?:   number

            isVirtualCity?:   string

            lat?:   string

            status?:   string

        }

        type OperationInput = {

            id?:   number

            version?:   number

        }

        type UpdateCityInput = {

            isDefault?:   string

            xinXiangCheckTime?:   string

            isSupportCommunityGroup?:   string

            communityGroupCheckTime?:   string

            name?:   string

            cityGroupId?:   number

            id?:   number

            isSupportVip?:   string

            deliverySwitch?:   string

            isVirtualCity?:   string

            status?:   string

        }

        type AreaMtInput = {

            areaCode?:   string

            childAreas?:    Array < AreaMtInput >

            areaName?:   string

        }

        type InitAreaDataInput = {

            msg?:   string

            code?:   number

            content?:    Array < AreaMtInput >

        }

        type QueryRefTypeListOutput = {

            code?:   string

            lastModifiedAt?:   string

            typeDesc?:   string

            isEnabled?:   number

            name?:   string

            pageable?:  Pageable

            id?:   number

            modifierName?:   string

            version?:   number

        }

        type PageQueryBankHeadOutput = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < QueryBankHeadOutput >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type PageCityConditionQueryOutput = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < CityConditionQueryOutput >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type BasLookupValueInput = {

            code?:   string

            valueDesc?:   string

            isEnabled?:   number

            name?:   string

            showOrder?:   number

            id?:   number

            refId?:   number

            refName?:   string

            refCode?:   string

            refValue?:   string

            value?:   string

            version?:   number

        }

        type QueryDistributionInput = {

            name?:   string

            pageable?:  Pageable

            id?:   number

        }

        type BasCity对象 = {

            mapCityId?:   string

            xinXiangCheckTime?:   string

            code?:   string

            btoCService?:   number

            isSupportCommunityGroup?:   string

            storeService?:   string

            communityGroupCheckTime?:   string

            lon?:   string

            isSupportVip?:   string

            type?:   number

            deliverySwitch?:   string

            isDefault?:   string

            province?:   string

            createTime?:   string

            parentCode?:   string

            lastUpdate?:   string

            name?:   string

            cityGroupId?:   number

            id?:   number

            isVirtualCity?:   string

            lat?:   string

            status?:   string

        }

        type CityIdAndGroupId = {

            cityGroupId?:   number

            cityId?:   number

        }

        type CreateLookupValueInput = {

            code?:   string

            valueDesc?:   string

            isEnabled?:   number

            name?:   string

            showOrder?:   number

            id?:   number

            refId?:   number

            refName?:   string

            refCode?:   string

            refValue?:   string

            value?:   string

            version?:   number

        }

        type BasBankHead对象 = {

            code?:   string

            creatorCode?:   string

            lastModifiedAt?:   string

            entId?:   number

            creatorName?:   string

            remark?:   string

            version?:   number

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            deleted?:   number

            name?:   string

            id?:   number

            modifierName?:   string

            modifierOrgCode?:   string

        }

        type UpdateCostTypeInput = {

            code?:   string

            typeDesc?:   string

            updateValueList?:    Array < UpdateCostTypeDetailInput >

            createValueList?:    Array < CreateCostTypeDetailInput >

            name?:   string

            refTypeId?:   number

            deleteIdList?:    Array < BasCostTypeDetailInput >

            id?:   number

            version?:   number

        }

        type PageBasCity对象 = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < BasCity对象_1 >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type Pageable = {

            pageNumber?:   number

            pageSize?:   number

            sort?:   string

        }

        type PageQueryBankListOutput = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < QueryBankListOutput >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type ProfitSharingUpdateInput = {

            id?:   number

            value?:   number

        }

        type Sort = {

            unsorted?:   boolean

            sorted?:   boolean

            empty?:   boolean

        }

        type BasUnit对象 = {

            code?:   string

            creatorCode?:   string

            lastModifiedAt?:   string

            entId?:   number

            creatorName?:   string

            isPermitDecimal?:   number

            version?:   number

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            deleted?:   number

            isEnabled?:   number

            name?:   string

            searchWord?:   string

            showOrder?:   number

            id?:   number

            modifierName?:   string

            modifierOrgCode?:   string

        }

        type IdAndVersionInput = {

            id?:   number

            version?:   number

            relVersion?:   number

        }

        type VersionIdDTO = {

            id?:   number

            version?:   number

        }

        type QueryCostTypeOutput = {

            refTypeCode?:   string

            code?:   string

            typeDesc?:   string

            refTypeName?:   string

            isEditableFlag?:   number

            isEnabled?:   number

            valueList?:    Array < BasCostTypeDetailOutput >

            name?:   string

            refTypeId?:   number

            id?:   number

            version?:   number

        }

        type LinkCityGroupAndDeliveryCenterInput = {

            addRelationList?:    Array < CityGroupIdAndDeliveryCenterIdRel >

            removeRelationList?:    Array < CityGroupIdAndDeliveryCenterIdRel >

        }

        type PageQueryRefTypeListOutput = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < QueryRefTypeListOutput >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type BasCostTypeDetailInput = {

            code?:   string

            valueDesc?:   string

            isEnabled?:   number

            showOrder?:   number

            id?:   number

            refId?:   number

            refCode?:   string

            refValue?:   string

            value?:   string

            version?:   number

        }

        type ViewExchangeRateInput = {

            createdAtStart?:   string

            exchangeCurrencyCodeList?:    Array < string >

            dateStart?:   string

            entId?:   number

            creatorName?:   string

            pageable?:  Pageable

            dateEnd?:   string

            currencyCodeList?:    Array < string >

            createdAtEnd?:   string

        }

        type OperationInput_1 = {

            id?:   number

            version?:   number

        }

        type AddDeliveryCenterInput = {

            code?:   string

            name?:   string

            id?:   number

        }

        type UpdateLookupValueOutput = {

            code?:   string

            errMsg?:   string

            name?:   string

            id?:   number

            value?:   string

        }

        type PageQueryCostTypeListOutput = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < QueryCostTypeListOutput >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type QueryCityInput = {

            cityName?:   string

            btoCService?:   number

            isSupportCommunityGroup?:   string

            storeService?:   string

            isConnected?:   number

            cityGroupId?:   number

            pageable?:  Pageable

        }

        type BasAreaMt对象 = {

            creatorCode?:   string

            lastModifiedAt?:   string

            entId?:   number

            creatorName?:   string

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            areaCode?:   string

            deleted?:   number

            areaName?:   string

            id?:   number

            modifierName?:   string

            modifierOrgCode?:   string

            parentAreaCode?:   string

        }

        type PriceTagSaveInput = {

            code?:   string

            printTemplate?:   string

            name?:   string

            priceMaker?:   string

            remark?:   string

            type?:   number

        }

        type PageQueryDeliveryCenterByPageOutput = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < QueryDeliveryCenterByPageOutput >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type QueryValueListOutput = {

            refTypeCode?:   string

            code?:   string

            refTypeName?:   string

            valueDesc?:   string

            name?:   string

            refTypeId?:   number

            showOrder?:   number

            id?:   number

            value?:   string

        }

        type QueryBankListOutput = {

            bankCode?:   string

            bankId?:   number

            bankDetailCode?:   string

            bankDetailId?:   number

            bankDetailName?:   string

            bankName?:   string

        }

        type CreateAndUpdateInput = {

            deleteRelationCityIdList?:    Array < number >

            name?:   string

            createRelationCityIdList?:    Array < number >

            id?:   number

        }

        type PageableDTOBasUnit对象 = {

            pageable?:  Pageable

            dto?:  BasUnit对象

        }

        type UpdateCostTypeDetailOutput = {

            code?:   string

            errMsg?:   string

            name?:   string

            id?:   number

        }

        type BasLookupType对象 = {

            code?:   string

            creatorCode?:   string

            lastModifiedAt?:   string

            typeDesc?:   string

            entId?:   number

            rootId?:   number

            isEditableFlag?:   number

            creatorName?:   string

            refTypeId?:   number

            isShowFlag?:   number

            version?:   number

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            path?:   string

            deleted?:   number

            levelNum?:   number

            isEnabled?:   number

            name?:   string

            searchWord?:   string

            id?:   number

            modifierName?:   string

            modifierOrgCode?:   string

        }

        type TemplateProvinceCityOutput = {

            cityName?:   string

            cityCode?:   string

            provinceCode?:   string

            name?:   string

            cityId?:   number

            id?:   number

            provinceName?:   string

            provinceId?:   number

        }

        type FindPriceTagListInput = {

            code?:   string

            lastModifiedAt?:    Array < string >

            isEnabled?:   number

            name?:   string

            pageable?:  Pageable

            modifierName?:   string

            type?:   number

        }

        type PageQueryPeriodOutput = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < QueryPeriodOutput >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type UpdateLookupValueInput = {

            code?:   string

            valueDesc?:   string

            isEnabled?:   number

            name?:   string

            showOrder?:   number

            id?:   number

            refId?:   number

            refName?:   string

            refCode?:   string

            refValue?:   string

            value?:   string

            version?:   number

        }

        type PageBasUnit对象 = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < BasUnit对象 >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type BasCostTypeDetailOutput = {

            code?:   string

            valueDesc?:   string

            isEnabled?:   number

            showOrder?:   number

            id?:   number

            refCode?:   string

            refValue?:   string

            sourceFlag?:   number

            value?:   string

            version?:   number

        }

        type CreateLookupTypeInput = {

            code?:   string

            typeDesc?:   string

            createValueList?:    Array < CreateLookupValueInput >

            name?:   string

            refTypeId?:   number

        }

        type BasLookupValueOutput = {

            code?:   string

            valueDesc?:   string

            isEnabled?:   number

            name?:   string

            showOrder?:   number

            id?:   number

            refName?:   string

            refCode?:   string

            refValue?:   string

            sourceFlag?:   number

            value?:   string

            version?:   number

        }

        type PageBasBankDetail对象 = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < BasBankDetail对象 >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type QueryPeriodOutput = {

            endDate?:   string

            isYearOpen?:   number

            startDate?:   string

            periodNo?:   string

        }

        type PriceTagUpdateInput = {

            printTemplate?:   string

            name?:   string

            priceMaker?:   string

            remark?:   string

            id?:   number

            version?:   number

        }

        type CategoryInput = {

            childCategories?:    Array < CategoryInput >

            categoryName?:   string

            categoryId?:   number

        }

        type ConditionQueryInput = {

            creatorName?:   string

            name?:   string

            createdAtEnd?:   string

            createdAtBegin?:   string

        }

        type BasExchangeRate对象 = {

            exchangeDate?:   string

            creatorCode?:   string

            lastModifiedAt?:   string

            entId?:   number

            creatorName?:   string

            exchangeCurrencyCode?:   string

            exchangeCurrencyName?:   string

            version?:   number

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            deleted?:   number

            currencyName?:   string

            exchangeRate?:   number

            id?:   number

            modifierName?:   string

            modifierOrgCode?:   string

            currencyCode?:   string

        }

        type PriceTagSaveOutput = {

            id?:   number

        }

        type QueryBankDetailOutput = {

            bankCode?:   string

            code?:   string

            lastModifiedAt?:   string

            creatorName?:   string

            bankName?:   string

            remark?:   string

            version?:   number

            createdAt?:   string

            bankId?:   number

            name?:   string

            zjbzyhNm?:   string

            id?:   number

            modifierName?:   string

        }

        type BasCategory对象 = {

            creatorCode?:   string

            lastModifiedAt?:   string

            entId?:   number

            creatorName?:   string

            categoryName?:   string

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            deleted?:   number

            parentCategoryId?:   number

            id?:   number

            modifierName?:   string

            modifierOrgCode?:   string

            categoryId?:   number

        }

        type PageQueryCityGroupOutput = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < QueryCityGroupOutput >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type DeliveryCenterTreeOutput = {

            deliveryCenterTreeList?:    Array < DeliveryCenterTree >

            unConnectCityList?:    Array < BasCity对象 >

        }

        type UpdateLookupTypeOutput = {

            result?:   string

            resultDetail?:   string

            valueOutputList?:    Array < UpdateLookupValueOutput >

        }

        type PageBasCity对象_1 = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < BasCity对象 >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }

        type CreateCostTypeInput = {

            code?:   string

            typeDesc?:   string

            createValueList?:    Array < CreateCostTypeDetailInput >

            name?:   string

            refTypeId?:   number

        }

        type CityGroupIdAndDeliveryCenterIdRel = {

            cityGroupId?:   number

            deliveryCenterId?:   number

        }

        type ConditionQueryOutput = {

            lastModifiedAt?:   string

            name?:   string

            cityList?:    Array < TemplateProvinceCityOutput >

            id?:   number

            modifierName?:   string

        }

        type QueryDeliveryCenterByPageInput = {

            connectStatus?:   number

            name?:   string

            cityGroupId?:   number

            pageable?:  Pageable

            id?:   number

        }

        type BasCostTypeDetail对象 = {

            code?:   string

            creatorCode?:   string

            lastModifiedAt?:   string

            entId?:   number

            creatorName?:   string

            refValue?:   string

            version?:   number

            parentId?:   number

            creatorOrgCode?:   string

            modifierCode?:   string

            createdAt?:   string

            deleted?:   number

            parentCode?:   string

            valueDesc?:   string

            isEnabled?:   number

            searchWord?:   string

            showOrder?:   number

            id?:   number

            modifierName?:   string

            refId?:   number

            modifierOrgCode?:   string

            refCode?:   string

            sourceFlag?:   number

            value?:   string

        }

        type QueryCityGroupOutput = {

            cityGroupNames?:   string

            createdAt?:   string

            lastModifiedAt?:   string

            cityNamesString?:   string

            name?:   string

            id?:   number

            isSupportVip?:   string

            modifierName?:   string

            isSupportImport?:   string

            parentId?:   number

            deliveryCenterName?:   string

        }

        type QueryCostTypeListInput = {

            code?:   string

            valueValue?:   string

            isEnabled?:   number

            name?:   string

            pageable?:  Pageable

            valueCode?:   string

        }

        type QueryPeriodInput = {

            period?:   string

            chooseDate?:   string

            pageable?:  Pageable

        }

        type QueryRefTypeListInput_1 = {

            code?:   string

            codeOrName?:   string

            name?:   string

            pageable?:  Pageable

            currentTypeId?:   number

        }

        type CityGroupTree = {

            name?:   string

            cityList?:    Array < BasCity对象 >

            id?:   number

        }

        type PageQueryBankDetailOutput = {

            number?:   number

            last?:   boolean

            numberOfElements?:   number

            size?:   number

            totalPages?:   number

            pageable?:  Pageable

            sort?:  Sort

            content?:    Array < QueryBankDetailOutput >

            first?:   boolean

            empty?:   boolean

            totalElements?:   number

        }



      /**description
       *
       */

        /**
        * 根据id查询
        * @method
        * @name 根据id查询
        * @param integer id - id * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasUnitGetById (

            parameters : {
              'id'  : number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 获取配送中心列表（下拉框）
        * @method
        * @name 获取配送中心列表（下拉框）
        * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCityGroupGetDeliveryCenterList (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据数据字典Id查询数据项
        * @method
        * @name 根据数据字典Id查询数据项
        * @param integer id - id * @param integer isEnabled - isEnabled * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasLookupTypeQueryLookupValueByTypeId (

            parameters : {
              'id'  : number,
              'isEnabled'  ?: number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量删除支行信息x
        * @method
        * @name 批量删除支行信息x
        * @param  body - inputs * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasBankDetailServiceBatchDeleteBankDetail (

            parameters : {
              'body'  : Array<DelIdInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 刷新类目
        * @method
        * @name 刷新类目
        * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCategoryServiceRefreshCategory (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 插入一条BasBankHead记录
        * @method
        * @name 插入一条BasBankHead记录
        * @param  body - head * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasBankHeadServiceCreate (

            parameters : {
              'body'  : BasBankHead对象,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 价格牌定义新增
        * @method
        * @name 价格牌定义新增
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasPriceTagCreatePriceTag (

            parameters : {
              'body'  : PriceTagSaveInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getProfitSharingValue
        * @method
        * @name getProfitSharingValue
        * @param integer id - id * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postProfitSharingGetProfitSharingValue (

            parameters : {
              'id'  : number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询所属费用类型列表(分页)
        * @method
        * @name 查询所属费用类型列表(分页)
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCostTypeQueryRefTypeListByPage (

            parameters : {
              'body'  : QueryRefTypeListInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 获取所有一级区划
        * @method
        * @name 获取所有一级区划
        * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasAreaMtGetFirstLevelArea (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 修改
        * @method
        * @name 修改
        * @param  body - inputList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasExchangeRateEdit (

            parameters : {
              'body'  : Array<BasExchangeRate对象>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 更新费用类型记录
        * @method
        * @name 更新费用类型记录
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCostTypeUpdateCostType (

            parameters : {
              'body'  : UpdateCostTypeInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getByName
        * @method
        * @name getByName
        * @param string name - name * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCityGetByName (

            parameters : {
              'name'  : string,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 初始化
        * @method
        * @name 初始化
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCategoryServiceInitData (

            parameters : {
              'body'  : InitCategoryDataInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 新增
        * @method
        * @name 新增
        * @param  body - inputList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasExchangeRateAdd (

            parameters : {
              'body'  : Array<BasExchangeRate对象>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 新增费用类型记录
        * @method
        * @name 新增费用类型记录
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCostTypeCreateCostType (

            parameters : {
              'body'  : CreateCostTypeInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量修改信息
        * @method
        * @name 批量修改信息
        * @param  body - basUnitS * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasUnitBatchEditUnit (

            parameters : {
              'body'  : Array<BasUnit对象>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询数据项列表
        * @method
        * @name 查询数据项列表
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasLookupTypeQueryLookupTypeList (

            parameters : {
              'body'  : QueryLookupTypeListInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 列表查询银行信息
        * @method
        * @name 列表查询银行信息
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasBankHeadServiceGetBankList (

            parameters : {
              'body'  : QueryBankListInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 初始化
        * @method
        * @name 初始化
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasAreaMtInitData (

            parameters : {
              'body'  : InitAreaDataInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * refreshExchangeRateJob
        * @method
        * @name refreshExchangeRateJob
        * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasExchangeRateRefreshExchangeRateJob (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询费用类型列表
        * @method
        * @name 查询费用类型列表
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCostTypeQueryCostTypeList (

            parameters : {
              'body'  : QueryCostTypeListInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 单个删除
        * @method
        * @name 单个删除
        * @param integer id - id * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasUnitDelete (

            parameters : {
              'id'  : number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量删除银行信息
        * @method
        * @name 批量删除银行信息
        * @param  body - inputs * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasBankHeadServiceBatchDeleteBankHead (

            parameters : {
              'body'  : Array<DelIdInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 前端使用
        * @method
        * @name 前端使用
        * @param string code - code * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasLookupTypeQueryLookupValueByTypeCode (

            parameters : {
              'code'  : string,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 获取配送中心下拉树
        * @method
        * @name 获取配送中心下拉树
        * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCityGroupGetDeliveryCenterTree (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量新增x
        * @method
        * @name 批量新增x
        * @param  body - entities * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasBankDetailServiceBatchCreate (

            parameters : {
              'body'  : Array<BasBankDetail对象>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 分页查询支行信息列表
        * @method
        * @name 分页查询支行信息列表
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasBankDetailServiceQueryBankDetailListByPage (

            parameters : {
              'body'  : QueryBankDetailInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 获取全国省份及其城市
        * @method
        * @name 获取全国省份及其城市
        * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCityGetProvinceAndCityList (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询价格牌定义列表
        * @method
        * @name 查询价格牌定义列表
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasPriceTagQueryPriceTagList (

            parameters : {
              'body'  : FindPriceTagListInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getExchangeRateByCurrency
        * @method
        * @name getExchangeRateByCurrency
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasExchangeRateGetExchangeRateByCurrency (

            parameters : {
              'body'  : ViewExchangeRateForWebInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量启用价格牌
        * @method
        * @name 批量启用价格牌
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasPriceTagBatchEnablePriceTag (

            parameters : {
              'body'  : BatchUpdateInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 获取类目下拉树
        * @method
        * @name 获取类目下拉树
        * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCategoryServiceGetCategoryTree (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询所属数据项列表(不分页)
        * @method
        * @name 查询所属数据项列表(不分页)
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasLookupTypeQueryRefTypeList (

            parameters : {
              'body'  : QueryRefTypeListInput_1,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 获取城市组列表（下拉框）
        * @method
        * @name 获取城市组列表（下拉框）
        * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCityGroupGetCityGroupList (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 分页查询
        * @method
        * @name 分页查询
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasExchangeRateViewList (

            parameters : {
              'body'  : ViewExchangeRateInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据父类区划代码查询子区划
        * @method
        * @name 根据父类区划代码查询子区划
        * @param string areaCode - areaCode * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasAreaMtGetChildAreaByParentCode (

            parameters : {
              'areaCode'  : string,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 新增数据项记录
        * @method
        * @name 新增数据项记录
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasLookupTypeCreateLookupType (

            parameters : {
              'body'  : CreateLookupTypeInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 删除配送模板
        * @method
        * @name 删除配送模板
        * @param integer id - id * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postDeliveryTemplateDeleteDeliveryTemplate (

            parameters : {
              'id'  : number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据数据项级联Code查询值
        * @method
        * @name 根据数据项级联Code查询值
        * @param string typeCode - typeCode * @param string valueCode - valueCode * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasLookupTypeQueryLookupValueByTypeAndValueCodes (

            parameters : {
              'typeCode'  : string,
              'valueCode'  : string,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 下拉模糊查询银行支行信息
        * @method
        * @name 下拉模糊查询银行支行信息
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasBankHeadServiceGetDetail (

            parameters : {
              'body'  : QueryBankDetailInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 获取所有省份
        * @method
        * @name 获取所有省份
        * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasAreaGetProvinces (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据市区代码获取县列表
        * @method
        * @name 根据市区代码获取县列表
        * @param string parentCode - parentCode * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasAreaGetDistrictByCode (

            parameters : {
              'parentCode'  : string,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 新增城市组
        * @method
        * @name 新增城市组
        * @param  body - addList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCityGroupAddCityGroup (

            parameters : {
              'body'  : Array<AddCityGroupInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 初始化全国所有城市数据
        * @method
        * @name 初始化全国所有城市数据
        * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCityInitAllCityInfo (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 首页条件查询
        * @method
        * @name 首页条件查询
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postDeliveryTemplateConditionQuery (

            parameters : {
              'body'  : ConditionQueryInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 新增配送中心
        * @method
        * @name 新增配送中心
        * @param  body - addList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCityGroupAddDeliveryCenter (

            parameters : {
              'body'  : Array<AddDeliveryCenterInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 删除配送中心
        * @method
        * @name 删除配送中心
        * @param integer id - id * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCityGroupRemoveDeliveryCenter (

            parameters : {
              'id'  : number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 配送中心分页查询
        * @method
        * @name 配送中心分页查询
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCityGroupQueryDeliveryCenterByPage (

            parameters : {
              'body'  : QueryDeliveryCenterByPageInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 单个数据更新
        * @method
        * @name 单个数据更新
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasUnitEditBasUnit (

            parameters : {
              'body'  : BasUnit对象,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getCityInfoList
        * @method
        * @name getCityInfoList
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCityGetCityInfoList (

            parameters : {
              'body'  : CityPullDownInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 银行信息导入接口
        * @method
        * @name 银行信息导入接口
        * @param file file - file * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasBankHeadServiceBankHeadImport (

            parameters : {
              'file'  ?: any,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 导出支行信息x
        * @method
        * @name 导出支行信息x
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasBankDetailServiceExportBankDetail (

            parameters : {
              'body'  : QueryBankDetailInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据父类区划代码list查询子区划
        * @method
        * @name 根据父类区划代码list查询子区划
        * @param  body - areaCodeList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasAreaMtGetChildAreaByParentCodeList (

            parameters : {
              'body'  : Array<string>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据主键批量查询entity.upperCaseName记录，不分页
        * @method
        * @name 根据主键批量查询entity.upperCaseName记录，不分页
        * @param  body - idList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getBasBankDetailServiceBatchGetByIds (

            parameters : {
              'body'  : Array<number>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 下拉模糊查询银行信息
        * @method
        * @name 下拉模糊查询银行信息
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasBankHeadServiceGetHead (

            parameters : {
              'body'  : QueryBankHeadInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据省份代码获取市区列表
        * @method
        * @name 根据省份代码获取市区列表
        * @param string parentCode - parentCode * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasAreaGetCityByCode (

            parameters : {
              'parentCode'  ?: string,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量新增BasBankHead记录
        * @method
        * @name 批量新增BasBankHead记录
        * @param  body - entities * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasBankHeadServiceBatchCreate (

            parameters : {
              'body'  : Array<BasBankHead对象>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 获取所有一级类目
        * @method
        * @name 获取所有一级类目
        * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCategoryServiceGetFirstLevelCategory (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据类型Id查询费用类型
        * @method
        * @name 根据类型Id查询费用类型
        * @param integer id - id * @param integer isEnabled - isEnabled * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCostTypeQueryCostTypeDetailByTypeId (

            parameters : {
              'id'  : number,
              'isEnabled'  ?: number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 模糊查询
        * @method
        * @name 模糊查询
        * @param  body - input * @param string type - type * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasUnitQueryTitleList (

            parameters : {
              'body'  : PageableDTOBasUnit对象,
              'type'  ?: string,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * getByCode
        * @method
        * @name getByCode
        * @param string code - code * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCityGetByCode (

            parameters : {
              'code'  : string,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 关联城市和城市组
        * @method
        * @name 关联城市和城市组
        * @param  body - inputList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCityGroupLinkCityAndCityGroup (

            parameters : {
              'body'  : LinkCityAndGroupInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据城市组查询已关联城市
        * @method
        * @name 根据城市组查询已关联城市
        * @param integer id - id * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCityGroupQueryConnectedCityByCityGroupId (

            parameters : {
              'id'  : number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 分页查询未关联城市列表
        * @method
        * @name 分页查询未关联城市列表
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCityGetUnConnectCityListByPage (

            parameters : {
              'body'  : QueryDeliveryCenterByPageInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 更新数据项记录
        * @method
        * @name 更新数据项记录
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasLookupTypeUpdateLookupType (

            parameters : {
              'body'  : UpdateLookupTypeInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询所属费用类型列表(不分页)
        * @method
        * @name 查询所属费用类型列表(不分页)
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCostTypeQueryRefTypeList (

            parameters : {
              'body'  : QueryRefTypeListInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询费用类型详情
        * @method
        * @name 查询费用类型详情
        * @param integer id - id * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCostTypeQueryCostType (

            parameters : {
              'id'  : number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据主键查询记录BasBankHead记录
        * @method
        * @name 根据主键查询记录BasBankHead记录
        * @param integer id - id * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasBankHeadServiceGetById (

            parameters : {
              'id'  : number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询数据项详情
        * @method
        * @name 查询数据项详情
        * @param integer id - id * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasLookupTypeQueryLookupType (

            parameters : {
              'id'  : number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据配送中心名称查询
        * @method
        * @name 根据配送中心名称查询
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCityGroupGetCityDeliveryCenterList (

            parameters : {
              'body'  : QueryDistributionInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据主键批量禁用所选费用类型列表
        * @method
        * @name 根据主键批量禁用所选费用类型列表
        * @param  body - operationInputList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCostTypeBatchDisableType (

            parameters : {
              'body'  : Array<OperationInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量停用价格牌
        * @method
        * @name 批量停用价格牌
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasPriceTagBatchDisablePriceTag (

            parameters : {
              'body'  : BatchUpdateInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询配送模板是否关联商品
        * @method
        * @name 查询配送模板是否关联商品
        * @param integer id - id * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postDeliveryTemplateQueryIsConnectWithGoods (

            parameters : {
              'id'  : number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * download
        * @method
        * @name download
        * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getTestExcelDownload (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据主键查询记录BasBankDetail记录
        * @method
        * @name 根据主键查询记录BasBankDetail记录
        * @param integer id - id * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getBasBankDetailServiceGetById (

            parameters : {
              'id'  : number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据id查询
        * @method
        * @name 根据id查询
        * @param integer id - id * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCityGetById (

            parameters : {
              'id'  : number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 关联&取消关联 城市组和配送中心
        * @method
        * @name 关联&取消关联 城市组和配送中心
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCityGroupLinkCityGroupAndDeliveryCenter (

            parameters : {
              'body'  : LinkCityGroupAndDeliveryCenterInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * upload
        * @method
        * @name upload
        * @param file file - file * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postTestExcelUpload (

            parameters : {
              'file'  ?: any,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据城市组id分页查询城市
        * @method
        * @name 根据城市组id分页查询城市
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCityGroupQueryCityByPage (

            parameters : {
              'body'  : QueryDeliveryCenterByPageInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 修改城市信息
        * @method
        * @name 修改城市信息
        * @param  body - basCityList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCityUpdateCityInfo (

            parameters : {
              'body'  : Array<UpdateCityInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询所属数据项列表(分页)
        * @method
        * @name 查询所属数据项列表(分页)
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasLookupTypeQueryRefTypeListByPage (

            parameters : {
              'body'  : QueryRefTypeListInput_1,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量新增
        * @method
        * @name 批量新增
        * @param  body - basUnitS * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasUnitBatchCreateUnit (

            parameters : {
              'body'  : Array<BasUnit对象>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据主键批量查询entity.upperCaseName记录，不分页
        * @method
        * @name 根据主键批量查询entity.upperCaseName记录，不分页
        * @param  body - idList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasBankHeadServiceBatchGetByIds (

            parameters : {
              'body'  : Array<number>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 获取最新美团区划数据
        * @method
        * @name 获取最新美团区划数据
        * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasAreaMtRefreshArea (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据费用类型Code查询子项/只取下一级，非递归
        * @method
        * @name 根据费用类型Code查询子项/只取下一级，非递归
        * @param string code - code * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCostTypeSelectSubCostTypeByCode (

            parameters : {
              'code'  : string,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量删除价格牌
        * @method
        * @name 批量删除价格牌
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasPriceTagBatchDeletePriceTag (

            parameters : {
              'body'  : BatchUpdateInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据主键批量删除费用类型记录
        * @method
        * @name 根据主键批量删除费用类型记录
        * @param  body - operationInputList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCostTypeBatchDeleteCostType (

            parameters : {
              'body'  : Array<OperationInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 获取城市列表
        * @method
        * @name 获取城市列表
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCityGetCityInfoListByPage (

            parameters : {
              'body'  : QueryCityInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 更新一条BasBankHead记录
        * @method
        * @name 更新一条BasBankHead记录
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasBankHeadServiceUpdate (

            parameters : {
              'body'  : BasBankHead对象,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据配送模板id查询模板明细
        * @method
        * @name 根据配送模板id查询模板明细
        * @param integer id - id * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postDeliveryTemplateQueryTemplateDetailById (

            parameters : {
              'id'  : number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 分页查询银行信息列表
        * @method
        * @name 分页查询银行信息列表
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasBankHeadServiceQueryBankHeadListByPage (

            parameters : {
              'body'  : QueryBankHeadInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 删除城市组
        * @method
        * @name 删除城市组
        * @param integer id - id * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCityGroupRemoveCityGroup (

            parameters : {
              'id'  : number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 删除一条BasBankHead记录
        * @method
        * @name 删除一条BasBankHead记录
        * @param integer id - id * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasBankHeadServiceDelete (

            parameters : {
              'id'  : number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量更新BasBankHead记录
        * @method
        * @name 批量更新BasBankHead记录
        * @param  body - entities * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasBankHeadServiceBatchUpdate (

            parameters : {
              'body'  : Array<BasBankHead对象>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据主键批量禁用所选数据项列表
        * @method
        * @name 根据主键批量禁用所选数据项列表
        * @param  body - operationInputList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasLookupTypeBatchDisableType (

            parameters : {
              'body'  : Array<OperationInput_1>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据名称查询支行信息，按照匹配度排序
        * @method
        * @name 根据名称查询支行信息，按照匹配度排序
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasBankDetailServiceSelectBankDetailByNameOrderBySuitability (

            parameters : {
              'body'  : QueryBankDetailInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据城市组名称分页查询
        * @method
        * @name 根据城市组名称分页查询
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCityGroupGetCityGroupListByPage (

            parameters : {
              'body'  : QueryCityGroupListInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 分页查询城市列表
        * @method
        * @name 分页查询城市列表
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCityGetCityListByPage (

            parameters : {
              'body'  : QueryDeliveryCenterByPageInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据配送中心id分页查询城市组
        * @method
        * @name 根据配送中心id分页查询城市组
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCityGroupQueryCityGroupByPage (

            parameters : {
              'body'  : QueryDeliveryCenterByPageInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据主键批量启用所选费用类型列表
        * @method
        * @name 根据主键批量启用所选费用类型列表
        * @param  body - operationInputList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCostTypeBatchEnableType (

            parameters : {
              'body'  : Array<OperationInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据主键批量启用所选数据项列表
        * @method
        * @name 根据主键批量启用所选数据项列表
        * @param  body - operationInputList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasLookupTypeBatchEnableType (

            parameters : {
              'body'  : Array<OperationInput_1>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询价格牌定义查详情
        * @method
        * @name 查询价格牌定义查详情
        * @param integer id - id * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasPriceTagQueryPriceTagDetail (

            parameters : {
              'id'  : number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 删除
        * @method
        * @name 删除
        * @param  body - inputList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasExchangeRateDelete (

            parameters : {
              'body'  : Array<BasExchangeRate对象>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 价格牌定义修改
        * @method
        * @name 价格牌定义修改
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasPriceTagUpdatePriceTag (

            parameters : {
              'body'  : PriceTagUpdateInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量删除
        * @method
        * @name 批量删除
        * @param  body - inputs * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasUnitBatchDelete (

            parameters : {
              'body'  : Array<IdAndVersionInput>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 银行信息导入模板导出接口
        * @method
        * @name 银行信息导入模板导出接口
        * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getBasBankHeadServiceBankHeadImportTemplate (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 校验重复并插入一条BasBankHead记录
        * @method
        * @name 校验重复并插入一条BasBankHead记录
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasBankHeadServiceCheckAndCreate (

            parameters : {
              'body'  : BasBankHead对象,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 导出银行信息
        * @method
        * @name 导出银行信息
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasBankHeadServiceExportBankHead (

            parameters : {
              'body'  : QueryBankHeadInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据父类id查询子类目
        * @method
        * @name 根据父类id查询子类目
        * @param integer parentId - parentId * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCategoryServiceGetChildCategoryByParentId (

            parameters : {
              'parentId'  : number,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询BasPeriod记录
        * @method
        * @name 查询BasPeriod记录
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasPeriodQueryPeriodList (

            parameters : {
              'body'  : QueryPeriodInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 批量更新x
        * @method
        * @name 批量更新x
        * @param  body - entities * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasBankDetailServiceBatchUpdate (

            parameters : {
              'body'  : Array<BasBankDetail对象>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 生成BasPeriod记录
        * @method
        * @name 生成BasPeriod记录
        * @param string year - year * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasPeriodCreatePeriodByYearByYear (

            parameters : {
              'year'  : string,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 关联城市or取消关联
        * @method
        * @name 关联城市or取消关联
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postDeliveryTemplateCreateAndUpdateTemplate (

            parameters : {
              'body'  : CreateAndUpdateInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 电商管理台 首页-条件查询接口
        * @method
        * @name 电商管理台 首页-条件查询接口
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasCityConditionQueryCityInfo (

            parameters : {
              'body'  : QueryCityInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * updateProfitSharingValue
        * @method
        * @name updateProfitSharingValue
        * @param  body - input * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postProfitSharingUpdateProfitSharingValue (

            parameters : {
              'body'  : ProfitSharingUpdateInput,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 支行信息导入模板导出接口
        * @method
        * @name 支行信息导入模板导出接口
        * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function getBasBankHeadServiceBankImportTemplate (

            parameters : {
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 根据主键批量删除数据项记录
        * @method
        * @name 根据主键批量删除数据项记录
        * @param  body - operationInputList * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasLookupTypeBatchDeleteLookupType (

            parameters : {
              'body'  : Array<OperationInput_1>,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

        /**
        * 查询列表
        * @method
        * @name 查询列表
        * @param  body - dto * @param  x-pagoda-envoy-route-project -  * @param  X-PAGODA-USER-ID -
        */
        function postBasUnitSelectUnitByCodeAndName (

            parameters : {
              'body'  : PageableDTOBasUnit对象,
              'x-pagoda-envoy-route-project'  ?: any,
              'X-PAGODA-USER-ID'  ?: any,

            }


        ): Promise < AxiosResponse >

      }
  }
}
export {}

