import domain from 'domain'
import { Request, Response, NextFunction } from 'express'
import Log, { getAppProjectName } from '../util/Log'

export default function domainError(req: Request, res: Response, next: NextFunction) {
  const reqDomain = domain.create()

  reqDomain.on('error', err => {
    const errCode = err.status || err.errCode || err.errorCode || 500
    const errorMsg =
      typeof err.error === 'object' ? err.error.message + ' ' + (err.error.detail || '') : err.error || err.message

    const errorInfo = {
      errCode,
      message: errorMsg,
      trackId: req?.query?.trackId || req?.body?.trackId,
      app_project_name: getAppProjectName(req.originalUrl),
      originalUrl: req.originalUrl || '',
      crid: res.locals.crid || '',
      stack: err.stack || err.error?.stack
    }

    Log.error(errorInfo) // 打印错误日志
    res.status(errCode)
    res.end(errorMsg)
  })

  reqDomain.run(next)
}
