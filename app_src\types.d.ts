declare global {
  interface ServerlessConfig {
    project_name: string
    project_code_dir: string
    //true 表示接入单点登录；不设置或设置为false表示不接入单点登录
    project_caslogin?: boolean
    url: ServerlessConfigUrl
  }
  interface ServerlessConfigUrl {
    [attr: string]: ServerlessConfigUrlModel
  }

  interface ServerlessConfigUrlModel {
    /** false：不接入单点登录 true：接入单点登录 不设置：由所属项目的caslogin决定 */
    caslogin?: boolean
    [attr: string]: any
  }
}

export {}
