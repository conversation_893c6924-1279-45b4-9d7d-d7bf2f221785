/*this is generated code */
import { AxiosResponse } from 'axios'
declare global{
  namespace API {
    /** 服务111
      * 文档链接：http://aaa.com/document
      * {"version":"1.0.0","title":"1.0.0"}
      * dm-member.erptest333.pagoda.com.cn
    */
    namespace service_id111 {
      
        type ApiResult = {
          
            data?:   any  
          
            errorMsg?:   string  
          
            resultCode:   number  
          
        }
      


      /**description
       * 
       */
      
        /**
        * GET /check 服务检查222【asura-dm-member】
        */
        function getCheck (
          


          

        ): Promise < AxiosResponse >
      
      }
  }
}
export {}

