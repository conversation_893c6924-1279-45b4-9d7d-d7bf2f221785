import dotenv from 'dotenv'
dotenv.config()

const config = {
  redis: {
    host: process.env.RD_HOST,
    password: process.env.RD_PASSWORD,
    port: Number(process.env.RD_PORT),
    keyPrefix: process.env.RD_PREFIX,
    communicationKeyPrefix: process.env.RD_COMMUNICATION_PREFIX
  },
  casServer: process.env.CAS_SERVER || 'https://cas.pagoda.com.cn',
  CODE_PATH: process.env.CODE_PATH || './project_code',
  CODE_DIST_PATH: process.env.CODE_DIST_PATH || './dist/project_code',
  cors: process.env.CORS ? process.env.CORS.split(',') : []
}

export default config

const corsDomains = [
  /\.pagoda\.com\.cn$/,
  /\.pagodagyjjh\.com$/,
  /\.yeahkagame\.com$/,
  /\.lskj365\.com$/,
  /\/\/*************$/,
  /\/\/localhost$/,
  /\/\/localhost:\d+$/,
  /\/\/192\.168\.\d+\.\d+:\d+$/
].concat(config.cors.map(value => new RegExp(value)))

console.log('cors:', corsDomains)

export { corsDomains }
