import { Router, Request, Response, NextFunction } from 'express'
import auth from '../../middlewares/auth.middleware'
import { UserService } from './user.service'
import message from '../../util/message'
import * as HttpStatus from 'http-status-codes'
import { IResponseError } from '../../interfaces/IResponseError.interface'

const user: Router = Router()

/**
 * 登录
 *
 * @method GET
 * @URL /login
 */
user.route('/login').get(auth, async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (req.query.redirect as string) {
      res.redirect(decodeURIComponent(req.query.redirect as string))
    } else {
      message.success(res, { message: '登录成功' })
    }
  } catch (error) {
    const err: IResponseError = {
      error,
      errorCode: HttpStatus.BAD_REQUEST
    }
    next(err)
  }
})

/**
 * 退出回调（单点登录系统回调专用）
 *
 * @method POST
 * @URL /login
 */
user.route('/login').post(async (req: Request, res: Response, next: NextFunction) => {
  const loginService = new UserService()
  try {
    await loginService.logoutCallback(req)
    message.success(res, { message: '退出成功' })
  } catch (error) {
    const err: IResponseError = {
      error,
      errorCode: HttpStatus.BAD_REQUEST
    }
    next(err)
  }
})

/**
 * 退出登录
 *
 * @method POST
 * @URL /logout
 */
user.route('/logout').all(auth, async (req: Request, res: Response, next: NextFunction) => {
  const loginService = new UserService()
  try {
    const url = await loginService.logout(req, res)
    if (req.method === 'POST') {
      message.success(res, { message: '退出成功', data: url })
    } else {
      res.redirect(url)
    }
  } catch (error) {
    const err: IResponseError = {
      error,
      errorCode: HttpStatus.BAD_REQUEST
    }
    next(err)
  }
})

export default user
