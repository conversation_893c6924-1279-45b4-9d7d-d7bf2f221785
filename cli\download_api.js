const { writeFileSync, ensureDirSync } = require('fs-extra')
var path = require('path')

var axios = require('axios').default

/**
 * @param {'dev'| 'test'| 'uat'| 'drill' |'prod'} env
 */
async function download(env) {
  console.log(`加载中台api的代码开始:${env}`)
  ensureDirSync(path.resolve(__dirname, `../dist/loaded_middleground_code/`))
  var resp = await axios.get('https://serverless-server-api.pagoda.com.cn/middleground/getCode?env=' + env)
  var resJson = resp.data
  var api_names = []
  if (resJson.errCode == 0) {
    resJson.data.forEach(function(el) {
      api_names.push(el.mgservice)
      writeFileSync(path.resolve(__dirname, `../dist/loaded_middleground_code/${el.mgservice}.js`), el.js_content)
    })
  } else {
    console.error(`加载中台api的js文件失败……:${env}` + resJson.msg)
  }
  console.log(`加载中台api的代码完成:${env}${JSON.stringify(api_names)}`)
}

exports.main = async function main() {
  console.log(`环境变量 API_ENV: ${process.env.API_ENV}`)
  if (process.env.API_ENV) {
    //@ts-ignore
    await download(process.env.API_ENV)
  } else {
    console.log(`环境变量 API_ENV 未设置，不加载中台代码`)
  }
}
