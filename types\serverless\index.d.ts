/// <reference path="session.d.ts" />
/// <reference path="serverless.config.d.ts" />

/// <reference path="API/dm_basedata_sink.d.ts" />
/// <reference path="API/ms_dm_order.d.ts" />
/// <reference path="API/mt_dm_basedata_sink.d.ts" />
/// <reference path="API/mt_dm_basedata.d.ts" />
/// <reference path="API/mt_dm_communi.d.ts" />
/// <reference path="API/mt_dm_customer_open.d.ts" />
/// <reference path="API/mt_dm_customer.d.ts" />
/// <reference path="API/mt_dm_franchisee.d.ts" />
/// <reference path="API/mt_dm_goods.d.ts" />
/// <reference path="API/mt_dm_order_comment.d.ts" />
/// <reference path="API/mt_dm_order_info.d.ts" />
/// <reference path="API/mt_dm_order.d.ts" />
/// <reference path="API/mt_dm_orgauth.d.ts" />
/// <reference path="API/mt_dm_warehouse_sell.d.ts" />
/// <reference path="API/mt_dm_warehouse.d.ts" />
/// <reference path="API/sink_member.d.ts" />
/// <reference path="API/service_id111.d.ts" />
// injected api dts

import { PagodaServerless as PagodaServerlessModel } from "pagoda-serverless"
import * as express from "express"

type XLog = Pick<Console, "log" | "info" | "error" | "debug" | "warn">
type RedisCacheKeyType = string | Buffer
type RedisCacheValueType = string | Buffer | number | any[] | object

declare global {
  //全局放上这么三个变量
  const req: express.Request
  const res: express.Response

  const xlog: XLog
  const getAPI: (env: "dev" | "test" | "uat" | "drill" | "prod") => typeof API
  const middlewareCallback: (flag?: boolean) => any
  const RUNTIME_ENV: string

  type PagodaServerless = PagodaServerlessModel
  const redisCache: {
    get(name: RedisCacheKeyType, projectName?: string): Promise<any>
    set(
      name: RedisCacheKeyType,
      value: RedisCacheValueType | object,
      seconds?: number,
      projectName?: string
    ): Promise<string>
    del(name: RedisCacheKeyType, projectName?: string): Promise<number>
    expire(
      name: RedisCacheKeyType,
      seconds: number,
      projectName?: string
    ): Promise<any>
  }
}
export {}
