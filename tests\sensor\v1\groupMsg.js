import express from 'express';
import cors from 'cors';
// console.log('/gupiao/list')
const app = express();
app.use(
  cors({
    origin: true,
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS,CONNECT,TRACE',
    preflightContinue: false,
    optionsSuccessStatus: 204,
    credentials: true,
    exposedHeaders: '*',
  })
);
app.use(express.static('static'));
const port = process.env.port || 4000;
app.set('trust proxy', 1);
app.listen(port, () => {
  console.log(`Example app listening on port ${port}`);
});

function entry() {
app.get('/sensor/v1/groupSensor', (req, res) => {
  const sa = getSensorsAnalyticsInstance()
  /**
   * 获取神策上报实例
   */
  function getSensorsAnalyticsInstance() {
    const key = 'wxappSaObj'
    if (API[key]) {
      return API[key]
    }
    const saInstance = new SensorsAnalytics()
    saInstance.disableReNameOption()
    API[key] = saInstance
    return saInstance
  }

  // 线上/压测/测试环境采取写日志定时上报，其余环境网络直接发送数据
  if (_.includes(['test_exc', 'drill_exc', 'exc'], RUNTIME_ENV)) {
    sa.initLoggingConsumer('/app/sensors_logs')
  } else {
    sa.submitTo({
      url: req.dominConfig.sersors_url,
      gzip: true,
      timeout: 10000
    })
  }

  const schema = Joi.object({
    encrypted_data: Joi.string().required(),
    iv: Joi.string().required(),
    utm_campaign: Joi.string().required(),
    utm_medium: Joi.string().required(),
    utm_content: Joi.string().allow(''),
    utm_source: Joi.string().required(),
    sf_strategy_name: Joi.string().allow(''),
    union_id: Joi.string().allow(''),
    js_code: Joi.alternatives()
      .try(Joi.number(), Joi.string())
      .required()
  }).unknown()

  $executeMain(main)

  async function main() {
    $validateParamsByJoi({ schema, data: req.body });

    // 以下返回数据上线后删除
    /*
    return {
      data: {
        data: "ok",
        params: JSON.stringify(req.body)
      }
    }
    */
    // 以下注释在接口上线后打开
    const resFail = {
      data: {
        data: 'failed',
        msg: '',
      },
    };
    // 1. 通过js_code获取unionId ok8R8v4dUHPSzbuL6RasgSTOoTt4
    let unionId = '';
    if (req.body.union_id) {
      unionId = req.body.union_id;
    } else {
      const { unionId: unionId2 } = await getUnionId(req.body.js_code);
      if (!unionId2) {
        resFail.data.msg = `通过js_code获取union信息失败, js_code: ${req.body.js_code}`;
        xlog.info(resFail.data.msg);
        return resFail;
      }
      unionId = unionId2;
    }

    const sessionKey = await redisHelper.getRedisVal(
      `customer_session_key${unionId}`
    );

    if (!sessionKey) {
      resFail.data.msg = `通过redis获取键值对失败, redisKey: customer_session_key${unionId}`;
      xlog.info(resFail.data.msg);
      return resFail;
    }

    // 2.解密
    const encryptedData = decodeURIComponent(req.body.encrypted_data);
    const iv = decodeURIComponent(req.body.iv);

    const pc = new WXBizDataCrypt(sessionKey);
    let opengid = '';
    try {
      let data = pc.decryptData(encryptedData, iv);
      opengid = data.opengid;
    } catch (err) {
      return resFail;
    }
    console.log('解密后 data: ', opengid);
    if (!opengid) {
      resFail.data.msg = `解密失败，可能sessionKey已刷新，sessionKey: ${sessionKey}, encryptedData: ${encryptedData}, iv: ${iv}`;
      xlog.info(resFail.data.msg);
      return resFail;
    }

    // 3. 通过opengid调用接口获取到chatid
    const chatId = await getChatId(opengid);
    if (!chatId) {
      resFail.data.msg = `通过opengid调用接口获取到chatid失败，opengid: ${opengid}，检查该发送的群信息是否是系统发送，或是否是测试数据`;
      xlog.info(resFail.data.msg);
      return resFail;
    }

    // 4. 上报到神策
    const {
      utm_campaign,
      utm_source,
      utm_medium,
      utm_content,
      sf_strategy_name = '',
    } = req.body;
    const reportInfo = {
      // 	广告系列名称
      utm_campaign,
      // 广告系列来源
      utm_source,
      // 广告系列媒介
      utm_medium,
      // 广告系列字词
      utm_term: chatId,
      // 预置参数$sf_strategy_unit_id，可以自动将“计划id_策略器id”映射成策略器名称
      $sf_strategy_name: sf_strategy_name || '',
    };
    // 如果有广告系列内容 就上报
    if (utm_content) {
      reportInfo.utm_content = utm_content;
    }
    sensorsReportFn('$MPlaunch', reportInfo);

    return {
      data: {
        data: 'ok',
      },
    };
  }

  /**
   * 通过js_code获取unionId
   * @param {String} js_code js_code
   * @returns {Promise<{unionId: String}>} 返回信息
   */
  async function getUnionId(js_code) {
    const url = `${req.dominConfig.ms_customer}/customer/v1/wechat/sns/jscode/check`;
    const result = await req.$executeHttp.post({
      url,
      data: {
        js_code,
      },
      type: 'eshop',
      isRuturnError: true,
    });
    return result;
  }
  /**
   * 通过opengid获取chatId
   * @param {String} opengid
   * @returns {Promise<{unionId: String}>} 返回信息
   */
  async function getChatId(opengid) {
    const url = `${req.dominConfig.all_rating_manage}/api/cw_calling_interface/opengid_to_chatid`;
    const {
      data: { chat_id },
    } = await req.$executeHttp.post({
      url,
      data: {
        opengid,
      },
      config: {
        headers: {
          token:
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1lIjoiIn0.YSYRZixeW2zC35Dc1W4ILnnrbQaBZ2mo7OvKD6bMbmw',
        },
      },
      isRuturnError: true,
    });
    return chat_id;
  }

  function sensorsReportFn(type, externalParams) {
    const {
      lon = '',
      lat = '',
      locationCity = '',
      locationDistrict = '',
      locationAddress = '',
    } = req.body;
    const basicProperties = {
      $is_login_id: true,
      Terminal: '百果园+小程序',
      lon,
      lat,
      locationCity,
      locationDistrict,
      locationAddress,
    };
    sa.track(
      String(req.body.customerID) || '-1',
      type,
      Object.assign(basicProperties, externalParams)
    );
  }
});
}
entry()
