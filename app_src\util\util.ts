import url from 'url'

export function parseOriginalUrl(originalUrl: string) {
  try {
    var url_data = url.parse(originalUrl)

    var paths = url_data.pathname!.split('/')
    if (paths.length < 4) {
      throw new Error('解析路径信息错误，' + originalUrl)
    }

    var module_file_name = paths.slice(3).join('/')
    var file_name = paths.slice(-1)
    return {
      env: paths[1], //draft_exc
      project_name: paths[2], //pagodabuy
      module_file_name: module_file_name, // aa/bb/22
      file_name: file_name
    }
  } catch (error) {
    throw new Error('解析路径信息错误，' + originalUrl)
  }
}

/**
 *
 * @param fullPath 得是全路径，还必须以js结尾。。。。 D:/xxx/xxx/xxx.js
 */
export default function cleanCache(fullPath: string) {
  console.log(fullPath)
  var module = require.cache[fullPath]
  if (!module) {
    return
  }
  // remove reference in module.parent
  if (module.parent) {
    module.parent.children.splice(module.parent.children.indexOf(module), 1)
  }
  delete require.cache[fullPath]
}
