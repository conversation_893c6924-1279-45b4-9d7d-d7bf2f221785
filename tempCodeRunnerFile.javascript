// 原题：
//        西瓜 20
//        特价-西瓜 15
//        苹果 21
//        兑换卡1-苹果 0+购买价格14
//        兑换卡2-西瓜苹果 5+购买价格0
//        兑换卡3-西瓜 6+购买价格0
// 转换：假设有n个商品，m张券，那么可以先算出券作用在每个商品的优惠金额，然后动态规划求解最大优惠总额，再反推路径，找到每个券需要作用在哪些商品上
// 例如：                              西瓜20 特价西瓜15 苹果21
//      使用兑换卡1，对应到每件商品优惠值为     0         0    7
//      使用兑换卡2，对应到每件商品优惠值为    15       10    16
//      使用兑换卡3，对应到每件商品优惠值为    17        9     0
// 那么首先求最优解 56，然后找到每个数组的索引 [2][1][0]
let cards = [
  [0, 0, 7],
  [15, 10, 16],
  [17, 9, 0]
];

function findPath(cards) {
  let n = cards.length;
  let m = cards[0].length;
  let dp = new Array(n).fill(0).map(() => new Array(m).fill(0));
  let indices = new Array(n).fill(0).map(() => new Array(m).fill(0));

  // 初始化dp数组
  for (let i = 0; i < m; i++) {
    dp[0][i] = cards[0][i];
    indices[0][i] = i;
  }

  for (let i = 1; i < n; i++) {
    for (let j = 0; j < m; j++) {
      for (let k = 0; k < m; k++) {
        if (j !== k) {
          let val = dp[i - 1][k] + cards[i][j];
          if (val > dp[i][j]) {
            dp[i][j] = val;
            indices[i][j] = k;
          }
        }
      }
    }
  }

  let max = 0;
  let maxIndex = 0;
  for (let i = 0; i < m; i++) {
    if (dp[n - 1][i] > max) {
      max = dp[n - 1][i];
      maxIndex = i;
    }
  }

  let paths = new Array(n);
  paths[n - 1] = maxIndex;
  for (let i = n - 2; i >= 0; i--) {
    paths[i] = indices[i + 1][paths[i + 1]];
  }
  return paths;
}

function findMax(cards) {
  let n = cards.length;
  let m = cards[0].length;
  let dp = new Array(n).fill(0).map(() => new Array(m).fill(0));

  // 初始化dp数组
  for (let i = 0; i < m; i++) {
    dp[0][i] = cards[0][i];
  }

  for (let i = 1; i < n; i++) {
    for (let j = 0; j < m; j++) {
      for (let k = 0; k < m; k++) {
        if (j !== k) {
          dp[i][j] = Math.max(dp[i][j], dp[i - 1][k] + cards[i][j]);
        }
      }
    }
  }

  let max = 0;
  for (let i = 0; i < m; i++) {
    max = Math.max(max, dp[n - 1][i]);
  }

  return max;
}

let max = findMax(cards);
console.log("最大值为：" + max);
let paths = findPath(cards);
console.log("选取的索引为：" + paths);