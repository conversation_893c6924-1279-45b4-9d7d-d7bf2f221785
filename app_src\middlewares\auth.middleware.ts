import { Request, Response, NextFunction } from 'express'
import { UserService } from '../business/user/user.service'
import Log from '../util/Log'

export default async function(req: Request, res: Response, next: NextFunction) {
  Log.log(req.originalUrl)

  if (req.query.ticket) {
    // use ticket auth
    const loginService = new UserService()
    await loginService.tickAuth(req)
  }

  const sessionState = hasLoginSession(req)
  if (!sessionState) {
    return needLogin(req, res)
  }

  return next()
}

function needLogin(req: Request, res: Response) {
  const userService = new UserService()
  if (req.headers['x-client-ajax']) {
    res.status(418).json({
      casServer: userService.casServer,
      appServer: `${req.protocol + '://' + req.headers.host}`,
      message: `当前接口需要登录，可以访问${req.protocol + '://' + req.headers.host}/login 触发登录操作`
    })
  } else {
    // go to login page
    return userService.redirectAuth(req, res)
  }
}

function hasLoginSession(req: Request) {
  if (req.session?.userInfo) {
    return true
  } else {
    return false
  }
}
