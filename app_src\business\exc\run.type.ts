import { Request, Response, NextFunction } from 'express'
import { xlog, getAppProjectName } from '../../util/Log'

interface CreateTypeParams {
  req?: Request
  res?: Response
  next?: NextFunction
  env: string
  project: string
  middlewareCallback?: (flag: boolean) => void
  redisCache?: any
}

function createWrapResponse(res?: Response) {
  let warpResponse
  if (res) {
    // hack response
    const endMethod = res.end
    const sendMethod = res.send
    res.end = (chunk, ...args) => {
      if (typeof chunk === 'number') {
        throw new Error('response end Invalid number type :' + chunk)
      }
      //@ts-ignore
      return endMethod.call(res, ...[chunk, ...args])
    }

    res.send = chunk => {
      if (typeof chunk === 'number') {
        throw new Error('response send Invalid number type :' + chunk)
      }

      return sendMethod.call(res, chunk)
    }
    warpResponse = res
  } else {
    warpResponse = null
  }
  return warpResponse
}

function createWrapXlog(xlog: any, trackId?: string, req?: Request, res?: Response) {
  const warpXlog = Object.keys(xlog).reduce((object, key) => {
    object[key] = (...args) => {
      xlog[key](...args, {
        trackId: trackId,
        crid: res?.locals?.crid,
        originalUrl: req?.originalUrl,
        app_project_name: getAppProjectName(req?.originalUrl ?? '')
      })
    }
    return object
  }, {})
  return warpXlog
}
export default function createContext({
  req,
  res,
  next,
  redisCache,
  project,
  env,
  middlewareCallback
}: CreateTypeParams) {
  let trackId = req?.query?.trackId || req?.body?.trackId

  const APIDic = {
    draft_exc: global['API_draft'],
    dev_exc: global['API_dev'],
    test_exc: global['API_test'],
    uat_exc: global['API_uat'],
    drill_exc: global['API_drill'],
    exc: global['API_prod']
  }

  var obj = {
    req: req,
    res: createWrapResponse(res),
    API: {},
    RUNTIME_ENV: env,
    xlog: createWrapXlog(xlog, trackId, req, res),
    redisCache,
    next: next,
    getAPI: function(env: 'dev' | 'test' | 'uat' | 'drill' | 'prod') {
      return global['API_' + env]
    },
    // require:createWrapRequire(require, project, env),
    middlewareCallback
  }

  return [
    obj.req,
    obj.res,
    obj.API,
    obj.RUNTIME_ENV,
    obj.xlog,
    obj.redisCache,
    obj.next,
    obj.getAPI,
    // obj.require,
    obj.middlewareCallback
  ]
}
