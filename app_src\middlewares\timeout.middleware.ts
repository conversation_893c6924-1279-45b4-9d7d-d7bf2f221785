import { Request, Response, NextFunction } from 'express'
import * as HttpStatus from 'http-status-codes'
import { IResponseError } from '../interfaces/IResponseError.interface'

export default function timeout(second: number = 7) {
  const apiTimeout = second * 1000

  return (req: Request, res: Response, next: NextFunction) => {
    // Set the timeout for all HTTP requests
    req.setTimeout(apiTimeout, () => {
      const err: IResponseError = {
        errorCode: HttpStatus.REQUEST_TIMEOUT,
        error: 'Request Timeout'
      }
      next(err)
    })

    // Set the server response timeout for all HTTP requests
    res.setTimeout(apiTimeout, () => {
      const err: IResponseError = {
        errorCode: HttpStatus.SERVICE_UNAVAILABLE,
        error: 'Service Unavailable'
      }
      next(err)
    })
    next()
  }
}
