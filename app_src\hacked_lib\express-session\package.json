{"name": "express-session", "version": "1.17.0", "description": "Simple session middleware for Express", "author": "<PERSON><PERSON> <<EMAIL>> (http://tjholowaychuk.com)", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "repository": "expressjs/session", "license": "MIT", "dependencies": {"cookie": "0.4.0", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "~2.0.0", "on-headers": "~1.0.2", "parseurl": "~1.3.3", "safe-buffer": "5.2.0", "uid-safe": "~2.1.5"}, "devDependencies": {"after": "0.8.2", "cookie-parser": "1.4.4", "eslint": "3.19.0", "eslint-plugin-markdown": "1.0.0", "express": "4.17.1", "mocha": "6.2.1", "nyc": "14.1.1", "supertest": "4.0.2"}, "files": ["session/", "HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.8.0"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md . && node ./scripts/lint-readme.js", "test": "mocha --require test/support/env --check-leaks --bail --no-exit --reporter spec test/", "test-cov": "nyc npm test", "test-travis": "nyc npm test -- --no-exit", "version": "node scripts/version-history.js && git add HISTORY.md"}, "_resolved": "https://registry.npmjs.org/express-session/-/express-session-1.17.0.tgz", "_integrity": "sha512-t4oX2z7uoSqATbMfsxWMbNjAL0T5zpvcJCk3Z9wnPPN7ibddhnmDZXHfEcoBMG2ojKXZoCyPMc5FbtK+G7SoDg==", "_from": "express-session@1.17.0"}