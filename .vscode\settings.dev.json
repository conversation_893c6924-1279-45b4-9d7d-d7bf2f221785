{
  "files.exclude": {
    "**/.git": true,
    "**/.svn": true,
    "**/.hg": true,
    "**/CVS": true,
    "**/.DS_Store": true,
    "logs": true,
  },
  "search.exclude": {
    "**/node_modules": true,
    "**/bower_components": true,
    "**/*.code-search": true,
    "loaded_middleground_code/**": true,
    "dist/**": true,
    "loaded_middleground_code": true,
    "type": true,
    ".env": true,
    ".prettierignore": true,
    "app.tsconfig.json": true,
    "project.tsconfig.json": true,
    "README.md": true
  }
}
