import { Request, Response } from 'express'
import Log from '../../util/Log'
import { promisify } from 'util'
import config from '../../config'
import { get } from 'request'
import { RedisCache } from '../../util/redisCache'
import { redisStore } from '../../middlewares/session.middleware'
import { REDIS_EXPIRE } from '../../util/constants'

type authResult = {
  body: string
}

const getAsync = promisify<string, authResult>(get)

export class UserService {
  casServer: string = config.casServer
  /**
   * 退出登录
   * @param req object
   */
  async logout(req: Request, res: Response) {
    Log.user_operation_log(req, 'logout')
    await this.destroySession(req)
    return await this.destroyAuth(req, res)
  }

  /**
   * 退出单点登录
   * @param req object
   * @param res object
   */
  async destroyAuth(req: Request, res: Response) {
    const redirect = req.query.redirect || req.body.redirect
    const callbackUrl = encodeURIComponent(
      `${req.protocol + '://' + req.headers.host}/login/${redirect ? '?redirect=' + redirect : ''}`
    )

    return `${this.casServer}/cas2/logout?service=${callbackUrl}`
  }

  /**
   * 摧毁session
   * @param req object
   */
  async destroySession(req: Request) {
    return await new Promise((resolve, reject) => {
      req.session?.destroy(error => {
        if (error) {
          Log.error(error)
          reject(error)
        } else {
          resolve(undefined)
        }
      })
    })
  }

  /**
   * 重定向到单点登录页面
   * @param req object
   * @param res object
   */
  async redirectAuth(req: Request, res: Response) {
    var { redirect, service, ...params } = req.query
    // 没有指定 redirect 的时候，默认跳回源地址
    redirect = redirect || `${req.protocol}://${req.headers.host || req.hostname}${req.originalUrl}`
    console.log('redirect:', redirect)
    var paramsString = ''
    for (const key in params) {
      if (params.hasOwnProperty(key)) {
        const value = params[key]
        paramsString += '&' + encodeURIComponent(key) + '=' + encodeURIComponent(value)
      }
    }
    const callbackUrl = encodeURIComponent(
      `${req.protocol + '://' + req.headers.host}/login${redirect ? '?redirect=' + redirect : ''}`
    )
    return res.redirect(`${this.casServer}/cas2/login?service=${callbackUrl}${paramsString}`)
  }

  /**
   * 单点登录系统广播退出回调
   * @param req object
   */
  async logoutCallback(req: Request) {
    if (!req.body.logoutRequest) {
      return Promise.reject(new Error('退出登录：缺少logoutRequest'))
    }

    const redisCache = new RedisCache('ticket')
    // 约定参数
    // 详情：http://wiki.pagoda.com.cn/pages/viewpage.action?pageId=8388700
    const reg = /<samlp:SessionIndex>(.+)<\/samlp:SessionIndex>/
    const ticketId = req.body.logoutRequest.match(reg)[1]
    const sessionID = await redisCache.get(ticketId)

    if (!sessionID) return

    await redisCache.del(ticketId)
    Log.log('退出登录....', sessionID)
    redisStore.destroy(sessionID, error => {
      if (error) {
        Log.error(error)
      }
    })
  }

  /**
   * ticket 换取 用户信息
   * @param req object
   */
  async tickAuth(req: Request) {
    try {
      const ticket = req.query.ticket as string
      console.log('url:', req.url, 'ticket:', ticket, 'protocal:', req.protocol)
      let data
      let body
      try {
        data = await getAsync(
          `${this.casServer}/p3/serviceValidate?ticket=${ticket}&service=${req.protocol +
            '://' +
            req.headers.host}&format=json`
        )
        try {
          body = JSON.parse(data.body)
        } catch (error) {
          Log.error('登录失败：data.body：' + data.body)
        }
      } catch (error) {
        Log.error('登录失败：ticket：' + ticket)
      }

      if (body?.serviceResponse?.authenticationSuccess?.user) {
        // save session and ticket
        const redisCache = new RedisCache('ticket')
        req.session!.userInfo = body.serviceResponse.authenticationSuccess
        // cache 30 day(unit seconds)
        redisCache.set(ticket, req.sessionID!, REDIS_EXPIRE / 1000)

        Log.user_operation_log(req, '登录成功')
      } else {
        Log.error('登录失败：redis：', data)
      }
    } catch (error) {
      Log.error('登录失败：tickAuth：', error)
    }
  }
}
