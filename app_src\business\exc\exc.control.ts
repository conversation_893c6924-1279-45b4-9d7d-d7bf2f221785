import { Router, Request, Response, NextFunction } from 'express'
import auth from '../../middlewares/auth.middleware'
import { ExcService, getCasInfo } from './exc.service'
import * as HttpStatus from 'http-status-codes'
import { IResponseError } from '../../interfaces/IResponseError.interface'
import { CASLOGIN } from '../function/function.entity'
import { ProjectService } from '../project/project.service'

const exc: Router = Router()

const checkAuthOption = authMiddleware => async (req: Request, res: Response, next: NextFunction) => {

  var needAuth = getCasInfo(req.originalUrl)


  if (needAuth) {
    return authMiddleware(req, res, next)
  } else {
    next()
  }
}

exc
  .route(['/draft_exc/*', '/dev_exc/*', '/test_exc/*', '/uat_exc/*', '/drill_exc/*', '/exc/*'])
  .all(checkAuthOption(auth), async (req: Request, res: Response, next: NextFunction) => {
    const excService = new ExcService()
    try {
      await excService.exc(req, res, next)
    } catch (error) {
      const err: IResponseError = {
        error,
        errorCode: HttpStatus.BAD_REQUEST
      }
      next(err)
    }
  })

export default exc
